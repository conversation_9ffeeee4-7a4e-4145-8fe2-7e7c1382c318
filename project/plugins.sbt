addSbtPlugin("com.github.sbt" % "sbt-native-packager" % "1.11.3")
addSbtPlugin("org.scalameta" % "sbt-scalafmt" % "2.5.5")
addSbtPlugin("org.scoverage" % "sbt-scoverage" % "2.3.1")
addSbtPlugin("au.com.onegeek" %% "sbt-dotenv" % "2.1.233")
addSbtPlugin("org.typelevel" % "sbt-fs2-grpc" % "2.7.13")

// Scapegoat is a Scala static code analyzer.
// see also: https://github.com/sksamuel/scapegoat
// see also: https://github.com/sksamuel/sbt-scapegoat
addSbtPlugin("com.sksamuel.scapegoat" %% "sbt-scapegoat" % "1.2.13")

// SonarQube Scanner for SBT.
// see also: https://github.com/olaq/sbt-sonar-scanner-plugin
addSbtPlugin("com.sonar-scala" % "sbt-sonar"   % "2.3.0")

addCompilerPlugin("com.olegpy" %% "better-monadic-for" % "0.3.1")

addDependencyTreePlugin

// Display your sbt project's dependency updates.
// see also: https://github.com/rtimush/sbt-updates
addSbtPlugin("com.timushev.sbt" % "sbt-updates" % "0.6.4")
