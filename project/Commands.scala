import sbt._

object Commands {
  val additionalCommands: Seq[Command] = Seq(prepareCommit, coverageTest)

  def prepareCommit: Command = Command.command("prepare-commit") { state =>
    "clean" ::
    "all scalafmtAll" ::
    "all compile test:compile" ::
    "scapegoat" ::
    "coverage" ::
    "test" ::
    "coverageReport" ::
    "public-api/sonarScan" ::
    "admin/sonarScan" ::
    "shared/sonarScan" ::
    "dependencyUpdates" ::
    state
  }

  def coverageTest: Command = Command.command("coverageTest") { state =>
    "compile" ::
    "test:compile" ::
    "coverage" ::
    "test" ::
    "coverageReport" :: state
  }
}
