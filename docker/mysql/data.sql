CREATE TABLE IF NOT EXISTS defaults_entry
(
    recruiter_id varchar(18)  NOT NULL,
    version      varchar(255) NOT NULL,
    defaults     JSON         NOT NULL,

    PRIMARY KEY (recruiter_id)
) charset = utf8;

create table api_key_entry
(
    id           varchar(36)    not null
        primary key,
    recruiter_id varchar(18)    not null,
    api_key      varbinary(128) not null,
    integration  varchar(255)   not null,
    created_at   timestamp      null,
    constraint id
        unique (id)
)
    charset = utf8;

create index api_key
    on api_key_entry (api_key);

create index recruiter_id
    on api_key_entry (recruiter_id);

CREATE TABLE IF NOT EXISTS usage_log_entry
(
    request_id varchar(36)  NOT NULL,
    `timestamp` varchar(27) NOT NULL,
    request  LONGTEXT NOT NULL,
    response LONGTEXT NOT NULL,
    recruiter_id varchar(18),

    PRIMARY KEY (request_id),
    INDEX (`timestamp`, recruiter_id)
) charset = utf8;