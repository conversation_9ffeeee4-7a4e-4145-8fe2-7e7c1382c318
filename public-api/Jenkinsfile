pipeline {
  agent {
      node {
        label 'alpakka_java21'
      }
  }
    parameters {
        string(name: 'MODULE_NAME', defaultValue: '',  description: 'Buildnumber to use')
        string(name: 'MODULE_BUILD_NUMBER', defaultValue: '', description: 'Buildnumber to use')
    }
  environment {
      String SLACK_CHANNEL = '#dpgr_alpakka_notifications'
      String SLACK_MESSAGE = "@here Build #${MODULE_BUILD_NUMBER} for ${env.JOB_NAME} (<${env.BUILD_URL}|open>) has failed"
      String SLACK_TEAM_DOMAIN = 'dpos'
    }
  options {
    ansiColor('xterm')
    buildDiscarder(logRotator(numToKeepStr: '25'))
    disableConcurrentBuilds()
    disableResume()
  }
  stages {
    stage('Compile') {
        steps {
            ansiColor('xterm') {
                sh "sbt -Dversion=${MODULE_BUILD_NUMBER} 'project ${MODULE_NAME}' clean scalafmtCheckAll"
            }
        }
    }
    stage('Test') {
      stages {
          stage('unit & bdd') {
              steps {
                  ansiColor('xterm') {
                      sh "sbt -Dversion=${MODULE_BUILD_NUMBER} 'project ${MODULE_NAME}' coverage test coverageReport sonarScan"
                  }
              }
          }
      }
    }
    stage('Quality') {
      steps {
        ansiColor('xterm') {
          sh "sbt -Drevision=${MODULE_BUILD_NUMBER} 'project ${MODULE_NAME}' scapegoat"
        }
      }
    }
    stage('Publish') {
      steps {
        ansiColor('xterm') {
          sh "aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 674201978047.dkr.ecr.eu-west-1.amazonaws.com"
          sh "DOCKER_BUILDKIT=1 sbt -Dversion=${MODULE_BUILD_NUMBER} ${MODULE_NAME}/docker:publish"
        }
      }
    }
    stage('Deploy - ACC') {
      steps {
        ansiColor('xterm') {
          sh "sbt 'project ${MODULE_NAME}' \"deploy acc ${MODULE_BUILD_NUMBER}\""
        }
      }
    }
  }
  post {
    unsuccessful {
     withCredentials([string(credentialsId: 'SLACK_TOKEN', variable: 'SLACK_TOKEN')]) {
      slack(SLACK_CHANNEL, '#c11238', SLACK_MESSAGE, SLACK_TEAM_DOMAIN, SLACK_TOKEN)
     }
    }
  }
}

def slack(String channel, String color, String message, String teamDomain, String token) {
  slackSend channel: channel, color: color, message: message, teamDomain: teamDomain, token: token
}
