kind: ConfigMap
apiVersion: v1
metadata:
  name: job-poster-api-public-api-config
  namespace: default
  labels:
    environment: acceptance
data:
  LOGGING_LEVEL: "INFO"
  LOGGING_APPENDER: "JSON"
  DATABASE_CONNECTION_STRING: "*********************************************************************************************************************************************"
  DASHBOARD_URL: "https://b2b-acc.persgroep.digital/dashboard"
  APPLICATION_SERVICE_HOST: "api-internal-acc.persgroep.digital"
  REPORTING_SERVICE_HOST: "http://reporting-service.default.svc.cluster.local"
  ADDRESS_SERVICE_HOST: "http://address-service"
  B2B_SALESFORCE_GATEWAY_SERVICE_HOST: "b2b-salesforce-gateway"
  B2B_JOB_MANAGER_SERVICE_HOST: "b2b-job-manager"
