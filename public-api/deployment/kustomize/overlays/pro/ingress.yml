kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: "job-poster-api"
  namespace: "default"
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "2m"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload";
      more_set_headers "x-frame-options: SAMEORIGIN";
    nginx.org/limit-req-rate: "10r/s"
    nginx.org/limit-req-burst: "20"
    nginx.org/limit-req-key: "${binary_remote_addr}"
    nginx.org/limit-req-no-delay: "false"
    nginx.org/limit-req-reject-code: "429"
    nginx.org/limit-req-log-level: "warn"
spec:
  ingressClassName: nginx-external
  rules:
    - host: "job-poster-api.persgroep.digital"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: "job-poster-api-public-api"
                port:
                  number: 11360
