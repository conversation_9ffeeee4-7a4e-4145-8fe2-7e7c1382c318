apiVersion: apps/v1
kind: Deployment
metadata:
  name: job-poster-api-public-api
  annotations:
    jenkins/build-number: "{{BUILD_NUMBER}}"
spec:
  selector:
    matchLabels:
      app: job-poster-api-public-api
  replicas: 3
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 320
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: job-poster-api-public-api
    spec:
      containers:
        - name: job-poster-api-public-api
          image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/job-poster-api-public-api:{{BUILD_NUMBER}}
          resources:
            limits:
              memory: 2000Mi
            requests:
              cpu: 200m
              memory: 1000Mi
          ports:
            - name: app-port
              containerPort: 11360
          readinessProbe:
            timeoutSeconds: 5
            httpGet:
              path: "/status"
              port: app-port
          livenessProbe:
            timeoutSeconds: 5
            httpGet:
              path: "/status"
              port: app-port
          startupProbe:
            timeoutSeconds: 5
            initialDelaySeconds: 5
            periodSeconds: 1
            failureThreshold: 30
            httpGet:
              path: "/status"
              port: app-port
          envFrom:
            - configMapRef:
                name: job-poster-api-public-api-config
          env:
            - name: "JAVA_OPTS"
              value: "-XX:+UseContainerSupport -XX:+UseG1GC -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:+UseStringDeduplication -XX:MaxRAMPercentage=75.0 -XX:InitialRAMPercentage=75.0"
            - name: DATABASE_USER
              valueFrom:
                secretKeyRef:
                  name: job-poster-api
                  key: RDS_USER
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: job-poster-api
                  key: RDS_PASSWORD
            - name: API_KEY_SALT
              valueFrom:
                secretKeyRef:
                  name: job-poster-api
                  key: API_KEY_SALT
            - name: OCCUPATION_PREDICTION_SERVICE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: job-poster-api
                  key: OCCUPATION_PREDICTION_SERVICE_API_KEY
            - name: OCCUPATION_PREDICTION_SERVICE_HOST
              value: "api.isco-classification.dpos.persgroep.cloud"
            - name: VACANCY_ENRICHER_API_KEY
              valueFrom:
                secretKeyRef:
                  name: job-poster-api
                  key: VACANCY_ENRICHER_API_KEY
            - name: VACANCY_ENRICHER_HOST
              value: "api.vacancy-enricher.dpos.persgroep.cloud"
            - name: APPLICATION_SERVICE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: job-poster-api
                  key: APPLICATION_SERVICE_API_KEY
      restartPolicy: Always
      dnsPolicy: ClusterFirst
