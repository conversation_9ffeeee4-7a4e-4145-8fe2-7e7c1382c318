package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object RecruiterIdSpec extends FunSuite {

  val recruiterIdString        = "*********"
  val recruiterId: RecruiterId = RecruiterId(recruiterIdString)

  test("Encode RecruiterId") {
    expect(recruiterId.asJson.noSpaces == s""""$recruiterIdString"""")
  }

  test("Decode RecruiterId") {
    expect(Json.fromString(recruiterIdString).as[RecruiterId] == Right(recruiterId))
  }
}
