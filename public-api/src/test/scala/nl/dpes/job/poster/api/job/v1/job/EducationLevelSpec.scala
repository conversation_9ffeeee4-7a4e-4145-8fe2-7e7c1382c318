package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.shared.{EducationLevel => AppEducationLevel}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import weaver._

object EducationLevelSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val level = "HBO"

  test("Encode education level") {
    expect(EducationLevel.SwaggerDoc.hbo.asJson.noSpaces == s""""$level"""")
  }

  test("Decode education level") {
    expect(Json.fromString(level).as[EducationLevel] == Right(EducationLevel.SwaggerDoc.hbo))
  }

  test("Unable to decode an invalid education level") {
    expect(
      Json.fromString("something").as[EducationLevel] == Left(
        DecodingFailure(s"Education level 'something' is not valid.", List())
      )
    )
  }

  test("Creating a education level") {
    expect(EducationLevel(level) == Valid(EducationLevel.SwaggerDoc.hbo))
  }

  test("It should return an error when education level is wrong") {
    val invalidLevel = "invalid level"
    expect(EducationLevel(invalidLevel) == Invalid(s"Education level '$invalidLevel' is not valid."))
  }

  test("Mapping EducationLevel from API to Application model") {
    val actualAppEducationLevel   = EducationLevel.map(EducationLevel.SwaggerDoc.hbo.some)
    val expectedAppEducationLevel = AppEducationLevel(EducationLevel.SwaggerDoc.hbo.level).some.sequence.toValidatedNec

    expect(actualAppEducationLevel == expectedAppEducationLevel)
  }

  test("Returning error when unable to map EducationLevel from API to Application model") {
    val invalidLevel            = "Something invalid"
    val actualAppEducationLevel = EducationLevel.map(EducationLevel.SwaggerDoc.hbo.copy(level = invalidLevel).some)

    val error = s"Education level '$invalidLevel' is not valid."
    expect(actualAppEducationLevel == MappingError(cursor -> error).invalid)
  }
}
