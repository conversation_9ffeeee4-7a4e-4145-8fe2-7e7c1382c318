package nl.dpes.job.poster.api.jobapplication.mapper

import cats.effect.IO
import cats.implicits.catsSyntaxOptionId
import nl.dpes.job.poster.api.jobapplication.v1.jobapplication.Application
import nl.dpes.job.poster.api.job.v1.job.{JobId => ApiJobId}
import nl.dpes.job.poster.api.service.job.ReferenceId
import nl.dpes.job.poster.api.reference_id.{ReferenceIdService, ReferenceId => AppReferenceId}
import org.mockito.MockitoSugar.{mock, when}
import weaver.SimpleIOSuite

object JobApplicationMapperSpec extends SimpleIOSuite {

  val application: Application = Application(
    id = "String",
    recruiterId = "String".some,
    jobId = "aba80ca3-f860-4fdc-a5ff-c703d565332f",
    createdAt = 1682951210L,
    jobSeekerId = "String".some,
    jobSeekerFirstName = "String",
    jobSeekerLastName = "String",
    jobSeekerEmail = "String",
    jobSeekerPhone = "String".some,
    motivation = "String".some,
    url = "String",
    filename = "String",
    site = "String"
  )

  test("Mapping Application to JobApplication without a referenceId") {
    val referenceIdService: ReferenceIdService[IO] = mock[ReferenceIdService[IO]]

    when(referenceIdService.getReferenceId(ApiJobId("aba80ca3-f860-4fdc-a5ff-c703d565332f"))).thenReturn(IO.none)

    val jobApplication =
      JobApplication(
        applicationId = ApplicationId("String"),
        recruiterId = RecruiterId("String").some,
        jobId = JobId("aba80ca3-f860-4fdc-a5ff-c703d565332f"),
        referenceId = None,
        applicationDate = Timestamp("2023-05-01T16:26:50Z"),
        jobSeeker = JobSeeker(
          id = JobSeekerId("String").some,
          name = JobSeekerName(firstName = Name("String"), lastName = Name("String")),
          email = Email("String"),
          phone = Phone("String").some
        ),
        motivation = Motivation("String").some,
        cv = s"/api/v1/application/String/cv",
        site = Site("String")
      )

    for {
      result <- JobApplicationMapper.map(application)(referenceIdService)
    } yield expect(result == jobApplication)
  }

  test("Mapping Application to JobApplication with a referenceId") {
    val referenceIdService: ReferenceIdService[IO] = mock[ReferenceIdService[IO]]

    when(referenceIdService.getReferenceId(ApiJobId("aba80ca3-f860-4fdc-a5ff-c703d565332f")))
      .thenReturn(IO.pure(AppReferenceId("123-456-789").some))

    val jobApplication =
      JobApplication(
        applicationId = ApplicationId("String"),
        recruiterId = RecruiterId("String").some,
        jobId = JobId("aba80ca3-f860-4fdc-a5ff-c703d565332f"),
        referenceId = ReferenceId("123-456-789").some,
        applicationDate = Timestamp("2023-05-01T16:26:50Z"),
        jobSeeker = JobSeeker(
          id = JobSeekerId("String").some,
          name = JobSeekerName(firstName = Name("String"), lastName = Name("String")),
          email = Email("String"),
          phone = Phone("String").some
        ),
        motivation = Motivation("String").some,
        cv = s"/api/v1/application/String/cv",
        site = Site("String")
      )

    for {
      result <- JobApplicationMapper.map(application)(referenceIdService)
    } yield expect(result == jobApplication)
  }
}
