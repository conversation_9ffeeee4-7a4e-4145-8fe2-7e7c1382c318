package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object ApplicationIdSpec extends FunSuite {

  val applicationIdString          = "*********"
  val applicationId: ApplicationId = ApplicationId(applicationIdString)

  test("Encode ApplicationId") {
    expect(applicationId.asJson.noSpaces == s""""$applicationIdString"""")
  }

  test("Decode ApplicationId") {
    expect(Json.fromString(applicationIdString).as[ApplicationId] == Right(applicationId))
  }
}
