package nl.dpes.job.poster.api.jobperformance.v1

import cats.effect.IO
import cats.effect.kernel.Resource
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.jobperformance.v1.PerformanceServiceClient.{JobPerformanceError, UnauthorizationError}
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend
import sttp.client3.testing.SttpBackendStub
import sttp.client3.{DeserializationException, HttpError, ShowError, SttpBackend}
import sttp.model.StatusCode
import weaver.SimpleIOSuite

object PerformanceServiceClientSpec extends SimpleIOSuite {

  val client: Resource[IO, SttpBackend[IO, Any]] => PerformanceServiceClient[IO] = PerformanceServiceClient.impl[IO]("http://base-url", _)

  val recruiterId: SalesForceId = SalesForceId.unsafeApply("0038E00001KHYV4QAP")
  val performance: Performance  = Performance("job_id", 1, 1, 1)

  val response: Map[String, Performance] = Map("job_id" -> performance)

  def createStubbedClientResource(f: SttpBackendStub[IO, Any] => SttpBackendStub[IO, Any]): Resource[IO, SttpBackend[IO, Any]] =
    Resource.make(IO(f(AsyncHttpClientFs2Backend.stub[IO])))(_.close())

  test("It should return job performance") {
    for {
      result <- client(createStubbedClientResource(_.whenAnyRequest.thenRespond(Right(response))))
        .getJobsPerformance(recruiterId)
        .attempt
    } yield expect(result == Right(response))
  }

  test("It should return empty job performance when no metrics have been returned") {
    for {
      result <- client(createStubbedClientResource(_.whenAnyRequest.thenRespond(Right(Map.empty[String, Performance]))))
        .getJobsPerformance(recruiterId)
        .attempt
    } yield expect(result == Right(Map.empty[String, Performance]))
  }

  test("It should return a job_performance_error when encountering an internal server error") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("Internal error has occurred", StatusCode.InternalServerError)))
        )
      ).getJobsPerformance(recruiterId).attempt
    } yield expect(result == Left(JobPerformanceError("statusCode: 500, response: Internal error has occurred")))
  }

  test("It should return an unauthorization_error when user is unauthorized") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("Unauthorized user", StatusCode.Unauthorized)))
        )
      ).getJobsPerformance(recruiterId).attempt
    } yield expect(result == Left(UnauthorizationError("statusCode: 401, response: Unauthorized user")))
  }

  test("It should return a job_performance_error when user when encountering a deserialization error") {
    implicit val error: ShowError[StatusCode] = (_: StatusCode) => "Error"
    for {
      result <- client(
        createStubbedClientResource(_.whenAnyRequest.thenRespond(Left(DeserializationException("Error", StatusCode.BadRequest))))
      ).getJobsPerformance(recruiterId).attempt
    } yield expect(result == Left(JobPerformanceError("Error")))
  }
}
