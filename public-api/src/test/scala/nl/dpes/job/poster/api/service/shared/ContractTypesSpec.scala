package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import cats.implicits.toTraverseOps
import io.circe.CursorOp.{DownArray, MoveRight}
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.Cursor
import weaver._
import io.circe.parser.decode

object ContractTypesSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val contractTypes = """["Interim","Stage","Tijdelijk"]"""

  test("Encode contract types") {
    expect(ContractTypes.SwaggerDoc.example.asJson.noSpaces == contractTypes)
  }

  test("Decode contract types") {
    expect(decode[ContractTypes](contractTypes) == Right(ContractTypes.SwaggerDoc.example))
  }

  test("Unable to decode an invalid contract types") {
    expect(
      decode[ContractTypes]("""["something"]""") == Left(
        DecodingFailure(s"Contract type 'something' is not valid.", List(DownArray))
      )
    )
  }

  test("Unable to decode contract types with empty value") {
    expect(
      decode[ContractTypes]("""[]""") == Left(
        DecodingFailure("At least 1 type(s) should be chosen", List())
      )
    )
  }

  test("Unable to decode contract types with unsupported contract type") {
    expect(
      decode[ContractTypes]("""["Tijdelijk", "Vast", "Leer-werk overeenkomst", "Interim", "Stage", "Arbeider"]""") == Left(
        DecodingFailure("Contract type 'Arbeider' is not valid.", List(MoveRight, MoveRight, MoveRight, MoveRight, MoveRight, DownArray))
      )
    )
  }

  test("Unable to decode contract types with too many values") {
    expect(
      decode[ContractTypes]("""["Tijdelijk", "Vast", "Leer-werk overeenkomst", "Interim", "Stage", "Bijbaan"]""") == Left(
        DecodingFailure("At most 5 types should be chosen", List())
      )
    )
  }

  test("It fails when less than minimum types have been provided") {
    expect(ContractTypes(Set()) == Invalid("At least 1 type(s) should be chosen"))
  }

  test("It fails when one of the contract types is unsupported") {
    val types = List(
      ContractType("Interim"),
      ContractType("Stage"),
      ContractType("Tijdelijk"),
      ContractType("Thuiswerk"),
      ContractType("Vast"),
      ContractType("Arbeider")
    ).sequence.map(_.toSet).andThen(ContractTypes.apply)

    expect(types == Invalid("Contract type 'Arbeider' is not valid."))
  }

  test("It fails when more than maximum types have been provided") {
    val types = List(
      ContractType("Interim"),
      ContractType("Stage"),
      ContractType("Tijdelijk"),
      ContractType("Thuiswerk"),
      ContractType("Vast"),
      ContractType("Bijbaan")
    ).sequence.map(_.toSet).andThen(ContractTypes.apply)

    expect(types == Invalid("At most 5 types should be chosen"))
  }

  test("It succeeds when a correct amount of types have been provided") {
    val types = List(
      ContractType("Stage"),
      ContractType("Thuiswerk")
    ).sequence.map(_.toSet).andThen(ContractTypes.apply)

    expect(types.isValid)
  }
}
