package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object SiteSpec extends FunSuite {

  val siteString = "https://google.com"
  val site: Site = Site(siteString)

  test("Encode Site") {
    expect(site.asJson.noSpaces == s""""$siteString"""")
  }

  test("Decode Site") {
    expect(Json.fromString(siteString).as[Site] == Right(site))
  }
}
