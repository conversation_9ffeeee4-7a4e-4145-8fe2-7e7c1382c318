package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import io.circe._
import io.circe.syntax._

object EducationLevelSpec extends FunSuite {

  val level = "HBO"

  test("Encode education level") {
    expect(EducationLevel.SwaggerDoc.hbo.asJson.noSpaces == s""""$level"""")
  }

  test("Decode education level") {
    expect(Json.fromString(level).as[EducationLevel] == Right(EducationLevel.SwaggerDoc.hbo))
  }

  test("Unable to decode an invalid education level") {
    expect(
      Json.fromString("something").as[EducationLevel] == Left(
        DecodingFailure(s"Education level 'something' is not valid.", List())
      )
    )
  }

  test("Creating a education level") {
    expect(EducationLevel("HBO").isValid)
  }

  test("It should return an error when education level is wrong") {
    val invalidLevel = "invalid level"
    expect(EducationLevel(invalidLevel) == Invalid(s"Education level '$invalidLevel' is not valid."))
  }
}
