package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import weaver._
import nl.dpes.job.poster.api.service.shared.{Workplace => AppWorkplace}

object WorkplaceSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val workplace = "Remote"

  test("Encode workplace") {
    expect(Workplace.SwaggerDoc.example.asJson.noSpaces == s""""$workplace"""")
  }

  test("Decode workplace") {
    expect(Json.fromString(workplace).as[Workplace] == Right(Workplace.SwaggerDoc.example))
  }

  test("Unable to decode an invalid workplace") {
    expect(
      Json.fromString("something").as[Workplace] == Left(
        DecodingFailure(s"Workplace 'something' is not valid.", List())
      )
    )
  }

  test("Creating a workplace") {
    expect(Workplace(workplace) == Valid(Workplace.SwaggerDoc.example))
  }

  test("It should return an error when workplace is wrong") {
    val invalidWorkplace = "invalid workplace"
    expect(Workplace(invalidWorkplace) == Invalid(s"Workplace '$invalidWorkplace' is not valid."))
  }

  test("Mapping Workplace from API to Application model") {
    val actualAppWorkplace   = Workplace.map(Workplace.SwaggerDoc.example.some)
    val expectedAppWorkplace = AppWorkplace(Workplace.SwaggerDoc.example.workplace).some.sequence.toValidatedNec

    expect(actualAppWorkplace == expectedAppWorkplace)
  }

  test("Returning error when unable to map Workplace from API to Application model") {
    val invalidType        = "Something invalid"
    val actualAppWorkplace = Workplace.map(Workplace.SwaggerDoc.example.copy(workplace = invalidType).some)

    val error = s"Workplace '$invalidType' is not valid."
    expect(
      actualAppWorkplace == MappingError(cursor -> error).invalid
    )
  }
}
