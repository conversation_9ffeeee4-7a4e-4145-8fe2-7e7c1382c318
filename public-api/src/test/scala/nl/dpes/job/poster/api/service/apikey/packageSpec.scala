package nl.dpes.job.poster.api.service.apikey

import io.circe.parser._
import io.circe.syntax._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import weaver.FunSuite

object packageSpec extends FunSuite {
  test("SalesforceIds are encoded to JsonString") {
    expect(SalesForceId.unsafeApply("0035800000zdcZhAAI").asJson.noSpaces == """"0035800000zdcZhAAI"""")
  }

  test("18 character ids can be decoded to SalesforceId") {
    val expected: Either[SalesForceId.Error, SalesForceId] = SalesForceId("0035800000zdcZhAAI")
    expect(decode[SalesForceId](""""0035800000zdcZhAAI"""") == expected && expected.isRight)
  }

  test("15 character ids can be decoded to SalesforceId") {
    val expected: Either[SalesForceId.Error, SalesForceId] = SalesForceId("0035800000zdcZhAAI")
    expect(decode[SalesForceId](""""0035800000zdcZh"""") == expected && expected.isRight)
  }

  test("decoding an encoded SalesforceId should return the same id") {
    val expected: SalesForceId = SalesForceId.unsafeApply("0035800000zdcZhAAI")
    expect(expected.asJson.as[SalesForceId] == Right(expected))
  }
}
