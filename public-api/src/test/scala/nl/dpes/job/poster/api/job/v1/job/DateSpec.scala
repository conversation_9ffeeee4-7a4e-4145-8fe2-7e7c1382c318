package nl.dpes.job.poster.api.job.v1.job

import cats.syntax.validated._
import io.circe._
import io.circe.syntax._
import weaver._

object DateSpec extends FunSuite {

  test("A date can be encoded") {
    expect(Date("2020-02-20").map(_.asJson.noSpaces) == s""""2020-02-20"""".valid)
  }

  test("A date can be decoded") {
    expect(Json.fromString("2020-02-20").as[Date] == Date("2020-02-20").toEither) and
    expect(Date("2020-02-20").isValid)
  }

  test("A date can be decoded with single digit month and/or day number") {
    expect(Json.fromString("2020-2-1").as[Date] == Date("2020-02-01").toEither) and
    expect(Date("2020-2-1").isValid)
  }

  test("Unable to decode an invalid date") {
    expect(Json.fromString("invalid date").as[Date] == Left(DecodingFailure("Cannot parse 'invalid date'", List())))
  }

  test("Dates can be constructed with string containing valid format") {
    expect(Date("2020-02-20").isValid)
  }

  test("Construction with invalid format fails") {
    expect(Date("2020/2/20").isInvalid)
  }
}
