package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Invalid
import cats.implicits._
import io.circe.CursorOp.DownArray
import nl.dpes.job.poster.api.service.Cursor
import weaver._
import io.circe._
import io.circe.parser.decode
import io.circe.syntax._
import nl.dpes.job.poster.api.service.shared.{IndustryCategories => AppIndustryCategories, IndustryCategory => AppIndustryCategory}

object IndustryCategoriesSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val industryCategories = """["Telecom","Techniek"]"""

  test("Encode industry categories") {
    expect(IndustryCategories.SwaggerDoc.example.asJson.noSpaces == industryCategories)
  }

  test("Decode industry categories") {
    expect(decode[IndustryCategories](industryCategories) == Right(IndustryCategories.SwaggerDoc.example))
  }

  test("Unable to decode an invalid industry categories") {
    expect(
      decode[IndustryCategories]("""["something"]""") == Left(
        DecodingFailure(s"Industry category 'something' is not valid.", List(DownArray))
      )
    )
  }

  test("Unable to decode industry categories with empty value") {
    expect(
      decode[IndustryCategories]("""[]""") == Left(
        DecodingFailure("At least 1 categories should be chosen", List())
      )
    )
  }

  test("Unable to decode industry categories with too many values") {
    expect(
      decode[IndustryCategories]("""["Telecom","Techniek","Automotive"]""") == Left(
        DecodingFailure("At most 2 categories should be chosen", List())
      )
    )
  }

  test("It fails when less than minimum categories have been provided") {
    expect(IndustryCategories(Set()) == Invalid("At least 1 categories should be chosen"))
  }

  test("It fails when more than maximum categories have been provided") {
    val categories = List(
      IndustryCategory("Techniek"),
      IndustryCategory("Telecom"),
      IndustryCategory("Automotive")
    ).sequence.map(_.toSet).andThen(IndustryCategories.apply)

    expect(categories == Invalid("At most 2 categories should be chosen"))
  }

  test("It succeeds when a correct amount of categories have been provided") {
    val categories = List(
      IndustryCategory("Techniek"),
      IndustryCategory("Automotive")
    ).sequence.map(_.toSet).andThen(IndustryCategories.apply)

    expect(categories.isValid)
  }

  test("Mapping IndustryCategories from API to Application model") {
    val actualAppIndustryCategories   = IndustryCategories.map(IndustryCategories.SwaggerDoc.example.some)
    val categories                    = Set(AppIndustryCategory("Techniek").toOption.get, AppIndustryCategory("Telecom").toOption.get)
    val expectedAppIndustryCategories = AppIndustryCategories(categories).some.sequence.toValidatedNec

    expect(actualAppIndustryCategories == expectedAppIndustryCategories)
  }
}
