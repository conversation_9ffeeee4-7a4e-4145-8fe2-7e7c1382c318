package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Valid
import cats.implicits._
import io.circe.CursorOp.DownField
import io.circe._
import io.circe.syntax._
import weaver._
import io.circe.parser
import io.circe.parser._
import nl.dpes.job.poster.api.service.Cursor
import nl.dpes.job.poster.api.service.shared.{
  EasyApply => AppEasyApply,
  ApplyViaExternalWebsite => AppApplyViaExternalWebsite,
  ApplyViaJobBoard => AppApplyViaJobBoard,
  Website => AppWebsite
}
import org.typelevel.jawn.ParseException

object ApplicationMethodSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val applyViaJobBoardString: String = """{"firstName":"Recruiter","lastName":"Lastname","emailAddress":"<EMAIL>"}"""

  val applyViaExternalWebsiteString = """{"url":"https://www.google.com"}"""

  val easyApplyString = """{}"""

  val applyViaJobBoard: ApplyViaJobBoard = ApplyViaJobBoard("Recruiter", "Lastname", "<EMAIL>")

  val applyViaExternalWebsite: ApplyViaExternalWebsite = ApplyViaExternalWebsite(Website("https://www.google.com").toOption.get)

  val easyApply: EasyApply.type = EasyApply

  test("Decode ApplyViaJobBoard") {
    expect(
      parser
        .parse(applyViaJobBoardString)
        .flatMap(_.as[ApplyViaJobBoard]) == applyViaJobBoard.asRight[Error]
    )
  }

  test("Encode ApplyViaJobBoard") {
    expect(applyViaJobBoard.asJson.noSpaces == applyViaJobBoardString)
  }

  test("An ApplyViaExternalWebsite fails decoding an invalid url") {
    expect(
      decode[ApplyViaExternalWebsite]("""{"url":"invalid url"}""") == Left(
        DecodingFailure("java.net.MalformedURLException: no protocol: invalid url", List(DownField("url")))
      )
    )
  }

  test("Decode ApplyViaExternalWebsite") {
    expect(
      parser
        .parse(applyViaExternalWebsiteString)
        .flatMap(_.as[ApplyViaExternalWebsite]) == Right(applyViaExternalWebsite)
    )
  }

  test("Encode ApplyViaExternalWebsite") {
    expect(applyViaExternalWebsite.asJson.noSpaces == applyViaExternalWebsiteString)
  }

  test("Decode EasyApply") {
    expect(
      parser
        .parse(easyApplyString)
        .flatMap(_.as[EasyApply.type]) == Right(easyApply)
    )
  }

  test("Encode EasyApply") {
    expect(easyApply.asJson.noSpaces == easyApplyString)
  }

  test("An EasyApply fails decoding an invalid input") {
    expect(
      decode[EasyApply.type]("something_invalid") == Left(
        ParsingFailure(
          "expected json value got 'someth...' (line 1, column 1)",
          ParseException(msg = "expected json value got 'someth...' (line 1, column 1)", line = 1, col = 1, index = 0)
        )
      )
    )
  }

  test("Mapping ApplyViaJobBoard from API to Application model") {
    val actualAppJobBoard   = ApplicationMethod.map(ApplyViaJobBoard("John", "Doe", "<EMAIL>").some)
    val expectedAppJobBoard = Valid(AppApplyViaJobBoard("John", "Doe", "<EMAIL>").some)

    expect(actualAppJobBoard == expectedAppJobBoard)
  }

  test("Mapping ApplyViaExternalWebsite from API to Application model") {
    val actualAppExternalWebsite   = ApplicationMethod.map(ApplyViaExternalWebsite(Website("https://www.google.com").toOption.get).some)
    val expectedAppExternalWebsite = AppApplyViaExternalWebsite(AppWebsite("https://www.google.com").toOption.get).some.valid

    expect(actualAppExternalWebsite == expectedAppExternalWebsite)
  }

  test("Mapping EasyApply from API to Application model") {
    val actualAppEasyApply   = ApplicationMethod.map(EasyApply.some)
    val expectedAppEasyApply = AppEasyApply.some.valid

    expect(actualAppEasyApply == expectedAppEasyApply)
  }
}
