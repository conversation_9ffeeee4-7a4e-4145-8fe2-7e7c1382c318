package nl.dpes.job.poster.api.defaults

import cats.syntax.either._
import nl.dpes.job.poster.api.Unauthorized
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.recruiter.RecruiterService.RecruiterUnauthorized
import nl.dpes.job.poster.api.service.recruiter.{AccessToken, RecruiterService}
import org.mockito.ArgumentMatchersSugar._
import org.mockito.MockitoSugar._
import weaver.FunSuite

import scala.util.{Failure, Success, Try}

object DefaultsServiceTest extends FunSuite {
  val emptyDefaults: Defaults = Defaults.defaultsDecoder("{}")

  test("When recruiter cannot be retrieved Unauthorized will be returned") {
    val recruiterService = mock[RecruiterService[Try]]
    when(recruiterService.getRecruiter(AccessToken(any[String]))).thenReturn(Failure(RecruiterUnauthorized("message")))
    val defaultsRepository = mock[DefaultsRepository[Try]]

    val defaultsService = DefaultsService.apply(defaultsRepository, recruiterService)

    val unAuthorized: Try[Either[Unauthorized, Unit]] = Success(Unauthorized("Error retrieving recruiter 'message'.").asLeft[Unit])

    expect(defaultsService.getDefaults(BearerToken("a token"))(()) == unAuthorized) and
    expect(defaultsService.saveDefaults(BearerToken("a token"))(emptyDefaults) == unAuthorized)
  }
}
