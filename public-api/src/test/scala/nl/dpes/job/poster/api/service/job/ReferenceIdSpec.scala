package nl.dpes.job.poster.api.service.job

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object ReferenceIdSpec extends FunSuite {

  val referenceIdString        = "1234-56789"
  val referenceId: ReferenceId = ReferenceId(referenceIdString)

  test("Encode ReferenceId") {
    expect(referenceId.asJson.noSpaces == s""""$referenceIdString"""")
  }

  test("Decode ReferenceId") {
    expect(Json.fromString(referenceIdString).as[ReferenceId] == Right(referenceId))
  }
}
