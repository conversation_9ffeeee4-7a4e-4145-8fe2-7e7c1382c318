package nl.dpes.job.poster.api.shutdownguard

import cats.effect.Async
import cats.effect.kernel.Ref
import cats.implicits._

import scala.concurrent.duration.Duration

trait TestRuntime[F[_]] {
  def setShutdownHook(hook: => Unit): F[Unit]
  def shutdown(wait: Duration): F[Unit]
}

object TestRuntime {

  def impl[F[_]: Async]: F[TestRuntime[F]] = for {
    shutDownHook <- Ref.of[F, Option[() => Unit]](none)
  } yield new TestRuntime[F] {

    override def setShutdownHook(hook: => Unit): F[Unit] =
      Async[F].blocking(println("setting shutdownhook")) >> shutDownHook.set((() => hook).some)

    override def shutdown(wait: Duration): F[Unit] = for {
      _    <- Async[F].sleep(wait)
      hook <- shutDownHook.get
    } yield hook match {
      case Some(h) => h()
      case None    => ()
    }
  }

  implicit def shutdownHandler[F[_]: Async]: ShutdownHandler[F, TestRuntime[F]] = new ShutdownHandler[F, TestRuntime[F]] {
    override def registerShutdownHook(runtime: TestRuntime[F], f: => Unit): F[Unit] = runtime.setShutdownHook(f)
  }
}
