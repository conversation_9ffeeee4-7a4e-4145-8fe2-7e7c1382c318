package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import cats.syntax.validated._
import io.circe._
import io.circe.syntax._

object JobCategorySpec extends FunSuite {

  val category = "Administratief/Secretarieel"

  test("Job category encode to string") {
    expect(JobCategory(category).map(_.asJson.noSpaces) == s""""$category"""".valid)
  }

  test("Job category decode from string") {
    expect(Json.fromString(category).as[JobCategory] == JobCategory(category).toEither) and
    expect(JobCategory(category).isValid)
  }

  test("Creating an job category") {
    expect(JobCategory("Administratief/Secretarieel").isValid)
  }

  test("Unable to decode an invalid job category") {
    expect(
      Json.fromString("something").as[JobCategory] == Left(
        DecodingFailure("Job category 'something' is not valid.", List())
      )
    )
  }

  test("It should return an error when job category is invalid") {
    val invalidCategory = "invalid category"
    expect(JobCategory(invalidCategory) == Invalid(s"Job category '$invalidCategory' is not valid."))
  }
}
