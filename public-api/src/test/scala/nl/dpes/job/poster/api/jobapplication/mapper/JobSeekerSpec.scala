package nl.dpes.job.poster.api.jobapplication.mapper

import cats.implicits.catsSyntaxOptionId
import io.circe.jawn.decode
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object JobSeekerSpec extends FunSuite {

  val jobSeekerString =
    """{"id":"123456","name":{"firstName":"someone","lastName":"candidate"},"email":"<EMAIL>","phone":"*********"}"""

  val jobSeekerId: Option[JobSeekerId] = JobSeekerId("123456").some
  val jobSeekerName: JobSeekerName     = JobSeekerName(Name("someone"), Name("candidate"))
  val email: Email                     = Email("<EMAIL>")
  val phone: Option[Phone]             = Phone("*********").some

  val jobSeeker: JobSeeker = JobSeeker(jobSeekerId, jobSeekerName, email, phone)

  test("Encode JobSeeker") {
    expect(jobSeeker.asJson.noSpaces == jobSeekerString)
  }

  test("Decode JobSeeker") {
    expect(decode[JobSeeker](jobSeekerString) == Right(jobSeeker))
  }
}
