package nl.dpes.job.poster.api.service.addressservice

import cats.effect.IO
import cats.effect.kernel.Resource
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.job.v1.job.{City, Geolocation, Latitude, Longitude}
import nl.dpes.job.poster.api.service.addressservice.AddressServiceClient.{
  AddressServiceError,
  InvalidData,
  UnsupportedLocale,
  ZipCodeNotFound,
  ZipCodeNotFoundByCityName
}
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend
import sttp.client3.testing.SttpBackendStub
import sttp.client3.{HttpError, SttpBackend}
import sttp.model.StatusCode
import weaver.SimpleIOSuite

object AddressServiceClientSpec extends SimpleIOSuite {

  implicit val loggerFactory: LoggerFactory[IO] = Slf4jFactory.create[IO]

  val client: Resource[IO, SttpBackend[IO, Any]] => AddressServiceClient[IO] = AddressServiceClient.impl[IO]("http://base-url", _)

  val recruiterId: SalesForceId = SalesForceId.unsafeApply("0038E00001KHYV4QAP")
  val addressInfo: AddressInfo  = AddressInfo("1018LL")
  val longitude: Longitude      = Longitude(4.9041)
  val latitude: Latitude        = Latitude(52.3676)
  val geolocation: Geolocation  = Geolocation(longitude, latitude)
  val city: City                = City("Duivendrecht")

  def createStubbedClientResource(f: SttpBackendStub[IO, Any] => SttpBackendStub[IO, Any]): Resource[IO, SttpBackend[IO, Any]] =
    Resource.make(IO(f(AsyncHttpClientFs2Backend.stub[IO])))(_.close())

  test("It should return a zipcode by geolocation") {
    for {
      result <- client(createStubbedClientResource(_.whenAnyRequest.thenRespond(Right(addressInfo))))
        .getZipcodeByGeolocation(geolocation)
        .attempt
    } yield expect(result == Right(addressInfo))
  }

  test("It should return ZipCodeNotFound error when receiving a NotFound exception from address-service") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("NotFound", StatusCode.NotFound)))
        )
      ).getZipcodeByGeolocation(geolocation).attempt
    } yield expect(result == Left(ZipCodeNotFound(geolocation)))
  }

  test("It should return an InvalidData error when receiving a BadRequest exception from address-service") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("Bad Request", StatusCode.BadRequest)))
        )
      ).getZipcodeByGeolocation(geolocation).attempt
    } yield expect(result == Left(InvalidData("statusCode: 400, response: Bad Request")))
  }

  test("It should return UnsupportedLocale error when receiving a NotAcceptable exception from address-service") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("NotAcceptable", StatusCode.NotAcceptable)))
        )
      ).getZipcodeByGeolocation(geolocation).attempt
    } yield expect(result == Left(UnsupportedLocale("statusCode: 406, response: NotAcceptable")))
  }

  test("It should return a AddressServiceError when encountering an InternalServerError exception from address-service") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("Internal error has occurred", StatusCode.InternalServerError)))
        )
      ).getZipcodeByGeolocation(geolocation).attempt
    } yield expect(result == Left(AddressServiceError(s"Invalid '$geolocation': statusCode: 500, response: Internal error has occurred")))
  }

  test("It should return a zipcode by city") {
    for {
      result <- client(createStubbedClientResource(_.whenAnyRequest.thenRespond(Right(addressInfo))))
        .getZipcodeByCity(city)
        .attempt
    } yield expect(result == Right(addressInfo))
  }

  test("It should return ZipCodeNotFound error when receiving a NotFound exception from address-service") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("NotFound", StatusCode.NotFound)))
        )
      ).getZipcodeByCity(city).attempt
    } yield expect(result == Left(ZipCodeNotFoundByCityName(city)))
  }

  test("It should return an InvalidData error when receiving a BadRequest exception from address-service") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("Bad Request", StatusCode.BadRequest)))
        )
      ).getZipcodeByCity(city).attempt
    } yield expect(result == Left(InvalidData("statusCode: 400, response: Bad Request")))
  }

  test("It should return UnsupportedLocale error when receiving a NotAcceptable exception from address-service") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("NotAcceptable", StatusCode.NotAcceptable)))
        )
      ).getZipcodeByCity(city).attempt
    } yield expect(result == Left(UnsupportedLocale("statusCode: 406, response: NotAcceptable")))
  }

  test("It should return a AddressServiceError when encountering an InternalServerError exception from address-service") {
    for {
      result <- client(
        createStubbedClientResource(
          _.whenAnyRequest.thenRespond(Left(HttpError("Internal error has occurred", StatusCode.InternalServerError)))
        )
      ).getZipcodeByCity(city).attempt
    } yield expect(result == Left(AddressServiceError(s"Invalid '$city': statusCode: 500, response: Internal error has occurred")))
  }

  test("It should trim whitespace from city name when making API request") {
    val cityWithTrailingSpace = City("Amsterdam ")
    val backendStub = AsyncHttpClientFs2Backend
      .stub[IO]
      .whenRequestMatches(_.uri.toString.endsWith("/api/v1/geolocations/nl/Amsterdam"))
      .thenRespond(Right(addressInfo))

    for {
      result <- client(Resource.make(IO(backendStub))(_.close()))
        .getZipcodeByCity(cityWithTrailingSpace)
        .attempt
    } yield expect(result == Right(addressInfo))
  }
}
