package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object TimestampSpec extends FunSuite {

  val timestampString      = "2023-05-01T16:26:50Z"
  val timestamp: Timestamp = Timestamp("2023-05-01T16:26:50Z")

  test("Encode Timestamp") {
    expect(timestamp.asJson.noSpaces == s""""$timestampString"""")
  }

  test("Decode Timestamp") {
    expect(Json.fromString(timestampString).as[Timestamp] == Right(timestamp))
  }
}
