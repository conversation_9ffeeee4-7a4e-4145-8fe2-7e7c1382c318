package nl.dpes.job.poster.api.service.prediction

import cats.effect.{IO, Resource}
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend
import sttp.client3.{HttpError, SttpBackend}
import sttp.model.StatusCode
import weaver.SimpleIOSuite

object VacancyEnricherClientSpec extends SimpleIOSuite {

  implicit val loggerFactory: LoggerFactory[IO] = Slf4jFactory.create[IO]

  test("it should return the response if no error occurred") {
    for {
      backend <- IO(
        AsyncHttpClientFs2Backend
          .stub[IO]
          .whenAnyRequest
          .thenRespond(
            Right(
              VacancyEnricherClient.Response(
                List(),
                List(IndustryCategory("een branch")),
                List(JobCategory("een vakgebied")),
                EducationLevel("VWO"),
                CareerLevel("Directie"),
                ContractType("Freelance")
              )
            )
          )
      )
      resource <- IO(Resource.make[IO, SttpBackend[IO, Any]](IO(backend))(_.close()))
      result <- VacancyEnricherClient
        .impl[IO](VacancyEnricherClient.Host("a host"), VacancyEnricherClient.ApiKey("an API kej"), resource)
        .predict(VacancyEnricherClient.Request(JobTitle("titel"), JobDescription("description")))
    } yield expect(
      result == VacancyEnricherClient.Response(
        List(),
        List(IndustryCategory("een branch")),
        List(JobCategory("een vakgebied")),
        EducationLevel("VWO"),
        CareerLevel("Directie"),
        ContractType("Freelance")
      )
    )
  }

  test("it should return an empty response in case of errors thrown by VacancyEnricher") {
    for {
      backend <- IO(
        AsyncHttpClientFs2Backend
          .stub[IO]
          .whenAnyRequest
          .thenRespond(Left(HttpError("Something went wrong", StatusCode.InternalServerError)))
      )
      resource <- IO(Resource.make[IO, SttpBackend[IO, Any]](IO(backend))(_.close()))
      result <- VacancyEnricherClient
        .impl[IO](VacancyEnricherClient.Host("a host"), VacancyEnricherClient.ApiKey("an API key"), resource)
        .predict(VacancyEnricherClient.Request(JobTitle("title"), JobDescription("description")))
    } yield expect(
      result == VacancyEnricherClient.Response(
        List(),
        List(),
        List(),
        EducationLevel(""),
        CareerLevel(""),
        ContractType("")
      )
    )
  }
}
