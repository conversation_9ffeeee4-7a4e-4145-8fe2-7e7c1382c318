package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Valid
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.Cursor
import weaver._
import nl.dpes.job.poster.api.service.shared.{Zipcode => AppLocation}

object ZipcodeSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val location = "1018LL"

  test("Encode location") {
    expect(Zipcode(location).asJson.noSpaces == s""""$location"""")
  }

  test("Decode location") {
    expect(Json.fromString(location).as[Zipcode] == Right(Zipcode(location)))
  }

  test("Mapping Location from API to Application model") {
    val actualAppLocation   = Zipcode.map(Zipcode(location).some)
    val expectedAppLocation = Valid(AppLocation(location).some)

    expect(actualAppLocation == expectedAppLocation)
  }
}
