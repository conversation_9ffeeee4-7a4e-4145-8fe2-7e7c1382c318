package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import io.circe._
import io.circe.syntax._

object LogoSpec extends FunSuite {

  private val validUrl = "http://google.com"

  test("Encode logo") {
    expect(Logo(validUrl).toOption.get.asJson.noSpaces == s""""$validUrl"""")
  }

  test("Decode logo") {
    expect(Json.fromString(validUrl).as[Logo] == Right(Logo(validUrl).toOption.get))
  }

  test("Unable to decode an invalid logo") {
    expect(Json.fromString("something").as[Logo] == Left(DecodingFailure("java.net.MalformedURLException: no protocol: something", List())))
  }

  test("Creating a logo url") {
    expect(Logo(validUrl).isValid)
  }

  test("It should return an error when logo url is wrong") {
    val invalidUrl = "invalid url"
    expect(Logo(invalidUrl) == Invalid(s"java.net.MalformedURLException: no protocol: $invalidUrl"))
  }
}
