package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Invalid
import cats.implicits._
import io.circe.CursorOp.DownArray
import io.circe._
import io.circe.syntax._
import io.circe.parser.decode
import nl.dpes.job.poster.api.service.Cursor
import weaver.FunSuite
import nl.dpes.job.poster.api.service.shared.{JobCategories => AppJobCategories, JobCategory => AppJobCategory}

object JobCategoriesSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val jobCategories = """["Administratief/Secretarieel","Automatisering/Internet"]"""

  test("Encode job categories") {
    expect(JobCategories.SwaggerDoc.example.asJson.noSpaces == jobCategories)
  }

  test("Decode job categories") {
    expect(decode[JobCategories](jobCategories) == Right(JobCategories.SwaggerDoc.example))
  }

  test("Unable to decode an invalid job categories") {
    expect(
      decode[JobCategories]("""["something"]""") == Left(
        DecodingFailure(s"Job category 'something' is not valid.", List(DownArray))
      )
    )
  }

  test("Unable to decode job categories with empty value") {
    expect(
      decode[JobCategories]("""[]""") == Left(
        DecodingFailure("At least 1 categories should be chosen", List())
      )
    )
  }

  test("Unable to decode job categories with too many values") {
    expect(
      decode[JobCategories]("""["Administratief/Secretarieel","Automatisering/Internet","Marketing/PR/Communicatie"]""") == Left(
        DecodingFailure("At most 2 categories should be chosen", List())
      )
    )
  }

  test("It fails when less than minimum categories have been provided") {
    expect(JobCategories(Set()) == Invalid("At least 1 categories should be chosen"))
  }

  test("It fails when more than maximum categories have been provided") {
    val categories = List(
      JobCategory("Administratief/Secretarieel"),
      JobCategory("Automatisering/Internet"),
      JobCategory("Marketing/PR/Communicatie")
    ).sequence.map(_.toSet).andThen(JobCategories.apply)

    expect(categories == Invalid("At most 2 categories should be chosen"))
  }

  test("It succeeds when a correct amount of categories have been provided") {
    val categories = List(
      JobCategory("Administratief/Secretarieel"),
      JobCategory("Automatisering/Internet")
    ).sequence.map(_.toSet).andThen(JobCategories.apply)

    expect(categories.isValid)
  }

  test("Mapping JobCategories from API to Application model") {
    val actualAppJobCategories   = JobCategories.map(JobCategories.SwaggerDoc.example.some)
    val categories               = Set(AppJobCategory("Administratief/Secretarieel").toOption.get, AppJobCategory("Automatisering/Internet").toOption.get)
    val expectedAppJobCategories = AppJobCategories.apply(categories).some.sequence.toValidatedNec

    expect(actualAppJobCategories == expectedAppJobCategories)
  }
}
