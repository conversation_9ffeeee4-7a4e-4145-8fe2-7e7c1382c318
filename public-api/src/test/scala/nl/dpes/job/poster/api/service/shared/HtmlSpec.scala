package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Valid
import weaver._
import cats.implicits._
import io.circe.Json
import io.circe.syntax._

object HtmlSpec extends FunSuite {

  test("It should strip tags that are not allowed and remove all attributes") {
    val document = """<html><body><div><p id="123">a paragraph with a <a href="#123">link</a>.</p></div></body></html>"""
    expect(
      Html(document).map(_.document) == "<p>a paragraph with a link.</p>".valid
    )
  }

  test("It should remove empty elements") {
    val document = """<p></p><p>  </p><p><br></p><li></li>"""
    expect(
      Html(document).map(_.document) == Valid("")
    )
  }

  test("Html can be encoded to a json string") {
    val document = "<p>A document</p>"
    expect(Html(document).map(_.asJson.noSpaces) == s""""$document"""".valid)
  }

  test("Html can be decoded from a json string") {
    val document = "<p>A document</p>"
    expect(Json.fromString(document).as[Html] == Html(document).toEither) and
    expect(Html(document).isValid)
  }

  test("The documentation is actually valid") {
    expect(Html("some description").isValid)
  }
}
