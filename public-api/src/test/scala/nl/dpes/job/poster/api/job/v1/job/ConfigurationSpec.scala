package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Valid
import cats.implicits._
import io.circe.CursorOp.{DownArray, DownField}
import io.circe._
import io.circe.parser.decode
import io.circe.syntax._
import nl.dpes.job.poster.api.job.v1.job.{
  JobPosting => ApiJobPosting,
  PerformanceBased => ApiPerformanceBased,
  Feature => ApiFeature,
  Site => ApiSite
}
import nl.dpes.job.poster.api.service.Cursor
import nl.dpes.job.poster.api.service.shared.{
  JobPosting => AppJobPosting,
  PerformanceBased => AppPerformanceBased,
  Feature => AppFeature,
  Budget => AppBudget,
  Site => AppSite
}
import weaver._

import scala.concurrent.duration.DurationInt

object ConfigurationSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val jobPostingString =
    """{"publishOn":["Nationale Vacaturebank","Intermediair"],"publicationDuration":60,"features":["logo"]}"""

  val invalidJobPostingString =
    """{"publishOn":["Nationale Vacaturebank","Intermediair"],"publicationDuration":60,"features":["something"]}"""

  val performanceBasedString = """{"budget":500}"""

  val invalidPerformanceBasedString = """{"budget": -500}"""

  val apiJobPosting: ApiJobPosting = ApiJobPosting(
    Set(ApiSite.SwaggerDoc.nvb, ApiSite.SwaggerDoc.iol),
    60.days,
    Set(ApiFeature.SwaggerDoc.logo)
  )

  val apiPerformanceBased: ApiPerformanceBased = ApiPerformanceBased(Budget.SwaggerDoc.example)

  test("Decode JobPosting") {
    expect(decode[JobPosting](jobPostingString) == Right(apiJobPosting))
  }

  test("Encode JobPosting") {
    expect(apiJobPosting.asJson.noSpaces == jobPostingString)
  }

  test("Unable to decode an invalid JobPosting") {
    expect(
      decode[JobPosting](invalidJobPostingString) == Left(
        DecodingFailure(s"Feature 'something' is not valid.", List(DownArray, DownField("features")))
      )
    )
  }

  test("Decode PerformanceBased") {
    expect(decode[PerformanceBased](performanceBasedString) == Right(apiPerformanceBased))
  }

  test("Encode PerformanceBased") {
    expect(apiPerformanceBased.asJson.noSpaces == performanceBasedString)
  }

  test("Unable to decode an invalid PerformanceBased") {
    expect(
      decode[PerformanceBased](invalidPerformanceBasedString) == Left(
        DecodingFailure("Budget '-500' must not be negative.", List(DownField("budget")))
      )
    )
  }

  test("Mapping JobPosting from API to Application model") {
    val actualAppJobPosting = Configuration.map(apiJobPosting.some)
    val expectedAppJobPosting = Valid(
      AppJobPosting(
        Set(AppSite("Nationale Vacaturebank").toOption.get, AppSite("Intermediair").toOption.get),
        60.days,
        Set(AppFeature("logo").toOption.get)
      ).some
    )
    expect(actualAppJobPosting == expectedAppJobPosting)
  }

  test("Mapping PerformanceBased from API to Application model") {
    val actualAppPerformanceBased   = Configuration.map(apiPerformanceBased.some)
    val expectedAppPerformanceBased = AppBudget(500).map(AppPerformanceBased.apply(_).some)

    expect(actualAppPerformanceBased == expectedAppPerformanceBased)
  }

  test("decode pbp as configuration") {
    expect(
      decode[PerformanceBased](performanceBasedString).map(_.asInstanceOf[Configuration].asJson.noSpaces) == Right(
        """{"PerformanceBased":{"budget":500}}"""
      )
    )
  }
}
