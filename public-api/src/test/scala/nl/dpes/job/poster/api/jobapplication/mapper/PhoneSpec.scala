package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object PhoneSpec extends FunSuite {

  val phoneString  = "0612356789"
  val phone: Phone = Phone(phoneString)

  test("Encode Phone") {
    expect(phone.asJson.noSpaces == s""""$phoneString"""")
  }

  test("Decode Phone") {
    expect(Json.fromString(phoneString).as[Phone] == Right(phone))
  }
}
