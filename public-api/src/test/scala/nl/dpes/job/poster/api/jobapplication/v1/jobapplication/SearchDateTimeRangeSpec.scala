package nl.dpes.job.poster.api.jobapplication.v1.jobapplication

import cats.data.Validated
import cats.data.Validated.Invalid
import cats.effect.IO
import weaver.SimpleIOSuite

import java.time.{LocalDate, ZonedDateTime}
import java.time.format.DateTimeFormatter.ISO_ZONED_DATE_TIME

object SearchDateTimeRangeSpec extends SimpleIOSuite {

  val validSearchRangeDateTime: Validated[String, SearchDateTimeRange] =
    SearchDateTimeRange(StartDate.SwaggerDoc.infStartDateTime, EndDate.SwaggerDoc.supEndDateTime)

  val invalidSearchRangeDateTime: Validated[String, SearchDateTimeRange] =
    SearchDateTimeRange(StartDate.SwaggerDoc.supStartDateTime, EndDate.SwaggerDoc.infEndDateTime)

  val startLocalDate: LocalDate = ZonedDateTime.parse(StartDate.SwaggerDoc.supStartDateTime.value, ISO_ZONED_DATE_TIME).toLocalDate
  val endLocalDate: LocalDate   = ZonedDateTime.parse(EndDate.SwaggerDoc.infEndDateTime.value, ISO_ZONED_DATE_TIME).toLocalDate

  test("Create a search datetime range successfully") {
    IO(expect(validSearchRangeDateTime.isValid))
  }

  test("End datetime cannot be before start datetime") {
    IO(expect(invalidSearchRangeDateTime == Invalid(s"End date '$endLocalDate' cannot be before start date '$startLocalDate'.")))
  }
}
