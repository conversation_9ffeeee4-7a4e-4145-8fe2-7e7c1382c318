package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import cats.syntax.validated._
import io.circe._
import io.circe.syntax._

object IndustryCategorySpec extends FunSuite {

  test("Industry categories encode to string") {
    expect(IndustryCategory("Techniek").map(_.asJson.noSpaces) == """"Techniek"""".valid)
  }

  test("Industry categories decode from string") {
    expect(Json.fromString("Techniek").as[IndustryCategory] == IndustryCategory("Techniek").toEither) and
    expect(IndustryCategory("Techniek").isValid)
  }

  test("Unable to decode an invalid industry category") {
    expect(
      Json.fromString("something").as[IndustryCategory] == Left(
        DecodingFailure(s"Industry category 'something' is not valid.", List())
      )
    )
  }

  test("Creating an industry category") {
    expect(IndustryCategory("Techniek").isValid)
  }

  test("It should return an error when industry category is invalid") {
    val invalidCategory = "invalid category"
    expect(IndustryCategory(invalidCategory) == Invalid(s"Industry category '$invalidCategory' is not valid."))
  }
}
