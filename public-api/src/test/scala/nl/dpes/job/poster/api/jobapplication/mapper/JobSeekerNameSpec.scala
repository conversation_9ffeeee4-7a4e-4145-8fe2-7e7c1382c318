package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.jawn.decode
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object JobSeekerNameSpec extends FunSuite {

  val jobSeekerNameString          = """{"firstName":"someone","lastName":"candidate"}"""
  val jobSeekerName: JobSeekerName = JobSeekerName(Name("someone"), Name("candidate"))

  test("Encode JobSeekerName") {
    expect(jobSeekerName.asJson.noSpaces == jobSeekerNameString)
  }

  test("Decode JobSeekerName") {
    expect(decode[JobSeekerName](jobSeekerNameString) == Right(jobSeekerName))
  }
}
