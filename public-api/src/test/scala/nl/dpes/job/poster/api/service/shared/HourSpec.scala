package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import io.circe._
import io.circe.syntax._

object HourSpec extends FunSuite {

  val hour = 10

  test("Decode hour") {
    expect(Hour.SwaggerDoc.minHourExample.asJson.noSpaces == s"""$hour""")
  }

  test("Encode hour") {
    expect(Json.fromInt(hour).as[Hour] == Right(Hour.SwaggerDoc.minHourExample))
  }

  test("Unable to decode an hour less than 1") {
    expect(Json.fromInt(-250).as[Hour] == Left(DecodingFailure("Hour '-250' cannot be less than 1.", List())))
  }

  test("Unable to decode a hour greater than 40") {
    expect(Json.fromInt(41).as[Hour] == Left(DecodingFailure("Hour '41' cannot be greater than 40.", List())))
  }

  test("Creating an hour") {
    expect(Hour(10).isValid)
  }

  test("It should return an error when hour is invalid") {
    val invalidHour = -1
    expect(Hour(invalidHour) == Invalid(s"Hour '$invalidHour' cannot be less than 1."))
  }
}
