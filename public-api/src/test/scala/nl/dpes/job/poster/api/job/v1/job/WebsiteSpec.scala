package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Invalid
import io.circe._
import io.circe.syntax._
import weaver._

object WebsiteSpec extends FunSuite {

  val validUrl = "http://google.com"

  test("Encode website") {
    expect(Website(validUrl).toOption.get.asJson.noSpaces == s""""$validUrl"""")
  }

  test("Decode website") {
    expect(Json.fromString(validUrl).as[Website] == Right(Website(validUrl).toOption.get))
  }

  test("Unable to decode an invalid website") {
    expect(
      Json.fromString("something").as[Website] == Left(DecodingFailure("java.net.MalformedURLException: no protocol: something", List()))
    )
  }

  test("Creating a website url") {
    expect(Website(validUrl).isValid)
  }

  test("It should return an error when website url is wrong") {
    val invalidUrl = "invalid url"
    expect(Website(invalidUrl) == Invalid(s"java.net.MalformedURLException: no protocol: $invalidUrl"))
  }
}
