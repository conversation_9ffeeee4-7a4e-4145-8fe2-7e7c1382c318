package nl.dpes.job.poster.api.cpc_registrations

import io.circe.Json
import io.circe.syntax._
import weaver.FunSuite

import java.time.Instant

object TimestampSpec extends FunSuite {

  val timestampString      = "2025-05-14T13:03:09.199Z"
  val timestamp: Timestamp = Timestamp(timestampString)

  test("Encode Timestamp") {
    expect(timestamp.asJson.noSpaces == s""""$timestampString"""")
  }

  test("now() returns a non-empty timestamp") {
    val now = Timestamp.now()
    expect(now.value.nonEmpty)
  }

  test("sixHoursAgo() returns a non-empty timestamp") {
    val sixHoursAgo = Timestamp.sixHoursAgo()
    expect(sixHoursAgo.value.nonEmpty)
  }

  test("SwaggerDoc examples return non-empty timestamps") {
    val fromExample = Timestamp.SwaggerDoc.fromExample
    val toExample   = Timestamp.SwaggerDoc.toExample

    expect(fromExample.value.nonEmpty && toExample.value.nonEmpty)
  }
}
