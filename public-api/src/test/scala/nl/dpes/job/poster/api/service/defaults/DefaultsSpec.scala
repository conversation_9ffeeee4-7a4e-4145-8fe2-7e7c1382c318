package nl.dpes.job.poster.api.service.defaults

import nl.dpes.job.poster.api.defaults.Defaults
import nl.dpes.job.poster.api.generators._
import weaver._
import weaver.scalacheck._

object DefaultsSpec extends SimpleIOSuite with Checkers {
  test("db codec can do roundtrip") {

    forall(genDefaults) { defaults =>
      val encoded = Defaults.encodeDefaults.f(defaults)
      val decoded = Defaults.decodeDefaults.f(encoded)
      expect(decoded == defaults)
    }
  }
}
