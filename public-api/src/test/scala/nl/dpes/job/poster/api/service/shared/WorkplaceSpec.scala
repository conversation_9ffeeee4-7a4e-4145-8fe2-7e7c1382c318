package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import io.circe._
import io.circe.syntax._

object WorkplaceSpec extends FunSuite {

  val workplace = "Remote"

  test("Encode workplace") {
    expect(Workplace.SwaggerDoc.example.asJson.noSpaces == s""""$workplace"""")
  }

  test("Decode workplace") {
    expect(Json.fromString(workplace).as[Workplace] == Right(Workplace.SwaggerDoc.example))
  }

  test("Unable to decode an invalid workplace") {
    expect(
      Json.fromString("something").as[Workplace] == Left(
        DecodingFailure(s"Workplace 'something' is not valid.", List())
      )
    )
  }

  test("Creating a workplace") {
    expect(Workplace("Remote").isValid)
  }

  test("It should return an error when workplace is wrong") {
    val invalidWorkplace = "invalid workplace"
    expect(Workplace(invalidWorkplace) == Invalid(s"Workplace '$invalidWorkplace' is not valid."))
  }
}
