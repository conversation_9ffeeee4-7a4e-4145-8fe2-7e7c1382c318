package nl.dpes.job.poster.api.job.v1.job

import io.circe._
import io.circe.parser._
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object LocationSpec extends FunSuite {
  case class Container(location: Location)

  object Container {
    import io.circe.generic.semiauto._
    implicit val containerDecoder: Decoder[Container] = deriveDecoder[Container]
    implicit val containerEncoder: Encoder[Container] = deriveEncoder[Container]
  }

  test("A location as string JSON can be decoded to a zipcode") {
    val json      = """{"location":"1234AB"}"""
    val container = decode[Container](json)
    expect(container == Right(Container(Zipcode("1234AB"))))
  }

  test("A location as zipcode JSON can be decoded to a zipcode") {
    val json      = """{"location":{"Zipcode":"1234AB"}}"""
    val container = decode[Container](json)
    expect(container == Right(Container(Zipcode("1234AB"))))
  }

  test("A location as Zipcode will always be encoded as zipcode JSON") {
    val container = Container(Zipcode("1234AB"))
    expect(container.asJson.noSpaces == """{"location":{"Zipcode":"1234AB"}}""")
  }

  test("A location as geolocation JSON can be decoded to a geolocation") {
    val json      = """{"location":{"Geolocation":{"longitude":4.0,"latitude":52.0}}}"""
    val container = decode[Container](json)
    expect(container == Right(Container(Geolocation(Longitude(4.0), Latitude(52.0)))))
  }

  test("A location as geolocation will be encoded as geolocation JSON") {
    val container = Container(Geolocation(Longitude(4.0), Latitude(52.0)))
    expect(container.asJson.noSpaces == """{"location":{"Geolocation":{"longitude":4.0,"latitude":52.0}}}""")
  }

  test("A location as city JSON can be decoded to a city") {
    val json      = """{"location":{"City":"Amsterdam"}}"""
    val container = decode[Container](json)
    expect(container == Right(Container(City("Amsterdam"))))
  }

  test("A location as city will be encoded as city JSON") {
    val container = Container(City("Amsterdam"))
    expect(container.asJson.noSpaces == """{"location":{"City":"Amsterdam"}}""")
  }

  test("When decoding with multiple options including Geolocation then Geolocation is preferred") {
    val json      = """{"location":{"Geolocation":{"longitude":4.0,"latitude":52.0},"Zipcode":"1234AB", "City":"Amsterdam"}}"""
    val container = decode[Container](json)
    expect(container == Right(Container(Geolocation(Longitude(4.0), Latitude(52.0)))))
  }

  test("When decoding with multiple options including Zipcode but excluding Geolocation then Zipcode is preferred") {
    val json      = """{"location":{"Zipcode":"1234AB","City":"Amsterdam"}}"""
    val container = decode[Container](json)
    expect(container == Right(Container(Zipcode("1234AB"))))
  }

  test("Failing to decode the location will give a readable message") {
    val json      = """{"location":1234}"""
    val container = decode[Container](json)
    expect(container == Left(DecodingFailure("Cannot decode location from '1234'", List())))
  }
}
