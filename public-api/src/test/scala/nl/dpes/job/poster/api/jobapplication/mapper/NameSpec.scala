package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object NameSpec extends FunSuite {

  val nameString    = "someone"
  val nameObj: Name = Name(nameString)

  test("Encode Name") {
    expect(nameObj.asJson.noSpaces == s""""$nameString"""")
  }

  test("Decode Name") {
    expect(Json.fromString(nameString).as[Name] == Right(nameObj))
  }
}
