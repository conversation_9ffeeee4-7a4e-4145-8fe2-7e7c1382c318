package nl.dpes.job.poster.api.service.shared

import cats.implicits._
import weaver._
import io.circe.syntax._
import io.circe.parser._

object CompanySpec extends FunSuite {

  val appCompanyWithWebsite: Option[Company] = Company.SwaggerDoc.example.some

  val companyString =
    """{"name":"ACME corp","address":{"PostalAddress":{"streetNameAndHouseNumber":"mt Lincolnweg 40","city":"Amsterdam","zipCode":"1033SN"}},"companyType":"Direct employer","website":"https://www.nationalevacaturebank.nl/"}"""

  val invalidCompanyString =
    """{"name":"ACME corp","address":{"PostalAddress":{"streetNameAndHouseNumber":"mt Lincolnweg 40","city":"Amsterdam","zipCode":"1033SN"}},"companyType":"someone","website":"https://www.nationalevacaturebank.nl/"}"""

  test("Decode company") {
    expect(decode[Company](companyString) == Right(Company.SwaggerDoc.example))
  }

  test("Encode company") {
    expect(Company.SwaggerDoc.example.asJson.noSpaces == companyString)
  }

  test("Creating a company with website") {
    expect(
      Company(
        "ACME corp",
        PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
        CompanyType("Direct employer").toOption.get,
        Website("https://www.nationalevacaturebank.nl/").toOption
      ) == appCompanyWithWebsite.get
    )
  }
}
