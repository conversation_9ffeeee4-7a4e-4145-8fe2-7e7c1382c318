package nl.dpes.job.poster.api.service.apikey.api

import cats.effect.IO
import nl.dpes.job.poster.api.converter.Converter.{ConvertorOps, InvertorOps}
import nl.dpes.job.poster.api.service.apikey.{Integration => Domain}
import weaver.SimpleIOSuite

object IntegrationSpec extends SimpleIOSuite {

  test("integration can be converted to domain") {
    for {
      integration <- IO(Integration("integration name"))
      converted   <- IO(integration.toDomain[Domain])
    } yield expect(converted == Domain("integration name"))
  }

  test("integration can be converted from domain") {
    for {
      integration <- IO(Domain("integration name"))
      converted   <- IO(integration.toApi[Integration])
    } yield expect(converted == Integration("integration name"))
  }
}
