package nl.dpes.job.poster.api.service.addressservice

import cats.effect.IO
import nl.dpes.job.poster.api.job.v1.job.{City, Geolocation, Latitude, Longitude}
import nl.dpes.job.poster.api.service.addressservice.AddressServiceClient.{
  AddressServiceError,
  InvalidData,
  UnsupportedLocale,
  ZipCodeNotFound,
  ZipCodeNotFoundByCityName
}
import org.mockito.MockitoSugar.{mock, when}
import weaver.SimpleIOSuite

object JobAddressServiceSpec extends SimpleIOSuite {

  val addressServiceClient: AddressServiceClient[IO] = mock[AddressServiceClient[IO]]
  val addressService: JobAddressService[IO]          = JobAddressService.impl[IO](addressServiceClient)

  val geolocation: Geolocation = Geolocation(Longitude(52.378), Latitude(4.900))
  val city: City               = City("Duivendrecht")
  val addressInfo: AddressInfo = AddressInfo("1234AB")

  test("It should be able to get zipcode by geolocation") {
    when(addressServiceClient.getZipcodeByGeolocation(geolocation)).thenReturn(IO(addressInfo))
    for {
      addressInfo <- addressService.getZipcodeByGeolocation(geolocation)
    } yield expect(addressInfo == addressInfo)
  }

  test("It should return an error when the client provided invalid input") {
    when(addressServiceClient.getZipcodeByGeolocation(geolocation)).thenReturn(IO.raiseError(InvalidData("Invalid data")))
    for {
      addressInfo <- addressService.getZipcodeByGeolocation(geolocation).attempt
    } yield expect(addressInfo == Left(InvalidData("Invalid data")))
  }

  test("It should return an error when no zipcode was found for the geolocation") {
    when(addressServiceClient.getZipcodeByGeolocation(geolocation)).thenReturn(IO.raiseError(ZipCodeNotFound(geolocation)))
    for {
      addressInfo <- addressService.getZipcodeByGeolocation(geolocation).attempt
    } yield expect(addressInfo == Left(ZipCodeNotFound(geolocation)))
  }

  test("It should return an error when the locale is not supported") {
    when(addressServiceClient.getZipcodeByGeolocation(geolocation)).thenReturn(IO.raiseError(UnsupportedLocale("Unsupported locale")))
    for {
      addressInfo <- addressService.getZipcodeByGeolocation(geolocation).attempt
    } yield expect(addressInfo == Left(UnsupportedLocale("Unsupported locale")))
  }

  test("It should return an error when the client encounters an error") {
    when(addressServiceClient.getZipcodeByGeolocation(geolocation)).thenReturn(IO.raiseError(AddressServiceError("Error occurred")))
    for {
      addressInfo <- addressService.getZipcodeByGeolocation(geolocation).attempt
    } yield expect(addressInfo == Left(AddressServiceError("Error occurred")))
  }

  test("It should be able to get zipcode by city") {
    when(addressServiceClient.getZipcodeByCity(city)).thenReturn(IO(addressInfo))
    for {
      addressInfo <- addressService.getZipcodeByCity(city)
    } yield expect(addressInfo == addressInfo)
  }

  test("It should return an error when the client provided invalid input") {
    when(addressServiceClient.getZipcodeByCity(city)).thenReturn(IO.raiseError(InvalidData("Invalid data")))
    for {
      addressInfo <- addressService.getZipcodeByCity(city).attempt
    } yield expect(addressInfo == Left(InvalidData("Invalid data")))
  }

  test("It should return an error when no zipcode was found for the city") {
    when(addressServiceClient.getZipcodeByCity(city)).thenReturn(IO.raiseError(ZipCodeNotFoundByCityName(city)))
    for {
      addressInfo <- addressService.getZipcodeByCity(city).attempt
    } yield expect(addressInfo == Left(ZipCodeNotFoundByCityName(city)))
  }

  test("It should return an error when the locale is not supported") {
    when(addressServiceClient.getZipcodeByCity(city)).thenReturn(IO.raiseError(UnsupportedLocale("Unsupported locale")))
    for {
      addressInfo <- addressService.getZipcodeByCity(city).attempt
    } yield expect(addressInfo == Left(UnsupportedLocale("Unsupported locale")))
  }

  test("It should return an error when the client encounters an error") {
    when(addressServiceClient.getZipcodeByCity(city)).thenReturn(IO.raiseError(AddressServiceError("Error occurred")))
    for {
      addressInfo <- addressService.getZipcodeByCity(city).attempt
    } yield expect(addressInfo == Left(AddressServiceError("Error occurred")))
  }
}
