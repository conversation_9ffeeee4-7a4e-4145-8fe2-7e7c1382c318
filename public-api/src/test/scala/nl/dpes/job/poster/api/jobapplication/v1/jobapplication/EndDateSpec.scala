package nl.dpes.job.poster.api.jobapplication.v1.jobapplication

import cats.data.Validated
import cats.data.Validated.{Invalid, Valid}
import weaver.FunSuite

object EndDateSpec extends FunSuite {

  val validEndDatetime: Validated[String, EndDate]      = EndDate(RawDate(EndDate.SwaggerDoc.supEndDateTime.value))
  val unsupportedDateFormat: Validated[String, EndDate] = EndDate(RawDate("12-12-2023"))
  val invalidEndDate: Validated[String, EndDate]        = EndDate(RawDate("invalid"))

  test("Create a end date from a datetime successfully") {
    expect(validEndDatetime == Valid(EndDate.SwaggerDoc.supEndDateTime))
  }

  test("Returns an error if raw date has an unsupported date format") {
    expect(
      unsupportedDateFormat == Invalid(
        s"Cannot parse date '12-12-2023'. The supported format is: '${ISO_INSTANT_PATTERN.replace("'", "")}'."
      )
    )
  }

  test("Returns an error if raw date is invalid") {
    expect(invalidEndDate == Invalid(s"Cannot parse date 'invalid'. The supported format is: '${ISO_INSTANT_PATTERN.replace("'", "")}'."))
  }
}
