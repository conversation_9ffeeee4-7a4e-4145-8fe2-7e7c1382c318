package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.{Invalid, Valid}
import io.circe._
import io.circe.syntax._
import weaver._

object OccupationSpec extends FunSuite {

  val occupation = "Developer"

  test("Encode occupation") {
    expect(Occupation.SwaggerDoc.example.asJson.noSpaces == s""""$occupation"""")
  }

  test("Decode occupation") {
    expect(Json.fromString(occupation).as[Occupation] == Right(Occupation.SwaggerDoc.example))
  }

  test("Unable to decode an invalid occupation") {
    expect(
      Json.fromString("").as[Occupation] == Left(
        DecodingFailure("The 'occupation' field should not be empty.", List())
      )
    )
  }

  test("Creating an occupation") {
    expect(Occupation(occupation) == Valid(Occupation.SwaggerDoc.example))
  }

  test("It should return an error when occupation is wrong") {
    val invalidOccupation = ""
    expect(Occupation(invalidOccupation) == Invalid("The 'occupation' field should not be empty."))
  }
}
