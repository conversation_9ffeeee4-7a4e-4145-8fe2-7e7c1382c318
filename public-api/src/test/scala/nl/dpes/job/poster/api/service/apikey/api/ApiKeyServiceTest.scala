package nl.dpes.job.poster.api.service.apikey.api

import cats.effect.IO
import nl.dpes.job.poster.api.{Forbidden, Unauthorized}
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.apikey.{ApiKeyRepository, ApiKeyTapirService}
import nl.dpes.job.poster.api.service.recruiter.RecruiterService.{RecruiterForbidden, RecruiterUnauthorized}
import nl.dpes.job.poster.api.service.recruiter.{AccessToken, RecruiterService}
import org.mockito.ArgumentMatchersSugar.any
import org.mockito.MockitoSugar.{mock, when}
import weaver._

object ApiKeyServiceTest extends SimpleIOSuite {

  val bearerToken: BearerToken = BearerToken("this-is-a-bearer-token")

  val services: IO[(RecruiterService[IO], ApiKeyRepository[IO], ApiKeyTapirService[IO])] = for {
    recruiterService <- IO(mock[RecruiterService[IO]])
    repository       <- IO(mock[ApiKeyRepository[IO]])
    apiKeyService    <- IO(ApiKeyTapirService(recruiterService, repository))
  } yield (recruiterService, repository, apiKeyService)

  test("Unauthorized Access Handling") {
    for {
      (recruiterService, repository, apiKeyService) <- services
      _ <- IO(
        when(recruiterService.getRecruiter(AccessToken(any[String])))
          .thenReturn(IO.raiseError(RecruiterUnauthorized("RecruiterUnauthorized")))
      )
      result <- apiKeyService.generateKeyForIntegration(bearerToken)(IntegrationInput(Integration("My integration")))
    } yield expect(result == Left(Unauthorized("RecruiterUnauthorized")))
  }

  test("Forbidden Access Handling") {
    for {
      (recruiterService, repository, apiKeyService) <- services
      _ <- IO(
        when(recruiterService.getRecruiter(AccessToken(any[String])))
          .thenReturn(IO.raiseError(RecruiterForbidden("RecruiterForbidden")))
      )
      result <- apiKeyService.generateKey(bearerToken)(()) // for test coverage, convert to generateKeyForIntegration when replaced
    } yield expect(result == Left(Forbidden("RecruiterForbidden")))
  }
}
