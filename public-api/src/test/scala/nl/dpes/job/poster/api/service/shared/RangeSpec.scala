package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import cats.implicits._
import io.circe.{parser, DecodingFailure}
import io.circe.syntax._
import io.circe.parser._

object RangeSpec extends FunSuite {

  val hourRange          = """{"lower":10,"upper":30}"""
  val invalidHourRange   = """{"lower":30,"upper":10}"""
  val salaryRange        = """{"lower":10000,"upper":30000}"""
  val invalidSalaryRange = """{"lower":30000,"upper":10000}"""

  test("Encode hour range") {
    expect(Range.SwaggerDoc.hourExample.asJson.noSpaces == hourRange)
  }
  test("Decoding fails when providing an invalid hour range") {
    expect(
      parser
        .parse(invalidHourRange)
        .flatMap(_.as[Range[Hour]]) == DecodingFailure("Lower value 'Hour(30)' cannot be greater than upper value 'Hour(10)'.", List())
        .asLeft[Range[Hour]]
    )
  }

  test("Decode hour range") {
    expect(decode[Range[Hour]](hourRange) == Right(Range.SwaggerDoc.hourExample))
  }

  test("Creating a hour range") {
    expect(Range[Hour](Hour(10).toOption.get, Hour(30).toOption.get).isValid)
  }

  test("It should return an error when hour range is invalid") {
    expect(
      Range[Hour](Hour(30).toOption.get, Hour(10).toOption.get) == Invalid(
        s"Lower value 'Hour(30)' cannot be greater than upper value 'Hour(10)'."
      )
    )
  }

  test("Creating a salary range") {
    expect(Range[Salary](Salary(10000).toOption.get, Salary(30000).toOption.get).isValid)
  }

  test("It should return an error when salary range is invalid") {
    expect(
      Range[Salary](Salary(30000).toOption.get, Salary(10000).toOption.get) == Invalid(
        s"Lower value 'Salary(30000.0)' cannot be greater than upper value 'Salary(10000.0)'."
      )
    )
  }
}
