package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object JobSeekerIdSpec extends FunSuite {

  val jobSeekerIdString        = "*********"
  val jobSeekerId: JobSeekerId = JobSeekerId(jobSeekerIdString)

  test("Encode JobSeekerId") {
    expect(jobSeekerId.asJson.noSpaces == s""""$jobSeekerIdString"""")
  }

  test("Decode JobSeekerId") {
    expect(Json.fromString(jobSeekerIdString).as[JobSeekerId] == Right(jobSeekerId))
  }
}
