package nl.dpes.job.poster.api.service.shared

import cats.implicits._
import weaver._

object PublicationPeriodSpec extends FunSuite {

  test("An enddate cannot be before startdate") {
    val invalidPeriod = for {
      start  <- Date("2020-12-12").toEither
      end    <- Date("1984-08-04").toEither
      period <- PublicationPeriod(start, end.some).toEither
    } yield period
    expect(invalidPeriod == "End date '1984-08-04' cannot be before start date '2020-12-12'.".asLeft)
  }
}
