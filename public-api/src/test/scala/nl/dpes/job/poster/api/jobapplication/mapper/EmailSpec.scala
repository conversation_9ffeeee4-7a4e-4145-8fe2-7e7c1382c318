package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object EmailSpec extends FunSuite {

  val emailString  = "<EMAIL>"
  val email: Email = Email(emailString)

  test("Encode Email") {
    expect(email.asJson.noSpaces == s""""$emailString"""")
  }

  test("Decode Email") {
    expect(Json.fromString(emailString).as[Email] == Right(email))
  }
}
