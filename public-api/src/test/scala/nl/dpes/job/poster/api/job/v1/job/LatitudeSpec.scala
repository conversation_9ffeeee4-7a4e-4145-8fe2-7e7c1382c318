package nl.dpes.job.poster.api.job.v1.job

import io.circe._
import io.circe.syntax._
import weaver.FunSuite

object LatitudeSpec extends FunSuite {

  val latitudeDouble: Double = 52.379189
  val latitude: Latitude     = Latitude(latitudeDouble)

  test("Encode latitude") {
    expect(latitude.asJson.noSpaces == s"""$latitudeDouble""")
  }

  test("Decode latitude") {
    expect(Json.fromDouble(latitudeDouble).map(_.as[Latitude]).contains(Right(latitude)))
  }
}
