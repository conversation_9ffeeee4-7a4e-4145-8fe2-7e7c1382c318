package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Valid
import cats.implicits._
import io.circe.Json
import io.circe.syntax._
import nl.dpes.job.poster.api.service.Cursor
import weaver._
import nl.dpes.job.poster.api.service.shared.{Html => AppHtml}

object HtmlSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  test("It should strip tags that are not allowed and remove all attributes") {
    val document = """<html><body><div><p id="123">a paragraph with a <a href="#123">link</a>.</p></div></body></html>"""
    expect(
      Html(document).map(_.document) == "<p>a paragraph with a link.</p>".valid
    )
  }

  test("It should remove empty elements") {
    val document = """<p></p><p>  </p><p><br></p><li></li>"""
    expect(
      Html(document).map(_.document) == Valid("")
    )
  }

  test("Html can be encoded to a json string") {
    val document = "<p>A document</p>"
    expect(Html(document).map(_.asJson.noSpaces) == s""""$document"""".valid)
  }

  test("Html can be decoded from a json string") {
    val document = "<p>A document</p>"
    expect(Json.fromString(document).as[Html] == Html(document).toEither) and
    expect(Html(document).isValid)
  }

  test("The documentation is actually valid") {
    expect(Html(Html.SwaggerDoc.example.document).isValid)
  }

  test("Mapping Html from API to Application model") {
    expect(Html.map(Html.SwaggerDoc.example.some) == AppHtml(Html.SwaggerDoc.example.document).some.sequence)
  }

  test("it will remove nested p tags") {
    val document = """<p><p>header</p><p>"""
    val parsed   = """<p>header</p>"""
    expect(Html(document).map(_.asJson.noSpaces) == s""""$parsed"""".valid)
  }

  test("it will remove br tags") {
    val document = """<p>some<br>text<p>"""
    val parsed   = """<p>some</p><p>text<p>"""
    expect(Html(document) == Html(parsed) && Html(document).isValid)
  }

  test("it will remove fancy br tags") {
    val document = """<p>some<br />text<p>"""
    val parsed   = """<p>some</p><p>text<p>"""
    expect(Html(document) == Html(parsed) && Html(document).isValid)
  }

  test("it will wrap text in a p tag") {
    val document = """<body><p>some<br />text</body>"""
    val parsed   = """<p>some</p><p>text<p>"""
    expect(Html(document) == Html(parsed) && Html(document).isValid)
  }

  test("it will handle weird html") {
    // this may be unwanted behaviour
    val document = """<p>some<div>incorrect</div>html</p>"""
    val parsed   = """<p>some</p>incorrecthtml"""
    expect(Html(document) == Html(parsed) && Html(document).isValid)
  }

  test("it will remove invisible characters") {
    val document = """<p>some<br>﻿<br>html</p>"""
    // Just check that the HTML is valid and contains the expected text
    expect(Html(document).isValid) and
    expect(Html(document).map(_.document).exists(_.contains("some")) && Html(document).map(_.document).exists(_.contains("html")))
  }
}
