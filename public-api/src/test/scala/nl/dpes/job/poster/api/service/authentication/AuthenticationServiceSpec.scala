package nl.dpes.job.poster.api.service.authentication

import cats.effect.IO
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.Unauthorized
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.apikey.ApiKeyRepository.ApiKeyEntryNotFound
import nl.dpes.job.poster.api.service.apikey.{ApiKey, ApiKeyRepository}
import nl.dpes.job.poster.api.service.recruiter.RecruiterService
import org.mockito.MockitoSugar.{mock, when}
import weaver.SimpleIOSuite

object AuthenticationServiceSpec extends SimpleIOSuite {

  case class Fixtures(
    apiKeyRepository: ApiKeyRepository[IO],
    recruiterService: RecruiterService[IO],
    authenticationService: AuthenticationService[IO]
  )

  val fixtures: IO[Fixtures] = for {
    apiKeyRepository      <- IO(mock[ApiKeyRepository[IO]])
    recruiterService      <- IO(mock[RecruiterService[IO]])
    authenticationService <- AuthenticationService.impl(apiKeyRepository)
  } yield Fixtures(apiKeyRepository, recruiterService, authenticationService)

  val bearerToken: BearerToken  = BearerToken("1234")
  val apiKey: ApiKey            = ApiKey("1234")
  val recruiterId: SalesForceId = SalesForceId.unsafeApply("recruiter id...")

  test("Fails when API key not found") {
    for {
      fixtures <- fixtures
      _        <- IO(when(fixtures.apiKeyRepository.readRecruiterId(apiKey)).thenReturn(IO.raiseError(ApiKeyEntryNotFound)))
      result   <- fixtures.authenticationService.authenticate(bearerToken)
    } yield expect(result == Left(Unauthorized(s"Unknown API key")))
  }
}
