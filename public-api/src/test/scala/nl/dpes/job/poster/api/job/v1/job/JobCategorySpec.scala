package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Invalid
import cats.syntax.validated._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.job.v1.job.JobCategory.validJobCategories
import weaver._

object JobCategorySpec extends FunSuite {

  val category = "Administratief/Secretarieel"

  test("It fails when an unknown category has been provided") {
    expect(JobCategory("Unknown") == Invalid("Job category 'Unknown' is not valid."))
  }

  test("It succeeds when a known category has been provided") {
    expect(JobCategory(category).isValid)
  }

  test("Job category encode to string") {
    expect(JobCategory(category).map(_.asJson.noSpaces) == s""""$category"""".valid)
  }

  test("Job category decode from string") {
    expect(Json.fromString(category).as[JobCategory] == JobCategory(category).toEither) and
    expect(JobCategory(category).isValid)
  }

  test("Unable to decode an invalid job category") {
    expect(
      Json.fromString("something").as[JobCategory] == Left(
        DecodingFailure("Job category 'something' is not valid.", List())
      )
    )
  }

  test("Valid Swagger examples") {
    expect(validJobCategories contains JobCategory.SwaggerDoc.administratief.category) and
    expect(validJobCategories contains JobCategory.SwaggerDoc.automatisering.category)
  }
}
