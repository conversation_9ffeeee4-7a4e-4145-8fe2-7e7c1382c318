package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.syntax.apply._
import cats.syntax.validated._
import nl.dpes.job.poster.api.service.Cursor
import nl.dpes.job.poster.api.service.shared.{SalaryPeriod => Period}
import weaver.FunSuite

object SalaryRangeSpec extends FunSuite {

  test("Salary ranges lower salary cannot be bigger than its upper salary") {
    val result = (Salary(1000), Salary(500)).tupled.andThen { case (lower, upper) => SalaryRange(lower, upper, None) }

    expect(result == "Lower salary '1000.0' cannot be more than upper salary '500.0'".invalid)
  }

  test("Salary ranges lower salary can be equal to its upper salary") {
    val result = (Sal<PERSON>(1000), Salary(1000)).tupled.andThen { case (lower, upper) => SalaryRange(lower, upper, None) }

    result match {
      case Validated.Valid(_)       => success
      case Validated.Invalid(error) => failure(error)
    }
  }

  test("Salary ranges lower salary can be lower than its upper salary") {
    val result = (<PERSON><PERSON>(500), <PERSON><PERSON>(1000)).tupled.andThen { case (lower, upper) => SalaryRange(lower, upper, None) }

    result match {
      case Validated.Valid(_)       => success
      case Validated.Invalid(error) => failure(error)
    }
  }

  test("The API model can be converted to the domain model") {
    val apiSalaryRange          = (Salary(500), Salary(1000)).tupled.andThen { case (lower, upper) => SalaryRange(lower, upper, None) }
    implicit val cursor: Cursor = Cursor.Root("")

    apiSalaryRange andThen SalaryRange.map match {
      case Validated.Valid(range) =>
        expect(range.lower.salary == 500) and
          expect(range.upper.salary == 1000) and
          expect(range.period == Period.Unspecified)
      case Validated.Invalid(e) => failure(e.toString)
    }
  }
}
