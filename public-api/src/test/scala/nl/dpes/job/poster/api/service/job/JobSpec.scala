package nl.dpes.job.poster.api.service.job

import cats.effect.IO
import cats.effect.unsafe.implicits.global
import cats.syntax.option._
import cats.syntax.either._
import nl.dpes.job.poster.api.defaults.Defaults
import nl.dpes.job.poster.api.job.v1.ControllerService
import nl.dpes.job.poster.api.job.v1.job.Job
import nl.dpes.job.poster.api.service.Cursor
import nl.dpes.job.poster.api.service.addressservice.JobAddressService
import nl.dpes.job.poster.api.service.shared.{
  CareerLevel,
  ContractType,
  ContractTypes,
  EducationLevel,
  EducationLevels,
  IndustryCategories,
  IndustryCategory,
  JobCategories,
  JobCategory,
  Prediction,
  Video
}
import org.mockito.MockitoSugar.mock
import weaver._

object JobSpec extends FunSuite {

  implicit val cursor: Cursor               = Cursor("body")
  val addressService: JobAddressService[IO] = mock[JobAddressService[IO]]

  test("It should return the provided app job without merging with defaults, because all job fields are present") {
    val appJob   = ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync()
    val defaults = Defaults.SwaggerDoc.defaultsExample

    expect(appJob.mergeDefaults(defaults) == appJob)
  }

  test("It should be able to set default optional field, when it's missing in the provided job") {
    val newVideo = Video("https://www.youtube.com/watch?v=-8V6bMjThNo").toOption
    val appJob   = ControllerService.mapJob(Job.SwaggerDoc.jobExample.copy(video = None), addressService).unsafeRunSync()
    val defaults = Defaults.SwaggerDoc.defaultsExample.copy(video = newVideo)
    val expected = appJob.copy(video = newVideo)

    expect(appJob.mergeDefaults(defaults) == expected)
  }

  test("It should be able to merge job when an optional field is missing in job and defaults") {
    val appJob   = ControllerService.mapJob(Job.SwaggerDoc.jobExample.copy(video = None), addressService).unsafeRunSync()
    val defaults = Defaults.SwaggerDoc.defaultsExample.copy(video = None)
    val expected = appJob.copy(video = None)

    expect(appJob.mergeDefaults(defaults) == expected)
  }

  def prediction: Either[String, Prediction] = for {
    predictedJobCategories   <- JobCategory("Juridisch").andThen(juridisch => JobCategories(Set(juridisch))).toEither
    predictedEducationLevels <- EducationLevel("Havo").andThen(havo => EducationLevels(Set(havo))).toEither
    predictedCareerLevel     <- CareerLevel("Ervaren").toEither
    predictedContractTypes   <- ContractType("Freelance").andThen(freelance => ContractTypes(Set(freelance))).toEither
  } yield Prediction(
    predictedJobCategories,
    predictedEducationLevels,
    predictedCareerLevel,
    predictedContractTypes
  )

  test("when industrycategories is available in job then the predicted value will not be used") {
    (for {
      jobIndustryCategories <- IndustryCategory("Techniek").andThen(techniek => IndustryCategories(Set(techniek))).toEither
      prediction            <- prediction
      appJob                <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingIndustryCategories <- appJob
        .copy(industryCategories = jobIndustryCategories.some)
        .mergePrediction(prediction.some)
        .industryCategories
        .asRight
    } yield (resultingIndustryCategories, jobIndustryCategories)) match {
      case Left(value)               => failure(value.toString)
      case Right((actual, original)) => expect(actual == original.some)
    }
  }

  test("when industrycategories is not available in job and no prediction was available there will be no industrycategories") {
    (for {
      appJob <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingIndustryCategories <- appJob
        .copy(industryCategories = none)
        .mergePrediction(None)
        .industryCategories
        .asRight
    } yield resultingIndustryCategories) match {
      case Left(value)   => failure(value.toString)
      case Right(actual) => expect(actual.isEmpty)
    }
  }

  test("when jobcategories is available in job then the predicted value will not be used") {
    (for {
      jobJobCategories <- JobCategory("Consultancy/Advies").andThen(juridisch => JobCategories(Set(juridisch))).toEither
      prediction       <- prediction
      appJob           <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingJobCategories <- appJob
        .copy(jobCategories = jobJobCategories.some)
        .mergePrediction(prediction.some)
        .jobCategories
        .asRight
    } yield (resultingJobCategories, jobJobCategories)) match {
      case Left(value)               => failure(value.toString)
      case Right((actual, original)) => expect(actual == original.some)
    }
  }

  test("when jobcategories is not available in job then the predicted value will be used") {
    (for {
      prediction <- prediction
      appJob     <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingJobCategories <- appJob
        .copy(jobCategories = none)
        .mergePrediction(prediction.some)
        .jobCategories
        .asRight
    } yield (resultingJobCategories, prediction.jobCategories)) match {
      case Left(value)                => failure(value.toString)
      case Right((actual, predicted)) => expect(actual == predicted.some)
    }
  }

  test("when jobcategories is not available in job and no prediction was available there will be no jobcategories") {
    (for {
      appJob <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingJobCategories <- appJob
        .copy(jobCategories = none)
        .mergePrediction(None)
        .jobCategories
        .asRight
    } yield resultingJobCategories) match {
      case Left(value)   => failure(value.toString)
      case Right(actual) => expect(actual.isEmpty)
    }
  }

  test("when educationlevels is available in job then the predicted value will not be used") {
    (for {
      jobEducationLevels <- EducationLevel("Postdoctoraal").andThen(postdoctoraal => EducationLevels(Set(postdoctoraal))).toEither
      prediction         <- prediction
      appJob             <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingEducationLevels <- appJob
        .copy(educationLevels = jobEducationLevels.some)
        .mergePrediction(prediction.some)
        .educationLevels
        .asRight
    } yield (resultingEducationLevels, jobEducationLevels)) match {
      case Left(value)               => failure(value.toString)
      case Right((actual, original)) => expect(actual == original.some)
    }
  }

  test("when educationlevels is not available in job then the predicted value will be used") {
    (for {
      prediction <- prediction
      appJob     <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingEducationLevels <- appJob
        .copy(educationLevels = none)
        .mergePrediction(prediction.some)
        .educationLevels
        .asRight
    } yield (resultingEducationLevels, prediction.educationLevels)) match {
      case Left(value)                => failure(value.toString)
      case Right((actual, predicted)) => expect(actual == predicted.some)
    }
  }

  test("when educationlevels is not available in job and no prediction was available there will be no educationlevels") {
    (for {
      appJob <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingEducationLevels <- appJob
        .copy(educationLevels = none)
        .mergePrediction(None)
        .educationLevels
        .asRight
    } yield resultingEducationLevels) match {
      case Left(value)   => failure(value.toString)
      case Right(actual) => expect(actual.isEmpty)
    }
  }

  test("when careerlevel is available in job then the predicted value will not be used") {
    (for {
      jobCareerLevel <- CareerLevel("Directie").toEither
      prediction     <- prediction
      appJob         <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingCareerLevel <- appJob
        .copy(careerLevel = jobCareerLevel.some)
        .mergePrediction(prediction.some)
        .careerLevel
        .asRight
    } yield (resultingCareerLevel, jobCareerLevel)) match {
      case Left(value)               => failure(value.toString)
      case Right((actual, original)) => expect(actual == original.some)
    }
  }

  test("when careerlevel is not available in job then the predicted value will be used") {
    (for {
      prediction <- prediction
      appJob     <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingCareerLevel <- appJob
        .copy(careerLevel = none)
        .mergePrediction(prediction.some)
        .careerLevel
        .asRight
    } yield (resultingCareerLevel, prediction.careerLevel)) match {
      case Left(value)                => failure(value.toString)
      case Right((actual, predicted)) => expect(actual == predicted.some)
    }
  }

  test("when careerlevel is not available in job and no prediction was available there will be no careerlevel") {
    (for {
      appJob <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingCareerLevel <- appJob
        .copy(careerLevel = none)
        .mergePrediction(None)
        .careerLevel
        .asRight
    } yield resultingCareerLevel) match {
      case Left(value)   => failure(value.toString)
      case Right(actual) => expect(actual.isEmpty)
    }
  }

  test("when contract types field is available in job then the predicted value will not be used") {
    (for {
      jobContractTypes <- ContractType("Vast").andThen(vast => ContractTypes(Set(vast))).toEither
      prediction       <- prediction
      appJob           <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingContractTypes <- appJob
        .copy(contractTypes = jobContractTypes.some)
        .mergePrediction(prediction.some)
        .contractTypes
        .asRight
    } yield (resultingContractTypes, jobContractTypes)) match {
      case Left(value)               => failure(value.toString)
      case Right((actual, original)) => expect(actual == original.some)
    }
  }

  test("when contract types field is not available in job then the predicted value will be used") {
    (for {
      prediction <- prediction
      appJob     <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingContractTypes <- appJob
        .copy(contractTypes = none)
        .mergePrediction(prediction.some)
        .contractTypes
        .asRight
    } yield (resultingContractTypes, prediction.contractTypes)) match {
      case Left(value)                => failure(value.toString)
      case Right((actual, predicted)) => expect(actual == predicted.some)
    }
  }

  test("when contract types field is not available in job and no prediction was available, so there will be no contract types") {
    (for {
      appJob <- Right(ControllerService.mapJob(Job.SwaggerDoc.jobExample, addressService).unsafeRunSync())
      resultingContractTypes <- appJob
        .copy(contractTypes = none)
        .mergePrediction(None)
        .contractTypes
        .asRight
    } yield resultingContractTypes) match {
      case Left(value)   => failure(value.toString)
      case Right(actual) => expect(actual.isEmpty)
    }
  }
}
