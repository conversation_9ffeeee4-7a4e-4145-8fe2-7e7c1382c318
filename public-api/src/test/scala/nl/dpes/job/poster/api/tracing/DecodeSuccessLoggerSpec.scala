package nl.dpes.job.poster.api.tracing

import cats.effect.IO
import cats.syntax.either._
import io.circe._
import io.circe.generic.auto._
import io.circe.parser._
import io.circe.syntax._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.job.v1.job.SummaryStatus.Published
import nl.dpes.job.poster.api.job.v1.job.{Job, JobCreated, JobId, JobStatus, JobSummary, JobTitle, JobUpdate, PublicationPeriod}
import nl.dpes.job.poster.api.{Conflict, ErrorMessage}
import nl.dpes.job.poster.api.tracing.DecodeSuccessLogger._
import org.mockito.scalatest.MockitoSugar
import org.typelevel.log4cats.Logger
import sttp.model.StatusCode
import sttp.tapir.model.ServerRequest
import sttp.tapir.server.interpreter.BodyListener
import sttp.tapir.server.model.{ServerResponse, ValuedEndpointOutput}
import weaver.SimpleIOSuite

object DecodeSuccessLoggerSpec extends SimpleIOSuite with MockitoSugar {
  implicit lazy val bodyListener: BodyListener[IO, Any] = mock[BodyListener[IO, Any]]
  case class SomeUnknownType()

  // extracting principal
  test("When extracting an unknown principal then return an error") {
    for {
      implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
      _                             <- IO(when(logger.error(any[String])).thenReturn(IO.unit))
      unknownPrincipal <- DecodeSuccessLogger
        .extractPrincipal[IO, SomeUnknownType](SomeUnknownType())
        .attempt
    } yield expect(unknownPrincipal == Left(UnknownPrincipalType(SomeUnknownType())))
  }

  test("The recruiters id can be extracted from Principal when posting succeeds") {
    for {
      implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
      recruiterId <- DecodeSuccessLogger.extractPrincipal[IO, SalesForceId](
        SalesForceId.unsafeApply("123456123456123")
      )
    } yield expect(recruiterId == Json.fromString("123456123456123AAA"))
  }

  // extracting input
  test("When extracting an unknown input then return an error") {
    for {
      implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
      _                             <- IO(when(logger.error(any[String])).thenReturn(IO.unit))
      unknownInput                  <- DecodeSuccessLogger.extractInput[IO, SomeUnknownType](SomeUnknownType()).attempt
    } yield expect(unknownInput == Left(UnknownInputType(SomeUnknownType())))
  }

  test("Request id and Job can be extracted for posting jobs") {
    for {
      implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
      requestId                     <- IO(RequestId.generate)
      correlationId                 <- IO(CorrelationId.generate)
      job                           <- IO(Job.SwaggerDoc.jobExample)
      input                         <- DecodeSuccessLogger.extractInput[IO, (RequestId, CorrelationId, Job)](requestId, correlationId, job)
    } yield {
      val result = input == RequestIdWithJob(requestId, job).asJson
      expect(result)
    }
  }

  test("Request id, jobid and Job can be extracted for updating jobs") {
    for {
      implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
      requestId                     <- IO(RequestId.generate)
      correlationId                 <- IO(CorrelationId.generate)
      job                           <- IO(JobUpdate.SwaggerDoc.example)
      input <- DecodeSuccessLogger.extractInput[IO, (RequestId, CorrelationId, JobId, JobUpdate)](
        requestId,
        correlationId,
        JobId("Job-ID"),
        job
      )
    } yield {
      val result = input == RequestIdWithJobUpdate(requestId, JobId("Job-ID"), job).asJson
      expect(result)
    }
  }

  test("Request id and JobId can be extracted for getting jobsummary") {
    for {
      implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
      requestId                     <- IO(RequestId.generate)
      correlationId                 <- IO(CorrelationId.generate)
      jobId                         <- IO(JobId("Job-ID"))
      input                         <- DecodeSuccessLogger.extractInput[IO, (RequestId, CorrelationId, JobId)](requestId, correlationId, jobId)
    } yield {
      val result = input == RequestIdWithJobId(requestId, jobId).asJson
      expect(result)
    }
  }

  test("Request id, string jobid and JobStatus can be extracted for updating jobs") {
    for {
      implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
      requestId                     <- IO(RequestId.generate)
      correlationId                 <- IO(CorrelationId.generate)
      jobStatus                     <- IO(JobStatus.Suspend)
      input <- DecodeSuccessLogger.extractInput[IO, (RequestId, CorrelationId, String, JobStatus)](
        requestId,
        correlationId,
        "Job-ID",
        JobStatus.Suspend
      )
    } yield {
      val result = input == RequestIdWithJobStatusUpdate(requestId, JobId("Job-ID"), jobStatus).asJson
      expect(result)
    }
  }

  test("Request id, JobId and JobStatus can be extracted for updating jobs") {
    for {
      implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
      requestId                     <- IO(RequestId.generate)
      correlationId                 <- IO(CorrelationId.generate)
      jobStatus                     <- IO(JobStatus.Suspend)
      input <- DecodeSuccessLogger.extractInput[IO, (RequestId, CorrelationId, JobId, JobStatus)](
        requestId,
        correlationId,
        JobId("Job-ID"),
        JobStatus.Suspend
      )
    } yield {
      val result = input == RequestIdWithJobStatusUpdate(requestId, JobId("Job-ID"), jobStatus).asJson
      expect(result)
    }
  }

  test("Request id and jobid can be extracted for deleting jobs") {
    for {
      implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
      requestId                     <- IO(RequestId.generate)
      correlationId                 <- IO(CorrelationId.generate)
      input <- DecodeSuccessLogger.extractInput[IO, (RequestId, CorrelationId, String)](
        requestId,
        correlationId,
        "Job-ID"
      )
    } yield {
      val result = input == RequestIdWithJobId(requestId, JobId("Job-ID")).asJson
      expect(result)
    }
  }

  // extracting output
  def prepareOutput[O](expectedOutput: O): IO[Json] = for {
    implicit0(logger: Logger[IO]) <- IO(mock[Logger[IO]])
    _                             <- IO(when(logger.error(any[String])).thenReturn(IO.unit))
    source                        <- IO(mock[ValuedEndpointOutput[O]])
    _                             <- IO(when(source.value).thenReturn(expectedOutput))
    response                      <- IO(mock[ServerResponse[Any]])
    _                             <- IO(when(response.source).thenReturn(Some(source)))
    output                        <- DecodeSuccessLogger.extractOutput[IO, Any](response)
  } yield output

  test("When extracting an unknown output then return an error") {
    for {
      unknownOutput <- prepareOutput(SomeUnknownType()).attempt
    } yield expect(unknownOutput == Left(UnknownOutputType(SomeUnknownType())))
  }

  test("When extracting an error the error is logged as the supertype") {
    for {
      error <- prepareOutput(Conflict("some error"))
    } yield expect(error == Conflict("some error").asInstanceOf[ErrorMessage].asJson)
  }

  test("Sometimes tapir prepends the statuscode") {
    for {
      error <- prepareOutput((StatusCode.Conflict, Conflict("some error")))
    } yield expect(error == Conflict("some error").asInstanceOf[ErrorMessage].asJson)
  }

  test("correct posting output is converted to json") {
    for {
      requestId <- IO(RequestId.generate)
      location  <- IO("Some location")
      success   <- prepareOutput((location, requestId))
    } yield expect(success == LocationWithRequestId(location, requestId).asJson)
  }

  test("get JobSummary output is converted to json") {
    for {
      requestId  <- IO(RequestId.generate)
      jobSummary <- IO(JobSummary(JobTitle("JobTitle"), Published, PublicationPeriod.SwaggerDoc.example))
      success    <- prepareOutput((jobSummary, requestId))
    } yield {
      val result = success == JobSummaryWithRequestId(jobSummary, requestId).asJson
      expect(result)
    }
  }

  test("JobCreated output is converted to json") {
    for {
      requestId  <- IO(RequestId.generate)
      location   <- IO("Some location")
      jobCreated <- IO(JobCreated(JobId("Job-ID")))
      success    <- prepareOutput((location, requestId, jobCreated))
    } yield {
      val result = success == JobCreatedWithRequestId(location, JobCreated(JobId("Job-ID")), requestId).asJson
      expect(result)
    }
  }

  test("correct update/delete output it is converted to json") {
    for {
      success <- prepareOutput(())
    } yield expect(success == ().asJson)
  }

  test("combining puts correct data in correct place") {
    for {
      request   <- IO(mock[ServerRequest])
      _         <- IO(when(request.method).thenReturn(sttp.model.Method("POST")))
      _         <- IO(when(request.pathSegments).thenReturn(List("foo", "bar", "baz")))
      principal <- IO("principal".asJson)
      input     <- IO("input".asJson)
      outcome   <- IO("outcome".asJson)
    } yield expect(
      combineJson(
        request,
        principal,
        input,
        outcome
      ).asRight == parse("""{"method":"POST","path":"/foo/bar/baz","recruiterId":"principal","input":"input","outcome":"outcome"}""")
    )
  }

  // make sure the logged message is  not seen as JSON
  // todo: This test is failing, since we have upgraded to log4cats 2.6.0 (scala3 version)
  //  => The functionality is still working, but the test is failing
//  test("Be able to directly log JSON so if anything needs some extra escaping this can be done in a single place") {
//    import io.circe.generic.auto._
//    case class Foo(bar: String)
//
//    for {
//      json   <- IO(Foo("Baz").asJson)
//      logger <- IO(mock[Logger[IO]])
//      _      <- IO.println(s"logger IN TEST: $logger")
//      _      <- IO(when(logger.info(any[String])).thenReturn(IO.unit))
//      _      <- logger.info(json)
//      _      <- IO(verify(logger).info("""ENCODED JSON {"bar":"Baz"}"""))
//      _      <- IO(when(logger.warn(any[String])).thenReturn(IO.unit))
//      _      <- logger.warn(json)
//      _      <- IO(verify(logger).warn("""ENCODED JSON {"bar":"Baz"}"""))
//      _      <- IO(when(logger.error(any[String])).thenReturn(IO.unit))
//      _      <- logger.error(json)
//      _      <- IO(verify(logger).error("""ENCODED JSON {"bar":"Baz"}"""))
//    } yield success
//  }
}
