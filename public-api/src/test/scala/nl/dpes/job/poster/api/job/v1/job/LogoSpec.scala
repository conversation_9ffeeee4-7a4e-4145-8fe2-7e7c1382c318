package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Invalid
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.Cursor
import weaver._
import nl.dpes.job.poster.api.service.shared.{Logo => AppLogo}

object LogoSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val validUrl = "http://google.com"

  test("Encode logo") {
    expect(Logo(validUrl).toOption.get.asJson.noSpaces == s""""$validUrl"""")
  }

  test("Decode logo") {
    expect(Json.fromString(validUrl).as[Logo] == Right(Logo(validUrl).toOption.get))
  }

  test("Unable to decode an invalid logo") {
    expect(Json.fromString("something").as[Logo] == Left(DecodingFailure("java.net.MalformedURLException: no protocol: something", List())))
  }

  test("Creating a logo url") {
    expect(Logo(validUrl).isValid)
  }

  test("It should return an error when logo url is wrong") {
    val invalidUrl = "invalid url"
    expect(Logo(invalidUrl) == Invalid(s"java.net.MalformedURLException: no protocol: $invalidUrl"))
  }

  test("Mapping Logo from API to Application model") {
    val actualAppLogo   = Logo.map(Logo(validUrl).toOption)
    val expectedAppLogo = AppLogo(validUrl).some.sequence

    expect(actualAppLogo == expectedAppLogo)
  }
}
