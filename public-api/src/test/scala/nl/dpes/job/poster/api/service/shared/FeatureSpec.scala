package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.{Invalid, Valid}
import weaver._
import io.circe._
import io.circe.syntax._

object FeatureSpec extends FunSuite {

  val feature = "logo"

  test("Encode feature") {
    expect(Feature.SwaggerDoc.logo.asJson.noSpaces == s""""$feature"""")
  }

  test("Decode feature") {
    expect(Json.fromString(feature).as[Feature] == Right(Feature.SwaggerDoc.logo))
  }

  test("Unable to decode an invalid feature") {
    expect(
      Json.fromString("something").as[Feature] == Left(
        DecodingFailure(s"Feature 'something' is not valid.", List())
      )
    )
  }

  test("Creating a feature") {
    expect(Feature("logo").isValid)
  }

  test("It should return an error when feature is invalid") {
    val invalidFeature = "invalid feature"
    expect(Feature(invalidFeature) == Invalid(s"Feature '$invalidFeature' is not valid."))
  }

  test("A feature can be a booster with a value") {
    val booster = "booster_300"
    expect(Feature(booster).isValid) and expect(Feature(booster).map(_.name) == Valid("booster_300"))
  }
}
