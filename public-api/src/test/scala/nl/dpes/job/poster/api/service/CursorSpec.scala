package nl.dpes.job.poster.api.service

import weaver._

object CursorSpec extends FunSuite {

  test("it starts empty") {
    expect(Cursor("body").toString == "body")
    expect(Cursor("body").root == "body")
  }

  test("it concatenates parts") {
    val cursor = Cursor("body")
    val foobar = cursor("foo")("bar")

    expect(cursor("foo").toString == "foo")
    expect(cursor("foo")("bar").toString == "foo.bar")
    expect(foobar("baz").toString == "foo.bar.baz")
    expect(cursor("foo")("bar")("baz").toString == "foo.bar.baz")
  }

  test("root and fields can be found seperately") {
    expect(Cursor("body")("foo")("bar")("baz").toString == "foo.bar.baz")
    expect(Cursor("body")("foo")("bar")("baz").root == "body")
  }
}
