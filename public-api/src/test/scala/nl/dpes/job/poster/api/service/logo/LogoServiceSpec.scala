package nl.dpes.job.poster.api.service.logo

import cats.effect.{IO, Resource}
import cats.implicits._
import fs2.Stream
import io.grpc.Metadata
import nl.dpes.fileservice.proto.chunk.chunk.Chunk
import nl.dpes.fileservice.proto.joblogo.job_logo.{JobLogoServiceFs2Grpc, PersistJobLogoRes}
import nl.dpes.job.poster.api.service.logo.LogoService.{RetrieveLogoError, SendLogoError}
import nl.dpes.job.poster.api.service.shared.Logo
import org.mockito.ArgumentMatchers._
import org.mockito.MockitoSugar._
import org.mockito.stubbing.ScalaOngoingStubbing
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import sttp.capabilities.WebSockets
import sttp.capabilities.fs2.Fs2Streams
import sttp.client3.Response
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend
import sttp.client3.testing.SttpBackendStub
import sttp.model.StatusCode
import weaver.SimpleIOSuite

object LogoServiceSpec extends SimpleIOSuite {

  implicit val loggerFactory: LoggerFactory[IO] = Slf4jFactory.create[IO]

  test("user errors are caught retrieving logo") {
    for {
      fileServiceClient <- IO(mock[JobLogoServiceFs2Grpc[IO, Metadata]])
      downloadClient <- IO(
        Resource.pure[IO, SttpBackendStub[IO, Fs2Streams[IO] with WebSockets]] {
          AsyncHttpClientFs2Backend.stub[IO].whenAnyRequest.thenRespondNotFound()
        }
      )
      service <- IO(LogoService.impl[IO](fileServiceClient, downloadClient))
      logo    <- IO.fromEither(Logo("http://505.example.org").toEither.leftMap(error => new Throwable(error)))
      result  <- service.store(logo).attempt
    } yield expect(result == Left(RetrieveLogoError(logo)))
  }

  test("user errors are caught storing logo") {
    val stream = fs2.Stream.empty
    case object FileServiceError extends Throwable("INVALID_ARGUMENT: Invalid mime-type: text/html")

    def whenPersistingLogoRaiseTypeError(client: JobLogoServiceFs2Grpc[IO, Metadata]): IO[ScalaOngoingStubbing[IO[PersistJobLogoRes]]] =
      IO(when(client.persistJobLogo(any[Stream[IO, Chunk]], any[Metadata])).thenReturn(IO.raiseError(FileServiceError)))

    val stubbedDownloadClient: IO[Resource[IO, SttpBackendStub[IO, Fs2Streams[IO] with WebSockets]]] = IO(
      Resource.pure[IO, SttpBackendStub[IO, Fs2Streams[IO] with WebSockets]] {
        AsyncHttpClientFs2Backend.stub[IO].whenAnyRequest.thenRespond(Response(stream.asRight, StatusCode.Ok))
      }
    )

    for {
      fileServiceClient <- IO(mock[JobLogoServiceFs2Grpc[IO, Metadata]])
      _                 <- whenPersistingLogoRaiseTypeError(fileServiceClient)
      downloadClient    <- stubbedDownloadClient
      service           <- IO(LogoService.impl[IO](fileServiceClient, downloadClient))
      logo              <- IO.fromEither(Logo("http://505.example.org").toEither.leftMap(error => new Throwable(error)))
      result            <- service.store(logo).attempt
    } yield expect(result == Left(SendLogoError(logo, FileServiceError)))
  }
}
