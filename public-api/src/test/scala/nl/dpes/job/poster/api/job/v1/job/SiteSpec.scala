package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import io.circe._
import io.circe.syntax._
import weaver._

object SiteSpec extends FunSuite {
  test("iol as site succeeds") {
    expect(Site("Intermediair") == Valid(Site.SwaggerDoc.iol))
  }

  test("nvb as site succeeds") {
    expect(Site("Nationale Vacaturebank") == Valid(Site.SwaggerDoc.nvb))
  }

  test("Unknown as site fails") {
    expect(Site("Unknown") == Invalid(s"Site 'Unknown' is not valid."))
  }

  test("Site is encoded to string") {
    expect(""""Intermediair"""" == Site.SwaggerDoc.iol.asJson.noSpaces)
  }

  test("Site is decoded from string") {
    expect(Json.fromString("Intermediair").as[Site] == Site("Intermediair").toEither) and
    expect(Site("Intermediair").isValid)
  }

  test("Unable to decode an invalid site") {
    expect(
      Json.fromString("something").as[Site] == Left(
        DecodingFailure(s"Site 'something' is not valid.", List())
      )
    )
  }
}
