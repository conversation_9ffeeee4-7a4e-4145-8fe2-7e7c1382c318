package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Invalid
import cats.implicits._
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import weaver._
import nl.dpes.job.poster.api.service.shared.{
  Company => AppCompany,
  CompanyType => AppCompanyType,
  PostalAddress => AppPostalAddress,
  Website => AppWebsite
}
import io.circe._
import io.circe.syntax._

object CompanySpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("company")

  val companyString =
    """{"name":"ACME corp","address":{"PostalAddress":{"streetNameAndHouseNumber":"mt Lincolnweg 40","city":"Amsterdam","zipCode":"1033SN"}},"companyType":"Direct employer","website":"https://www.nationalevacaturebank.nl/"}"""

  val invalidCompanyString =
    """{"name":"ACME corp","address":{"PostalAddress":{"streetNameAndHouseNumber":"mt Lincolnweg 40","city":"Amsterdam","zipCode":"1033SN"}},"companyType":"someone","website":"https://www.nationalevacaturebank.nl/"}"""

  val appCompanyWithWebsite: Option[Company] = Company.SwaggerDoc.example.some

  test("Decode company") {
    expect(
      parser
        .parse(companyString)
        .flatMap(_.as[Company]) == Right(Company.SwaggerDoc.example)
    )
  }

  test("Encode company") {
    expect(Company.SwaggerDoc.example.asJson.noSpaces == companyString)
  }

  test("Creating a company with website") {
    expect(
      Company(
        "ACME corp",
        PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
        CompanyType("Direct employer").toOption.get,
        Website("https://www.nationalevacaturebank.nl/").toOption
      ) == appCompanyWithWebsite.get
    )
  }

  test("Mapping Company from API to Application model") {
    val actualAppCompany = Company.map(appCompanyWithWebsite)
    val expectedAppCompany =
      AppCompany(
        "ACME corp",
        AppPostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
        AppCompanyType("Direct employer").toOption.get,
        AppWebsite("https://www.nationalevacaturebank.nl/").toOption
      ).some.valid

    expect(actualAppCompany == expectedAppCompany)
  }

  test("Mapping Company should concatenate errors when invalid data is provided") {
    val actualAppCompany = Company.map(
      appCompanyWithWebsite.map(
        _.copy(
          companyType = CompanyType.SwaggerDoc.example.copy("invalid_type"),
          website = Website.SwaggerDoc.example.copy("invalid_website").some
        )
      )
    )
    val error = Invalid(
      MappingError(
        Cursor("company") ->
        "Company type 'invalid_type' is not valid.java.net.MalformedURLException: no protocol: invalid_website"
      )
    )
    expect(actualAppCompany == error)
  }
}
