package nl.dpes.job.poster.api.service
package jobmanager

import cats.data.Validated
import cats.effect.IO
import cats.syntax.apply._
import cats.syntax.option._
import cats.syntax.validated._
import nl.dpes.b2b.common.CompanyType.DirectEmployer
import nl.dpes.b2b.jobmanager.domain.Occupation.{Dpgco, Isco, Unclassified, Occupations => JobManagerOccupations}
import nl.dpes.b2b.jobmanager.domain.salarytype.{Hourly, Monthly, Unspecified, Yearly}
import nl.dpes.b2b.jobmanager.domain.{
  ApplyViaJobBoard,
  Content,
  ContractType,
  Criteria,
  EducationLevel,
  Hours,
  Huisstijl,
  InitialContract,
  InitialJobContent,
  JobContentLogo,
  JobContentVideo,
  Location,
  PersonName,
  Company => DomainCompany,
  Contact => DomainContact,
  ContactInformation => DomainContactInformation,
  ContractTypes => DomainContractTypes,
  EducationLevels => DomainEducationLevels,
  PostalAddress => DomainPostalAddress,
  Range => DomainRange
}
import nl.dpes.job.poster.api.job.v1.job
import nl.dpes.job.poster.api.job.v1.job.{
  CareerLevel,
  Company,
  Contact,
  ContactInformation,
  EducationLevels,
  Html,
  IndustryCategories,
  JobCategories,
  Logo,
  Name,
  Occupation,
  PostalAddress,
  PublicationPeriod,
  Video,
  ContractTypes => ApiContractTypes,
  Range => ApiRange,
  SalaryRange => ApiSalaryRange,
  Workplace => ApiWorkplace
}
import nl.dpes.job.poster.api.service.job.{Job => AppJob, JobUpdate => AppJobUpdate}
import nl.dpes.job.poster.api.service.logo.LogoKey

import nl.dpes.job.poster.api.service.prediction.OccupationClassification
import nl.dpes.job.poster.api.service.shared
import nl.dpes.job.poster.api.service.shared.{
  ContractTypes,
  Hour,
  Salary,
  SalaryPeriod,
  SalaryRange,
  Workplace,
  ApplyViaJobBoard => AppApplyViaJobBoard,
  Date => AppDate,
  Html => AppHtml,
  Occupation => JobPosterOccupation,
  PublicationPeriod => AppPublicationPeriod,
  Range => ServiceRange,
  Zipcode => AppZipcode
}
import weaver._

import java.time.LocalDate
import scala.concurrent.duration._
import scala.util.Try

object JobManagerAdapterSpec extends SimpleIOSuite {
  import JobManagerAdapter._

  implicit val cursor: Cursor = Cursor("body")

  private val apiCompany: Option[Company] = Company.SwaggerDoc.example.some

  private val apiContactInformation = ContactInformation(
    Contact(
      Name("John", "Doe"),
      "**********".some,
      "<EMAIL>"
    ),
    PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
    "www.example.org".some
  ).some

  val appJob: AppJob = AppJob(
    "Scala developer gezocht".some,
    Html.map(Html.SwaggerDoc.example.some).toOption.flatten,
    Occupation.map(Occupation.SwaggerDoc.example.some).toOption.flatten,
    JobCategories.map(JobCategories.SwaggerDoc.example.some).toOption.flatten,
    IndustryCategories.map(IndustryCategories.SwaggerDoc.example.some).toOption.flatten,
    EducationLevels.map(EducationLevels.SwaggerDoc.example.some).toOption.flatten,
    CareerLevel.map(CareerLevel.SwaggerDoc.example.some).toOption.flatten,
    ApiContractTypes.map(ApiContractTypes.SwaggerDoc.example.some).toOption.flatten,
    ApiWorkplace.map(ApiWorkplace.SwaggerDoc.example.some).toOption.flatten,
    ApiRange.mapWorkingHours(ApiRange.SwaggerDoc.hourExample.some).toOption.flatten,
    ApiSalaryRange.map(ApiSalaryRange.SwaggerDoc.example.some).toOption.flatten,
    AppZipcode("1018LL").some,
    PublicationPeriod.map(PublicationPeriod.SwaggerDoc.example.some).toOption.flatten,
    AppApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
    Logo.map(job.Logo("https://picsum.photos/200/300").toOption).toOption.flatten,
    Video.map(Video.SwaggerDoc.example.some)(cursor("video")).toOption.flatten,
    Company.map(apiCompany).toOption.flatten,
    apiContactInformation.map(ContactInformation.mapToService)
  )

  val appJobUpdate: AppJobUpdate = AppJobUpdate(
    "Scala developer gezocht".some,
    Html.map(Html.SwaggerDoc.example.some).toOption.flatten,
    Occupation.map(Occupation.SwaggerDoc.example.some).toOption.flatten,
    JobCategories.map(JobCategories.SwaggerDoc.example.some).toOption.flatten,
    IndustryCategories.map(IndustryCategories.SwaggerDoc.example.some).toOption.flatten,
    EducationLevels.map(EducationLevels.SwaggerDoc.example.some).toOption.flatten,
    CareerLevel.map(CareerLevel.SwaggerDoc.example.some).toOption.flatten,
    ApiContractTypes.map(ApiContractTypes.SwaggerDoc.example.some).toOption.flatten,
    ApiWorkplace.map(ApiWorkplace.SwaggerDoc.example.some).toOption.flatten,
    ApiRange.mapWorkingHours(ApiRange.SwaggerDoc.hourExample.some).toOption.flatten,
    ApiSalaryRange.map(ApiSalaryRange.SwaggerDoc.example.some).toOption.flatten,
    AppZipcode("1018LL").some,
    PublicationPeriod.map(PublicationPeriod.SwaggerDoc.example.some).toOption.flatten,
    AppApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
    Logo.map(job.Logo("https://picsum.photos/200/300").toOption).toOption.flatten,
    Video.map(Video.SwaggerDoc.example.some)(cursor("video")).toOption.flatten,
    Company.map(apiCompany).toOption.flatten,
    apiContactInformation.map(ContactInformation.mapToService)
  )

  val expected: InitialJobContent = InitialJobContent(
    content = Content(
      title = "Scala developer gezocht",
      description = "This is description",
      functionGroups =
        JobManagerOccupations[Try](Set(Dpgco[Try]("this is a scala developer", Isco[Try]("1012").toOption.get).toOption.get)).get,
      jobCategories = Set("Administratief/Secretarieel", "Automatisering/Internet"),
      industryCategories = Set("Telecom", "Techniek")
    ),
    criteria =
      Criteria(DomainEducationLevels(Set(EducationLevel("HBO"), EducationLevel("WO"), EducationLevel("HAVO"))), Set("Starter"), None),
    contract = InitialContract(
      DomainContractTypes(Set(ContractType("Interim"), ContractType("Stage"), ContractType("Tijdelijk"))),
      DomainRange[Hours](10, 30).toOption,
      Some("Remote"),
      Monthly[Try](10000, 30000).toOption
    ),
    location = Location("NL", "1018LL"),
    publicationPeriod = DomainRange.unsafeApply(LocalDate.of(2019, 10, 24), LocalDate.of(2019, 11, 24)).some,
    applyVia = ApplyViaJobBoard("John", "Doe", "<EMAIL>"),
    logo = Some(JobContentLogo("this-is-a-logo-key", Some("https://picsum.photos/200/300"))),
    video = Some(JobContentVideo("https://www.youtube.com/watch?time_continue=4&v=jobvideo")),
    trackingUrl = None,
    company = DomainCompany(
      name = "ACME corp",
      companyType = DirectEmployer,
      huisstijl = Huisstijl.CompanyDefault,
      description = None,
      address = DomainPostalAddress[Try]("mt Lincolnweg 40", "Amsterdam", "1033SN").toOption,
      website = Some("https://www.nationalevacaturebank.nl/")
    ).some,
    contactInformation = DomainContactInformation(
      contact = DomainContact(
        name = PersonName("John", "Doe"),
        phoneNumber = Some("**********"),
        emailAddress = "<EMAIL>"
      ),
      address = Some(
        DomainPostalAddress.unsafeApply(
          streetNameAndHouseNumber = "mt Lincolnweg 40",
          zipCode = "1033SN",
          city = "Amsterdam"
        )
      ),
      website = Some("www.example.org")
    ),
    customFields = List()
  )

  test("When no publicationperiod is provided a period will be calculated based on today and the maximum duration of the job") {
    for {
      cursor    <- IO(Cursor("PublicationPeriod"))
      startDate <- IO(AppDate("2022-07-06"))
      period    <- IO(startDate.andThen(date => mapToPublicationPeriod(None, 20.days, true, date)(cursor).leftMap(_.getMessage)))
      range     <- IO(Validated.fromEither(DomainRange(LocalDate.of(2022, 7, 6), LocalDate.of(2022, 7, 27))).map(_.some))
    } yield expect(startDate.isValid) and
    expect(period.isValid) and
    expect(range.isValid) and
    expect(period == range)
  }

  test("Pick up the predicted classification when an input occupation was provided") {
    for {
      givenOccupation     <- IO(JobPosterOccupation("Developer").toOption)
      predictedOccupation <- IO(OccupationClassification("6789", "Predicted-Job").some)
      cursor              <- IO(Cursor("Occupation"))
    } yield {
      val actualOccupation = mapToOccupation(givenOccupation, predictedOccupation, cursor)
      expect(
        actualOccupation == JobManagerOccupations[Try](Set(Dpgco[Try]("Predicted-Job", Isco[Try]("6789").get).get)).toDomainValidation("")
      )
    }
  }

  test("Pick up the unclassified input occupation when no predicted occupations were provided") {
    for {
      givenOccupation <- IO(JobPosterOccupation("Developer").toOption)
      cursor          <- IO(Cursor("Occupation"))
    } yield {
      val actualOccupation = mapToOccupation(givenOccupation, None, cursor)
      expect(actualOccupation == JobManagerOccupations[Try](Set(Unclassified("Developer"))).get.valid)
    }
  }

  test("Pick up the predicted classification when no input occupation was provided") {
    for {
      predictedOccupation <- IO(OccupationClassification("6789", "Predicted-Job").some)
      cursor              <- IO(Cursor("Occupation"))
    } yield {
      val actualOccupation = mapToOccupation(None, predictedOccupation, cursor)
      expect(
        actualOccupation == JobManagerOccupations[Try](Set(Dpgco[Try]("Predicted-Job", Isco[Try]("6789").get).get)).toDomainValidation("")
      )
    }
  }

  test("Return an error when nothing is provided") {
    for {
      cursor <- IO(Cursor("Occupation"))
    } yield {
      val result = mapToOccupation(None, None, cursor)
      expect(result == MappingError(cursor("occupation") -> "missing field").invalid)
    }
  }

  test("Return an error when the given occupation is invalid") {
    for {
      givenOccupation <- IO(JobPosterOccupation("").toOption)
      cursor          <- IO(Cursor("Occupation"))
    } yield {
      val result = mapToOccupation(givenOccupation, None, cursor)
      expect(result == MappingError(cursor("occupation") -> "missing field").invalid)
    }
  }

  test("Return an error when the predicted occupation is invalid") {
    for {
      predictedOccupation <- IO(OccupationClassification("invalid", "Engineer").some)
      cursor              <- IO(Cursor("Occupation"))
    } yield {
      val error = mapToOccupation(None, predictedOccupation, cursor)
      expect(error == MappingError(cursor("occupation") -> "Provided id 'invalid' did not match the ISCO id pattern").invalid)
    }
  }

  test("The grpc part should be stripped from the message") {
    for {
      message  <- IO("GRPC server was FAILED_PRECONDITION: 'Status change from 'Expired' to 'Suspended' is not allowed'")
      stripped <- IO(message.cleanGrpcError)
    } yield expect(stripped == "Status change from 'Expired' to 'Suspended' is not allowed")
  }

  test("Return the original message if the grpc part was not available") {
    for {
      message  <- IO("Some other error: 'Status change from 'Expired' to 'Suspended' is not allowed'")
      stripped <- IO(message.cleanGrpcError)
    } yield expect(stripped == "Some other error: 'Status change from 'Expired' to 'Suspended' is not allowed'")
  }

  test("Creating a contract without a salary is permitted") {
    implicit val cursor: Cursor = Cursor("test")

    case class Fail(message: String) extends Throwable(message)

    val noSalary: Option[ServiceRange[Salary]] = None

    def createContract: (
      Validated[String, Option[ContractTypes]],
      Validated[String, Option[ServiceRange[Hour]]],
      Validated[String, None.type],
      Validated[String, Option[Workplace]]
    ) = (
      ContractTypes(Set(nl.dpes.job.poster.api.service.shared.ContractType.SwaggerDoc.tijdelijk)).map(_.some),
      shared.Range[Hour](Hour.SwaggerDoc.minHourExample, Hour.SwaggerDoc.maxHourExample).map(_.some),
      None.valid[String],
      Workplace("Remote").map(_.some)
    )

    val result: Validated[String, Validated[MappingError, InitialContract]] = createContract.mapN(mapToInitialContract)

    for {
      result <- result match {
        case Validated.Valid(a)   => IO.pure(a)
        case Validated.Invalid(e) => IO.raiseError(Fail(e))
      }
    } yield expect(result.isValid && result.isInstanceOf[Validated[MappingError, InitialContract]])
  }

  test("Unspecified salaries can be mapped") {
    implicit val cursor: Cursor = Cursor.Root("")
    for {
      minimumSalary <- Salary(10).toIO
      maximumSalary <- Salary(20).toIO
      salaryRange   <- SalaryRange(minimumSalary, maximumSalary, SalaryPeriod.Unspecified).toIO
      actual        <- mapToSalary(salaryRange).toIO
      expected      <- Unspecified[IO](minimumSalary.salary, maximumSalary.salary)
    } yield expect(actual == expected)
  }

  test("Hourly salaries can be mapped") {
    implicit val cursor: Cursor = Cursor.Root("")
    for {
      minimumSalary <- Salary(10).toIO
      maximumSalary <- Salary(20).toIO
      salaryRange   <- SalaryRange(minimumSalary, maximumSalary, SalaryPeriod.Hour).toIO
      actual        <- mapToSalary(salaryRange).toIO
      expected      <- Hourly[IO](minimumSalary.salary, maximumSalary.salary)
    } yield expect(actual == expected)
  }

  test("Monthly salaries can be mapped") {
    implicit val cursor: Cursor = Cursor.Root("")
    for {
      minimumSalary <- Salary(10).toIO
      maximumSalary <- Salary(20).toIO
      salaryRange   <- SalaryRange(minimumSalary, maximumSalary, SalaryPeriod.Month).toIO
      actual        <- mapToSalary(salaryRange).toIO
      expected      <- Monthly[IO](minimumSalary.salary, maximumSalary.salary)
    } yield expect(actual == expected)
  }

  test("Annual salaries can be mapped") {
    implicit val cursor: Cursor = Cursor.Root("")
    for {
      minimumSalary <- Salary(10).toIO
      maximumSalary <- Salary(20).toIO
      salaryRange   <- SalaryRange(minimumSalary, maximumSalary, SalaryPeriod.Year).toIO
      actual        <- mapToSalary(salaryRange).toIO
      expected      <- Yearly[IO](minimumSalary.salary, maximumSalary.salary)
    } yield expect(actual == expected)
  }

  test("It should be able to map InitialJobContent for posting a job") {
    implicit val cursor: Cursor = Cursor.Root("")

    for {
      actual <- IO.fromEither(
        mapToInitialJobContent(
          job = appJob.copy(
            description = AppHtml("This is description").toOption,
            publicationPeriod = AppPublicationPeriod(AppDate("2019-10-24").toOption.get, AppDate("2019-11-24").toOption).toOption
          ),
          maximumProductDuration = 60 days,
          logoKey = Some(LogoKey("this-is-a-logo-key")),
          classification = Some(OccupationClassification("1012", "this is a scala developer"))
        ).toEither
      )
    } yield expect(actual == expected)
  }

  test("It should be able to map InitialJobContent for updating a job") {
    implicit val cursor: Cursor = Cursor.Root("")

    for {
      actual <- IO.fromEither(
        mapToInitialJobContent(
          job = appJobUpdate.copy(
            description = AppHtml("This is description").toOption,
            publicationPeriod = AppPublicationPeriod(AppDate("2019-10-24").toOption.get, AppDate("2019-11-24").toOption).toOption
          ),
          maximumProductDuration = 60 days,
          logoKey = Some(LogoKey("this-is-a-logo-key")),
          classification = Some(OccupationClassification("1012", "this is a scala developer"))
        ).toEither
      )
    } yield expect(actual == expected)
  }

  implicit class ValidatedToIO[E, A](val value: Validated[E, A]) extends AnyVal {

    def toIO: IO[A] = value match {
      case Validated.Valid(a)   => IO(a)
      case Validated.Invalid(e) => IO.raiseError(new Throwable(e.toString))
    }
  }
}
