package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Invalid
import cats.syntax.validated._
import io.circe._
import io.circe.syntax._
import weaver._

object IndustryCategorySpec extends FunSuite {
  test("it fails when an unknown category has been provided") {
    expect(IndustryCategory("Unknown") == Invalid(s"Industry category 'Unknown' is not valid."))
  }

  test("it succeeds when a known category has been provided") {
    expect(IndustryCategory("Techniek").isValid)
  }

  test("Industry categories encode to string") {
    expect(IndustryCategory("Techniek").map(_.asJson.noSpaces) == """"Techniek"""".valid)
  }

  test("Industry categories decode from string") {
    expect(Json.fromString("Techniek").as[IndustryCategory] == IndustryCategory("Techniek").toEither) and
    expect(IndustryCategory("Techniek").isValid)
  }

  test("Unable to decode an invalid industry category") {
    expect(
      Json.fromString("something").as[IndustryCategory] == Left(
        DecodingFailure(s"Industry category 'something' is not valid.", List())
      )
    )
  }

  test("Swagger examples actually exist") {
    expect(IndustryCategory.validIndustryCategories contains IndustryCategory.SwaggerDoc.telecom.category) and
    expect(IndustryCategory.validIndustryCategories contains IndustryCategory.SwaggerDoc.techniek.category)
  }
}
