package nl.dpes.job.poster.api.shutdownguard

import cats.effect.{IO, Ref}
import cats.implicits._
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.SimpleIOSuite

import scala.concurrent.duration._

object ShutdownGuardSpec extends SimpleIOSuite {
  implicit def loggerFactory: Slf4jFactory[IO] = Slf4jFactory.create[IO]

  test("ShutdownGuard completes all processes") {
    def process(n: Int, results: Ref[IO, List[Int]]): IO[Unit] =
      IO.sleep((n * 100).millis) >> results.update(_ :+ n)

    def runMultipleProcesses(guard: ShutdownGuard[IO], results: Ref[IO, List[Int]]): IO[Unit] =
      IO.uncancelable(_ => (1 to 10).toList.parTraverse(n => guard.run(process(n, results)))).void

    for {
      results <- Ref.of[IO, List[Int]](List.empty)
      runtime <- TestRuntime.impl[IO]

      guardedProcess <- ShutdownGuard[IO, TestRuntime[IO]](runtime).use { guard =>
        (runMultipleProcesses(guard, results), guard.awaitCompletion).parTupled
      }.start

      _      <- runtime.shutdown(500.millis) // shutdown halfway multipleProcesses
      _      <- guardedProcess.join
      result <- results.get
    } yield expect(result.sorted == (1 to 10).toList) // all processes should have completed, order doesn't matter
  }
}
