package nl.dpes.job.poster.api.service.apikey.api

import cats.effect.IO
import nl.dpes.job.poster.api.converter.Converter.{ConvertorOps, InvertorOps}
import nl.dpes.job.poster.api.service.apikey.{ApiKeyId => Domain}
import weaver.SimpleIOSuite

object Api<PERSON>eyIdSpec extends SimpleIOSuite {

  test("apikey can be converted to domain") {
    for {
      apiKeyId  <- IO(ApiKeyId("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
      converted <- IO(apiKeyId.toDomain[Domain])
    } yield expect(converted == Domain("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
  }

  test("apikey can be converted from domain") {
    for {
      apiKeyId  <- IO(Domain("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
      converted <- IO(apiKeyId.toApi[ApiKeyId])
    } yield expect(converted == ApiKeyId("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
  }
}
