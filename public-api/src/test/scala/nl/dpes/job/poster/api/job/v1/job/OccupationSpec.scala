package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import weaver._
import nl.dpes.job.poster.api.service.shared.{Occupation => AppOccupation}

object OccupationSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val occupation = "Developer"

  test("Encode occupation") {
    expect(Occupation.SwaggerDoc.example.asJson.noSpaces == s""""$occupation"""")
  }

  test("Decode occupation") {
    expect(Json.fromString(occupation).as[Occupation] == Right(Occupation.SwaggerDoc.example))
  }

  test("Unable to decode an invalid occupation") {
    expect(
      Json.fromString("").as[Occupation] == Left(
        DecodingFailure("The 'occupation' field should not be empty.", List())
      )
    )
  }

  test("Creating an occupation") {
    expect(Occupation(occupation) == Valid(Occupation.SwaggerDoc.example))
  }

  test("It should return an error when occupation is wrong") {
    val invalidOccupation = ""
    expect(Occupation(invalidOccupation) == Invalid("The 'occupation' field should not be empty."))
  }

  test("Mapping Occupation from API to Application model") {
    val actualAppOccupation   = Occupation.map(Occupation.SwaggerDoc.example.some)
    val expectedAppOccupation = AppOccupation(Occupation.SwaggerDoc.example.group).some.sequence.toValidatedNec

    expect(actualAppOccupation == expectedAppOccupation)
  }

  test("Returning error when unable to map Occupation from API to Application model") {
    val invalidOccupation   = ""
    val actualAppOccupation = Occupation.map(Occupation.SwaggerDoc.example.copy(group = invalidOccupation).some)

    val error = "The 'occupation' field should not be empty."
    expect(actualAppOccupation == MappingError(cursor -> error).invalid)
  }
}
