package nl.dpes.job.poster.api.job.v1.job

import weaver.FunSuite
import cats.syntax.option._
import cats.syntax.validated._
import io.circe.Json
import nl.dpes.job.poster.api.service.Cursor
import nl.dpes.job.poster.api.service.shared.{SalaryPeriod => AppSalaryPeriod}

object SalaryPeriodSpec extends FunSuite {
  test("Hours can be created case insensitive") {
    val hours = List("hour", "HOUR", "Hour", "hOuR")
    hours.foldLeft(success) { case (acc, input) => acc and expect(SalaryPeriod(input) == SalaryPeriod.Hour.valid) }
  }

  test("Months can be created case insensitive") {
    val months = List("month", "MONTH", "Month", "MoNtH")
    months.foldLeft(success) { case (acc, input) => acc and expect(SalaryPeriod(input) == SalaryPeriod.Month.valid) }
  }

  test("Years can be created case insensitive") {
    val years = List("year", "YEAR", "Year", "YeAr")
    years.foldLeft(success) { case (acc, input) => acc and expect(SalaryPeriod(input) == SalaryPeriod.Year.valid) }
  }

  test("Unknown values will result in an error") {
    expect(SalaryPeriod("Some unknown value") == "Cannot get period from 'Some unknown value'".invalid)
  }

  test("A period can be decoded from a string") {
    expect(Json.fromString("month").as[SalaryPeriod] == Right(SalaryPeriod.Month))
  }

  test("A period can be decoded from a string") {
    expect(Json.fromString("month").as[SalaryPeriod] == Right(SalaryPeriod.Month))
  }

  test("the api version can be converted to the domain model") {
    implicit val cursor: Cursor.Root = Cursor.Root("")
    expect(SalaryPeriod.map(SalaryPeriod.Hour) == AppSalaryPeriod.Hour.valid)
  }

  test("an optional api version can be converted to a domain model") {
    implicit val cursor: Cursor.Root = Cursor.Root("")
    expect(SalaryPeriod.map(SalaryPeriod.Hour.some) == AppSalaryPeriod.Hour.valid) and
    expect(SalaryPeriod.map(None) == AppSalaryPeriod.Unspecified.valid)
  }
}
