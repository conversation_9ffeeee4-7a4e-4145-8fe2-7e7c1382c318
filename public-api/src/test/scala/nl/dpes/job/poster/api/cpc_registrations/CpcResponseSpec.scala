package nl.dpes.job.poster.api.cpc_registrations

import io.circe.{Decoder, Encoder, Json}
import io.circe.syntax._
import io.circe.parser.decode
import weaver.FunSuite

import java.util.UUID
import java.time.{Instant, ZoneOffset, ZonedDateTime}
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

object CpcResponseSpec extends FunSuite {

  // Manual encoders and decoders for testing
  implicit val clickIdEncoder: Encoder[ClickId]         = Encoder.encodeString.contramap(_.value)
  implicit val jobIdEncoder: Encoder[JobId]             = Encoder.encodeString.contramap(_.value)
  implicit val referenceIdEncoder: Encoder[ReferenceId] = Encoder.encodeString.contramap(_.value)
  implicit val cpcBucketEncoder: Encoder[CpcBucket]     = Encoder.encodeString.contramap(_.value)
  implicit val timestampEncoder: Encoder[Timestamp]     = Encoder.encodeString.contramap(_.value)

  implicit val cpcResultEncoder: Encoder[CpcResult] = Encoder.instance { result =>
    Json.obj(
      "clickId"     -> result.clickId.asJson,
      "jobId"       -> result.jobId.asJson,
      "referenceId" -> result.referenceId.asJson,
      "cpcBucket"   -> result.cpcBucket.asJson,
      "timestamp"   -> result.timestamp.asJson
    )
  }

  implicit val cpcResponseEncoder: Encoder[CpcResponse] = Encoder.instance { response =>
    Json.obj(
      "results" -> response.results.asJson
    )
  }

  // Helper method to create a test CpcResponse
  private def createTestCpcResponse(): CpcResponse = {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")

    def generateTimestamp(hoursAgo: Int): String = {
      val instant       = Instant.now().minus(hoursAgo, ChronoUnit.HOURS)
      val zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneOffset.UTC)
      formatter.format(zonedDateTime)
    }

    CpcResponse(
      List(
        CpcResult(
          ClickId(UUID.randomUUID().toString),
          JobId(UUID.randomUUID().toString),
          Some(ReferenceId("ref-test-1")),
          CpcBucket("unknown"),
          Timestamp(generateTimestamp(12))
        ),
        CpcResult(
          ClickId(UUID.randomUUID().toString),
          JobId(UUID.randomUUID().toString),
          Some(ReferenceId("ref-test-2")),
          CpcBucket("unknown"),
          Timestamp(generateTimestamp(24))
        )
      )
    )
  }

  test("Encode CpcResponse to JSON") {
    val response = createTestCpcResponse()
    val json     = response.asJson

    // Verify the JSON structure
    expect(json.isObject) and
    expect(json.hcursor.downField("results").focus.exists(_.isArray)) and
    expect(json.hcursor.downField("results").values.exists(_.size == 2))
  }

  test("SwaggerDoc example is valid") {
    val example = CpcResponse.SwaggerDoc.example

    // Verify the example has the expected structure
    expect(example.results.size == 3) and
    expect(example.results.forall(_.clickId.value.nonEmpty)) and
    expect(example.results.forall(_.jobId.value.nonEmpty)) and
    expect(example.results.forall(_.referenceId.isDefined)) and
    expect(example.results.forall(_.cpcBucket.value.nonEmpty)) and
    expect(example.results.forall(_.timestamp.value.nonEmpty))
  }

  test("SwaggerDoc example uses UUID format for IDs") {
    val example = CpcResponse.SwaggerDoc.example

    // Verify clickId and jobId are in UUID format
    val uuidPattern = "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$".r

    val clickIdsValid = example.results.forall { result =>
      uuidPattern.findFirstMatchIn(result.clickId.value).isDefined
    }

    val jobIdsValid = example.results.forall { result =>
      uuidPattern.findFirstMatchIn(result.jobId.value).isDefined
    }

    expect(clickIdsValid) and expect(jobIdsValid)
  }
}
