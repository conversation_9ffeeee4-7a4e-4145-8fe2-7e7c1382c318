package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import io.circe._
import io.circe.syntax._
import weaver._

object HourSpec extends FunSuite {

  val hour = 10

  test("Encode hour") {
    expect(Hour.SwaggerDoc.minHourExample.asJson.noSpaces == s"""$hour""")
  }

  test("Decode hour") {
    expect(Json.fromInt(hour).as[Hour] == Right(Hour.SwaggerDoc.minHourExample))
  }

  test("Unable to decode an hour less than 1") {
    expect(Json.fromInt(-250).as[Hour] == Left(DecodingFailure("Hour '-250' cannot be less than 1.", List())))
  }

  test("Unable to decode an hour greater than 40") {
    expect(Json.fromInt(41).as[Hour] == Left(DecodingFailure("Hour '41' cannot be greater than 40.", List())))
  }

  test("Creating an hour") {
    expect(Hour(hour) == Valid(Hour.SwaggerDoc.minHourExample))
  }
}
