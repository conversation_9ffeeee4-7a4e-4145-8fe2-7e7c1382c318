package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Invalid
import cats.syntax.validated._
import io.circe._
import io.circe.syntax._
import weaver._

object CompanyTypeSpec extends FunSuite {

  val companyType = "Direct employer"

  test("Encode company type") {
    expect(CompanyType.SwaggerDoc.example.asJson.noSpaces == s""""$companyType"""")
  }

  test("Decode company type") {
    expect(Json.fromString(companyType).as[CompanyType] == Right(CompanyType.SwaggerDoc.example))
  }

  test("Unable to decode an invalid company type") {
    expect(
      Json.fromString("someone").as[CompanyType] == Left(
        DecodingFailure(s"Company type 'someone' is not valid.", List())
      )
    )
  }

  test("it fails when an unknown company type has been provided") {
    expect(CompanyType("Unknown") == Invalid(s"Company type 'Unknown' is not valid."))
  }

  test("it succeeds when a known category has been provided") {
    expect(CompanyType("Direct employer").isValid)
  }

  test("Industry categories encode to string") {
    expect(CompanyType("Direct employer").map(_.asJson.noSpaces) == """"Direct employer"""".valid)
  }

  test("Industry categories decode from string") {
    expect(Json.fromString("Direct employer").as[CompanyType] == CompanyType("Direct employer").toEither) and
    expect(CompanyType("Direct employer").isValid)
  }

  test("Swagger examples actually exist") {
    expect(CompanyType.validCompanyTypes contains CompanyType.SwaggerDoc.example.companyType)
  }
}
