package nl.dpes.job.poster.api.service.occupationclassification

import cats.effect.IO
import cats.implicits.catsSyntaxOptionId
import nl.dpes.job.poster.api.defaults.Defaults
import nl.dpes.job.poster.api.service.job.Job
import nl.dpes.job.poster.api.service.occupationclassification.OccupationClassifier.{UnclassifiedPBPJob, Unrecognized<PERSON><PERSON>}
import nl.dpes.job.poster.api.service.prediction
import nl.dpes.job.poster.api.service.prediction.{JobDescription, JobTitle, OccupationClassification, VacancyEnricher}
import nl.dpes.job.poster.api.service.shared.{
  ApplyViaJobBoard,
  Budget,
  CareerLevel,
  Company,
  Contact,
  ContactInformation,
  ContractTypes,
  Date,
  EducationLevels,
  Feature,
  Html,
  IndustryCategories,
  JobCategories,
  JobPosting,
  Logo,
  Name,
  Occupation,
  PerformanceBased,
  PostalAddress,
  Prediction,
  PublicationPeriod,
  Range,
  SalaryRange,
  Site,
  Video,
  Workplace,
  Zipcode
}
import org.mockito.ArgumentMatchers._
import org.mockito.MockitoSugar._
import weaver._

import scala.concurrent.duration.DurationInt

case class Service(
  occupationClassifier: OccupationClassifier[IO],
  vacancyEnricher: VacancyEnricher[IO]
)

object OccupationClassifierSpec extends SimpleIOSuite {

  val predictedClassification: Option[OccupationClassification] = OccupationClassification("5678", "Prediction developer").some

  val jobPosting: JobPosting = JobPosting(
    Set(Site.SwaggerDoc.nvb, Site.SwaggerDoc.iol),
    60.days,
    Set(Feature.SwaggerDoc.logo)
  )

  val performanceBased: PerformanceBased = PerformanceBased(Budget.SwaggerDoc.example)

  val job: Job = Job(
    "Scala developer gezocht".some,
    Html.SwaggerDoc.example.some,
    Occupation.SwaggerDoc.example.some,
    JobCategories.SwaggerDoc.example.some,
    IndustryCategories.SwaggerDoc.example.some,
    EducationLevels.SwaggerDoc.example.some,
    CareerLevel.SwaggerDoc.example.some,
    ContractTypes.SwaggerDoc.example.some,
    Workplace.SwaggerDoc.example.some,
    Range.SwaggerDoc.hourExample.some,
    SalaryRange.SwaggerDoc.example.some,
    Zipcode("1018LL").some,
    PublicationPeriod(Date.SwaggerDoc.start, Date.SwaggerDoc.end.some).toOption,
    ApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
    Logo.SwaggerDoc.example.some,
    Video.SwaggerDoc.example.some,
    Company.SwaggerDoc.example.some,
    ContactInformation(
      Contact(
        Name("John", "Doe"),
        "**********".some,
        "<EMAIL>"
      ),
      PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
      "https://www.nationalevacaturebank.nl".some
    ).some
  )

  val defaults: Defaults = Defaults(
    "Scala developer gezocht".some,
    Html.SwaggerDoc.example.some,
    Occupation.SwaggerDoc.example.some,
    JobCategories.SwaggerDoc.example.some,
    IndustryCategories.SwaggerDoc.example.some,
    EducationLevels.SwaggerDoc.example.some,
    CareerLevel.SwaggerDoc.example.some,
    ContractTypes.SwaggerDoc.example.some,
    Workplace.SwaggerDoc.example.some,
    Range.SwaggerDoc.hourExample.some,
    SalaryRange.SwaggerDoc.example.some,
    Zipcode("1018LL").some,
    ApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
    Logo.SwaggerDoc.example.some,
    Video.SwaggerDoc.example.some,
    Company.SwaggerDoc.example.some,
    JobPosting(
      Set(Site.SwaggerDoc.nvb, Site.SwaggerDoc.iol),
      60.days,
      Set(Feature.SwaggerDoc.logo)
    ).some
  )

  val predicted: prediction.Prediction = prediction.Prediction(
    None,
    List(prediction.JobCategory("Juridisch")),
    prediction.EducationLevel("Havo"),
    prediction.CareerLevel("Directie"),
    prediction.ContractType("Freelance")
  )

  test("It should return the predicted classification for a job") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.vacancyEnricher.predict(JobTitle(anyString()), JobDescription(anyString())))
          .thenAnswer(IO.delay(predicted.copy(ISCO = predictedClassification)))
      )
      result <- serviceWithMocks.occupationClassifier.classify(job, jobPosting.some).attempt
    } yield expect(result == Right(predictedClassification))
  }

  test("It should return no classification if the predicted classification is not provided") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.vacancyEnricher.predict(JobTitle(anyString()), JobDescription(anyString())))
          .thenAnswer(IO.delay(predicted))
      )
      result <- serviceWithMocks.occupationClassifier.classify(job, jobPosting.some).attempt
    } yield expect(result == Right(None))
  }

  test("It should not return a classification if the predicted classification is not provided") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.vacancyEnricher.predict(JobTitle(anyString()), JobDescription(anyString())))
          .thenAnswer(IO.delay(predicted))
      )
      result <- serviceWithMocks.occupationClassifier.classify(job, jobPosting.some).attempt
    } yield expect(result == Right(None))
  }

  test("It should return an error if the PBP job is unclassified") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.vacancyEnricher.predict(JobTitle(anyString()), JobDescription(anyString())))
          .thenAnswer(IO.delay(predicted))
      )
      result <- serviceWithMocks.occupationClassifier.classify(job, performanceBased.some).attempt
    } yield expect(result == Left(UnclassifiedPBPJob))
  }

  test("It should return an error when no occupation was provided and no classification was computed") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.vacancyEnricher.predict(JobTitle(anyString()), JobDescription(anyString())))
          .thenAnswer(IO.delay(predicted))
      )
      result <- serviceWithMocks.occupationClassifier
        .classify(job.copy(occupation = None), jobPosting.some)
        .attempt
    } yield expect(result == Left(UnrecognizedJob))
  }

  def createService(): IO[Service] =
    for {
      vacancyEnricher <- IO(mock[VacancyEnricher[IO]])
      service         <- IO(OccupationClassifier[IO](vacancyEnricher))
    } yield Service(service, vacancyEnricher)
}
