package nl.dpes.job.poster.api.service.apikey.api

import cats.effect.IO
import nl.dpes.job.poster.api.converter.Converter.{ConvertorOps, InvertorOps}
import nl.dpes.job.poster.api.service.apikey.{ApiKey => Domain}
import weaver.SimpleIOSuite

object Api<PERSON>eySpec extends SimpleIOSuite {

  test("apikey can be converted to domain") {
    for {
      apiKey    <- IO(ApiKey("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
      converted <- IO(apiKey.toDomain[Domain])
    } yield expect(converted == Domain("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
  }

  test("apikey can be converted from domain") {
    for {
      apiKey    <- IO(Domain("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
      converted <- IO(apiKey.toApi[ApiKey])
    } yield expect(converted == ApiKey("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
  }
}
