package nl.dpes.job.poster.api.service.apikey.api

import cats.effect.IO
import nl.dpes.job.poster.api.converter.Converter.{ConvertorOps, InvertorOps}
import nl.dpes.job.poster.api.service.apikey.{CreatedAt => Domain}
import weaver.SimpleIOSuite

import java.sql.Timestamp
import java.time.Instant

object CreatedAtSpec extends SimpleIOSuite {
  val timestamp: Timestamp = Timestamp.from(Instant.now)

  test("created at can be converted to domain") {
    for {
      createdAt <- IO(CreatedAt(timestamp))
      converted <- IO(createdAt.toDomain[Domain])
    } yield expect(converted == Domain(timestamp))
  }

  test("created at can be converted from domain") {
    for {
      createdAt <- IO(Domain(timestamp))
      converted <- IO(createdAt.toApi[CreatedAt])
    } yield expect(converted == CreatedAt(timestamp))
  }
}
