package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import cats.implicits.toTraverseOps
import io.circe.CursorOp.DownArray
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.Cursor
import weaver._
import io.circe.parser.decode

object EducationLevelsSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val educationLevels = """["HBO","WO","HAVO"]"""

  test("Encode education levels") {
    expect(EducationLevels.SwaggerDoc.example.asJson.noSpaces == educationLevels)
  }

  test("Decode education levels") {
    expect(decode[EducationLevels](educationLevels) == Right(EducationLevels.SwaggerDoc.example))
  }

  test("Unable to decode an invalid education levels") {
    expect(
      decode[EducationLevels]("""["something"]""") == Left(
        DecodingFailure(s"Education level 'something' is not valid.", List(DownArray))
      )
    )
  }

  test("Unable to decode education levels with empty value") {
    expect(
      decode[EducationLevels]("""[]""") == Left(
        DecodingFailure("At least 1 level(s) should be chosen", List())
      )
    )
  }

  test("Unable to decode education levels with too many values") {
    expect(
      decode[EducationLevels]("""["HAVO", "VWO", "MBO", "HBO", "WO", "Postdoctoraal"]""") == Left(
        DecodingFailure("At most 5 levels should be chosen", List())
      )
    )
  }

  test("It fails when less than minimum levels have been provided") {
    expect(EducationLevels(Set()) == Invalid("At least 1 level(s) should be chosen"))
  }

  test("It fails when more than maximum levels have been provided") {
    val levels = List(
      EducationLevel("WO"),
      EducationLevel("HBO"),
      EducationLevel("HAVO"),
      EducationLevel("LBO"),
      EducationLevel("VWO"),
      EducationLevel("Postdoctoraal")
    ).sequence.map(_.toSet).andThen(EducationLevels.apply)

    expect(levels == Invalid("At most 5 levels should be chosen"))
  }

  test("It succeeds when a correct amount of levels have been provided") {
    val levels = List(
      EducationLevel("WO"),
      EducationLevel("LBO")
    ).sequence.map(_.toSet).andThen(EducationLevels.apply)

    expect(levels.isValid)
  }
}
