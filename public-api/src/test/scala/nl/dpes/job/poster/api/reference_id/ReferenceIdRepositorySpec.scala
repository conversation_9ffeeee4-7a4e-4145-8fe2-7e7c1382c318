package nl.dpes.job.poster.api.reference_id

import cats.effect.{IO, Resource}
import doobie.implicits._
import doobie.util.transactor.Transactor
import nl.dpes.job.poster.api.database.{DatabaseGenerator, TableName}
import nl.dpes.job.poster.api.reference_id.ReferenceIdRepository._
import weaver._

import java.sql.SQLSyntaxErrorException
import scala.concurrent.duration.DurationInt

object ReferenceIdRepositorySpec extends IOSuite with DatabaseGenerator {

  override type Res = doobie.util.transactor.Transactor[IO]

  override def sharedResource: Resource[IO, Res] = transactor

  def randomTableName: IO[TableName]     = IO.randomUUID.map(uuid => TableName(s"reference_id_$uuid"))
  def randomJobId: IO[JobId]             = IO.randomUUID.map(uuid => JobId(uuid.toString))
  def randomReferenceId: IO[ReferenceId] = IO.randomUUID.map(uuid => ReferenceId(s"referenceId-$uuid"))
  def randomRecruiterId: IO[RecruiterId] = IO.randomUUID.map(uuid => RecruiterId(s"recruiter-$uuid".take(18)))

  test("When the repo is not instantiated there should be no table") { xa =>
    for {
      tableName <- randomTableName
      result <- sql"""select "success" from ${tableName.frag}"""
        .query[String]
        .option
        .transact[IO](xa)
        .attempt
    } yield result match {
      case Left(value) => expect(value.getClass == classOf[SQLSyntaxErrorException])
      case Right(_)    => failure("This test should fail successfully")
    }
  }

  test("When the repo is instantiated there should be a table") { xa =>
    for {
      tableName <- randomTableName
      result <- ReferenceIdRepository[IO](tableName, xa).use(_ =>
        sql"""select "success" from ${tableName.frag}"""
          .query[String]
          .option
          .transact[IO](xa)
      )
    } yield expect(result.isEmpty)
  }

  test("multiple recruiters can have the same referenceId") { xa =>
    for {
      tableName <- randomTableName
      result <- ReferenceIdRepository[IO](tableName, xa).use(repo =>
        for {
          recruiterId1 <- randomRecruiterId
          recruiterId2 <- randomRecruiterId
          referenceId  <- randomReferenceId
          jobId1       <- randomJobId
          jobId2       <- randomJobId
          _            <- repo.store(recruiterId1, referenceId, jobId1)
          _            <- repo.store(recruiterId2, referenceId, jobId2)
          result <- sql"""select count(*) from ${tableName.frag} where referenceId = $referenceId"""
            .query[Int]
            .unique
            .transact[IO](xa)
        } yield result
      )
    } yield expect(result == 2)
  }

  test("reference ids must be unique per recruiter") { xa =>
    for {
      tableName   <- randomTableName
      recruiterId <- randomRecruiterId
      referenceId <- randomReferenceId
      jobId1      <- randomJobId
      result <- ReferenceIdRepository[IO](tableName, xa).use(repo =>
        for {
          _      <- repo.store(recruiterId, referenceId, jobId1)
          result <- repo.store(recruiterId, referenceId, jobId1).attempt
        } yield result
      )
    } yield result match {
      case Left(value) =>
        expect(value == DuplicateReferenceId(recruiterId, referenceId))
      case Right(_) => failure("This test should fail successfully")
    }
  }

  test("job ids must be unique") { xa =>
    for {
      tableName    <- randomTableName
      recruiterId1 <- randomRecruiterId
      recruiterId2 <- randomRecruiterId
      referenceId  <- randomReferenceId
      jobId        <- randomJobId
      result <- ReferenceIdRepository[IO](tableName, xa).use(repo =>
        for {
          _      <- repo.store(recruiterId1, referenceId, jobId)
          result <- repo.store(recruiterId2, referenceId, jobId).attempt
        } yield result
      )
    } yield result match {
      case Left(value) =>
        expect(value == DuplicateJobId(jobId))
      case Right(_) => failure("This test should fail successfully")
    }
  }

  test("stored data can be retrieved") { xa =>
    for {
      tableName   <- randomTableName
      recruiterId <- randomRecruiterId
      referenceId <- randomReferenceId
      jobId       <- randomJobId
      result <- ReferenceIdRepository[IO](tableName, xa).use(repo =>
        for {
          _ <- repo.store(recruiterId, referenceId, jobId)
          result <- sql"""select recruiterId, referenceId, jobId from ${tableName.frag}"""
            .query[(RecruiterId, ReferenceId, JobId)]
            .option
            .transact[IO](xa)
        } yield result
      )
    } yield expect(result.contains((recruiterId, referenceId, jobId)))
  }

  test("It should be able to retrieve referenceId by jobId") { xa =>
    for {
      tableName   <- randomTableName
      referenceId <- randomReferenceId
      result <- ReferenceIdRepository[IO](tableName, xa).use(repo =>
        for {
          recruiterId <- randomRecruiterId
          jobId       <- randomJobId
          _           <- repo.store(recruiterId, referenceId, jobId)
          result      <- repo.getReferenceId(jobId)
        } yield result
      )
    } yield expect(result.contains(referenceId))
  }

  test("It returns None when referenceId has not been found for a specific jobId") { xa =>
    for {
      tableName <- randomTableName
      result <- ReferenceIdRepository[IO](tableName, xa).use(repo =>
        for {
          unknownJobId <- randomJobId
          result       <- repo.getReferenceId(unknownJobId)
        } yield result
      )
    } yield expect(result.isEmpty)
  }

  test("It should be able to get the latest job") { xa =>
    for {
      tableName   <- randomTableName
      referenceId <- randomReferenceId
      recruiterId <- randomRecruiterId
      result <- ReferenceIdRepository[IO](tableName, xa).use(repo =>
        for {
          _      <- repo.store(recruiterId, referenceId, JobId("1"))
          _      <- IO.sleep(1.seconds)
          _      <- repo.store(recruiterId, referenceId, JobId("2"))
          _      <- IO.sleep(1.seconds)
          _      <- repo.store(recruiterId, referenceId, JobId("3"))
          _      <- IO.sleep(1.seconds)
          result <- repo.getLatestJobId(referenceId, recruiterId)
        } yield result
      )
    } yield expect(result.contains(JobId("3")))
  }

  test("It should be able to delete a referenceId with a specific jobId") { xa =>
    for {
      tableName   <- randomTableName
      referenceId <- randomReferenceId
      recruiterId <- randomRecruiterId
      result <- ReferenceIdRepository[IO](tableName, xa).use(repo =>
        for {
          _      <- repo.store(recruiterId, referenceId, JobId("1"))
          _      <- IO.sleep(1.seconds)
          _      <- repo.store(recruiterId, referenceId, JobId("2"))
          _      <- IO.sleep(1.seconds)
          _      <- repo.store(recruiterId, referenceId, JobId("3"))
          _      <- IO.sleep(1.seconds)
          _      <- repo.delete(recruiterId, referenceId, JobId("3"))
          result <- getAll(tableName, xa)
        } yield result
      )
    } yield expect(result == List((recruiterId, referenceId, JobId("1")), (recruiterId, referenceId, JobId("2"))))
  }

  test("It should return unit when trying to delete a non-existent row") { xa =>
    for {
      tableName   <- randomTableName
      referenceId <- randomReferenceId
      recruiterId <- randomRecruiterId
      result <- ReferenceIdRepository[IO](tableName, xa).use(repo =>
        for {
          _      <- repo.store(recruiterId, referenceId, JobId("1"))
          result <- repo.delete(recruiterId, referenceId, JobId("3"))
        } yield result
      )
    } yield expect(result == ())
  }

  test("It should not return a job_id when the provided reference_id does not exist") { xa =>
    for {
      tableName   <- randomTableName
      referenceId <- randomReferenceId
      recruiterId <- randomRecruiterId
      result      <- ReferenceIdRepository[IO](tableName, xa).use(_.getLatestJobId(referenceId, recruiterId))
    } yield expect(result.isEmpty)
  }

  def getAll(tableName: TableName, xa: Transactor[IO]): IO[List[(RecruiterId, ReferenceId, JobId)]] =
    sql"""
      SELECT recruiterId, referenceId, jobId
      FROM ${tableName.frag}
    """
      .query[(RecruiterId, ReferenceId, JobId)]
      .to[List]
      .transact(xa)
}
