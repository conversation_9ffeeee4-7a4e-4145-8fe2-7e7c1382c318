package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.{Invalid, Valid}
import cats.implicits._
import io.circe.CursorOp.DownArray
import io.circe._
import io.circe.parser.decode
import io.circe.syntax._
import weaver._

object IndustryCategoriesSpec extends FunSuite {

  val industryCategories = """["Telecom","Techniek"]"""

  test("Encode industry categories") {
    expect(IndustryCategories.SwaggerDoc.example.asJson.noSpaces == industryCategories)
  }

  test("Decode industry categories") {
    expect(decode[IndustryCategories](industryCategories) == Right(IndustryCategories.SwaggerDoc.example))
  }

  test("Unable to decode an invalid industry categories") {
    expect(
      decode[IndustryCategories]("""["something"]""") == Left(
        DecodingFailure(s"Industry category 'something' is not valid.", List(DownArray))
      )
    )
  }

  test("Able to decode industry categories with empty value") {
    expect(
      decode[IndustryCategories]("""[]""") == Right(
        IndustryCategories.empty
      )
    )
  }

  test("Unable to decode industry categories with too many values") {
    expect(
      decode[IndustryCategories]("""["Telecom","Techniek","Automotive"]""") == Left(
        DecodingFailure("At most 2 categories should be chosen", List())
      )
    )
  }

  test("There may be no industry categories provided") {
    expect(IndustryCategories(Set()) == Valid(IndustryCategories.empty))
  }

  test("It fails when more than maximum categories have been provided") {
    val categories = List(
      IndustryCategory("Techniek"),
      IndustryCategory("Telecom"),
      IndustryCategory("Automotive")
    ).sequence.map(_.toSet).andThen(IndustryCategories.apply)

    expect(categories == Invalid("At most 2 categories should be chosen"))
  }

  test("It succeeds when a correct amount of categories have been provided") {
    val categories = List(
      IndustryCategory("Techniek"),
      IndustryCategory("Automotive")
    ).sequence.map(_.toSet).andThen(IndustryCategories.apply)

    expect(categories.isValid)
  }
}
