package nl.dpes.job.poster.api.cpc_registrations

import cats.effect.{IO, Ref, Resource}
import cats.implicits._
import cats.effect.unsafe.implicits.global
import io.scalaland.chimney.dsl._
import nl.dpes.b2b.jobmanager.service.cpc
import nl.dpes.b2b.jobmanager.service.GrpcJobService
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.reference_id.ReferenceIdService
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.typelevel.log4cats.{LoggerFactory, SelfAwareStructuredLogger}
import org.typelevel.log4cats.noop.NoOpLogger
import weaver.SimpleIOSuite
import nl.dpes.job.poster.api.job.v1.job.{JobId => ApiJobId}
import nl.dpes.job.poster.api.reference_id.{ReferenceId => ApiReferenceId}

import scala.concurrent.Future

object CpcServiceSpec extends SimpleIOSuite {

  implicit val testLoggerFactory: LoggerFactory[IO] =
    new LoggerFactory[IO] {

      override def getLoggerFromName(name: String): SelfAwareStructuredLogger[IO] =
        NoOpLogger[IO]

      override def fromName(name: String): IO[SelfAwareStructuredLogger[IO]] = IO(getLoggerFromName(name))
    }

  test("successful call on the service") {

    val mockGrpcJobService     = mock(classOf[GrpcJobService])
    val mockReferenceIdService = mock(classOf[ReferenceIdService[IO]])

    val connection: Resource[IO, GrpcJobService] = Resource.pure(mockGrpcJobService)

    val recruiterId = SalesForceId.unsafeApply("123456789123456")
    val period      = Period(Timestamp("2025-03-27T14:00:11.693Z").some, Timestamp("2025-03-28T14:00:11.693Z").some)

    val cpcRecord = cpc.CpcRecord(
      cpc.ClickId("a click id"),
      cpc.JobId("job-id"),
      cpc.Cpc("middle"),
      cpc.Timestamp("2025-03-28T11:00:11.693Z")
    )
    val cpcResultsResponse = List(cpcRecord)

    when(mockGrpcJobService.cpcResultsForRecruiter(any[cpc.CpcResultsRequest]))
      .thenReturn(Future.successful(cpcResultsResponse))

    when(mockReferenceIdService.getReferenceId(any[ApiJobId]))
      .thenReturn(IO.pure(ApiReferenceId("reference-id").some))

    when(mockReferenceIdService.getReferenceId(any[ApiJobId]))
      .thenReturn(IO.pure(ApiReferenceId("reference-id").some))

    when(mockGrpcJobService.cpcResultsForRecruiter(any[cpc.CpcResultsRequest]))
      .thenReturn(Future.successful(cpcResultsResponse))

    for {
      service  <- CpcService.impl[IO](connection, mockReferenceIdService)
      response <- service.getRegisteredApplies(recruiterId)(period)
    } yield expect(response.results.nonEmpty) and
    expect(response.results.head.referenceId.contains(ReferenceId("reference-id")))
  }

  test("when no reference id is known for a job it is not added to the response") {

    val mockGrpcJobService     = mock(classOf[GrpcJobService])
    val mockReferenceIdService = mock(classOf[ReferenceIdService[IO]])

    val connection: Resource[IO, GrpcJobService] = Resource.pure(mockGrpcJobService)

    val recruiterId = SalesForceId.unsafeApply("123456789123456")
    val period      = Period(Timestamp("2025-03-27T14:00:11.693Z").some, Timestamp("2025-03-28T14:00:11.693Z").some)

    val cpcRecord = cpc.CpcRecord(
      cpc.ClickId("a click id"),
      cpc.JobId("job-id"),
      cpc.Cpc("middle"),
      cpc.Timestamp("2025-03-28T11:00:11.693Z")
    )
    val cpcResultsResponse = List(cpcRecord)

    when(mockGrpcJobService.cpcResultsForRecruiter(any[cpc.CpcResultsRequest]))
      .thenReturn(Future.successful(cpcResultsResponse))

    when(mockReferenceIdService.getReferenceId(any[ApiJobId]))
      .thenReturn(IO.pure(none))

    for {
      service  <- CpcService.impl[IO](connection, mockReferenceIdService)
      response <- service.getRegisteredApplies(recruiterId)(period)
    } yield expect(response.results.nonEmpty) and
    expect(response.results.head.referenceId.isEmpty)
  }

  test("when a cpc bucket is returned, the corresponding cpc bucket is calculated") {

    val mockGrpcJobService     = mock(classOf[GrpcJobService])
    val mockReferenceIdService = mock(classOf[ReferenceIdService[IO]])

    val connection: Resource[IO, GrpcJobService] = Resource.pure(mockGrpcJobService)

    val recruiterId = SalesForceId.unsafeApply("123456789123456")
    val period      = Period(Timestamp("2025-03-27T14:00:11.693Z").some, Timestamp("2025-03-28T14:00:11.693Z").some)

    val cpcRecord = cpc.CpcRecord(
      cpc.ClickId("a click id"),
      cpc.JobId("job-id"),
      cpc.Cpc("middle"),
      cpc.Timestamp("2025-03-28T11:00:11.693Z")
    )
    val cpcResultsResponse = List(cpcRecord)

    when(mockGrpcJobService.cpcResultsForRecruiter(any[cpc.CpcResultsRequest]))
      .thenReturn(Future.successful(cpcResultsResponse))

    when(mockReferenceIdService.getReferenceId(any[ApiJobId]))
      .thenReturn(IO.pure(none))

    for {
      service  <- CpcService.impl[IO](connection, mockReferenceIdService)
      response <- service.getRegisteredApplies(recruiterId)(period)
    } yield expect(response.results.head.cpcBucket == CpcBucket("middle"))
  }

  test("when a cpc bucket is returned, the cpc bucket is passed through correctly") {

    val mockGrpcJobService     = mock(classOf[GrpcJobService])
    val mockReferenceIdService = mock(classOf[ReferenceIdService[IO]])

    val connection: Resource[IO, GrpcJobService] = Resource.pure(mockGrpcJobService)

    val recruiterId = SalesForceId.unsafeApply("123456789123456")
    val period      = Period(Timestamp("2025-03-27T14:00:11.693Z").some, Timestamp("2025-03-28T14:00:11.693Z").some)

    val cpcRecord = cpc.CpcRecord(
      cpc.ClickId("a click id"),
      cpc.JobId("job-id"),
      cpc.Cpc("high"),
      cpc.Timestamp("2025-03-28T11:00:11.693Z")
    )
    val cpcResultsResponse = List(cpcRecord)

    when(mockGrpcJobService.cpcResultsForRecruiter(any[cpc.CpcResultsRequest]))
      .thenReturn(Future.successful(cpcResultsResponse))

    when(mockReferenceIdService.getReferenceId(any[ApiJobId]))
      .thenReturn(IO.pure(none))

    for {
      service  <- CpcService.impl[IO](connection, mockReferenceIdService)
      response <- service.getRegisteredApplies(recruiterId)(period)
    } yield expect(response.results.head.cpcBucket == CpcBucket("high"))
  }
}
