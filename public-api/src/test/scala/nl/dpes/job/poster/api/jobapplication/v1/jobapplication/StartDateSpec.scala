package nl.dpes.job.poster.api.jobapplication.v1.jobapplication

import cats.data.Validated
import cats.data.Validated.{Invalid, Valid}
import weaver.FunSuite

object StartDateSpec extends FunSuite {

  val validStartDatetime: Validated[String, StartDate]    = StartDate(RawDate(StartDate.SwaggerDoc.supStartDateTime.value))
  val unsupportedDateFormat: Validated[String, StartDate] = StartDate(RawDate("12-12-2023"))
  val invalidStartDate: Validated[String, StartDate]      = StartDate(RawDate("invalid"))

  test("Create a start date from a date successfully") {
    expect(validStartDatetime == Valid(StartDate.SwaggerDoc.supStartDateTime))
  }

  test("Returns an error if raw date has an unsupported date format") {
    expect(
      unsupportedDateFormat == Invalid(
        s"Cannot parse date '12-12-2023'. The supported format is: '${ISO_INSTANT_PATTERN.replace("'", "")}'."
      )
    )
  }

  test("Returns an error if raw date is invalid") {
    expect(invalidStartDate == Invalid(s"Cannot parse date 'invalid'. The supported format is: '${ISO_INSTANT_PATTERN.replace("'", "")}'."))
  }
}
