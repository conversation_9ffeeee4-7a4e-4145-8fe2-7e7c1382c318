package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import weaver._
import nl.dpes.job.poster.api.service.shared.{ContractType => AppContractType}

object ContractTypeSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val contractType = "Interim"

  test("Encode contract type") {
    expect(ContractType.SwaggerDoc.interim.asJson.noSpaces == s""""$contractType"""")
  }

  test("Decode contract type") {
    expect(Json.fromString(contractType).as[ContractType] == Right(ContractType.SwaggerDoc.interim))
  }

  test("Unable to decode an invalid contract type") {
    expect(
      Json.fromString("something").as[ContractType] == Left(
        DecodingFailure(s"Contract type 'something' is not valid.", List())
      )
    )
  }

  test("Creating a contract type") {
    expect(ContractType(contractType) == Valid(ContractType.SwaggerDoc.interim))
  }

  test("It should return an error when contract type is 'Arbeider'") {
    val invalidContract = "Arbeider"
    expect(ContractType(invalidContract) == Invalid(s"Contract type '$invalidContract' is not valid."))
  }

  test("It should return an error when contract type is 'Vakantiejob / studentenjob'") {
    val invalidContract = "Vakantiejob / studentenjob"
    expect(ContractType(invalidContract) == Invalid(s"Contract type '$invalidContract' is not valid."))
  }

  test("Unable to decode 'Arbeider' contract type") {
    val invalidContract = "Arbeider"
    expect(
      Json.fromString(invalidContract).as[ContractType] == Left(
        DecodingFailure(s"Contract type '$invalidContract' is not valid.", List())
      )
    )
  }

  test("Unable to decode 'Vakantiejob / studentenjob' contract type") {
    val invalidContract = "Vakantiejob / studentenjob"
    expect(
      Json.fromString(invalidContract).as[ContractType] == Left(
        DecodingFailure(s"Contract type '$invalidContract' is not valid.", List())
      )
    )
  }

  test("It should return an error when contract type is wrong") {
    val invalidContract = "invalid contract"
    expect(ContractType(invalidContract) == Invalid(s"Contract type '$invalidContract' is not valid."))
  }

  test("Mapping ContractType from API to Application model") {
    val actualAppContractType   = ContractType.map(ContractType.SwaggerDoc.interim.some)
    val expectedAppContractType = AppContractType(ContractType.SwaggerDoc.interim.contractType).some.sequence.toValidatedNec

    expect(actualAppContractType == expectedAppContractType)
  }

  test("Returning error when unable to map ContractType from API to Application model") {
    val invalidType           = "Something invalid"
    val actualAppContractType = ContractType.map(ContractType.SwaggerDoc.interim.copy(contractType = invalidType).some)

    val error = s"Contract type '$invalidType' is not valid."
    expect(actualAppContractType == MappingError(cursor -> error).invalid)
  }
}
