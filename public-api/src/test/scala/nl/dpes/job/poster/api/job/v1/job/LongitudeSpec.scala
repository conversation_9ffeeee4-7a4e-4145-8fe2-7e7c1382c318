package nl.dpes.job.poster.api.job.v1.job

import io.circe._
import io.circe.syntax._
import weaver.FunSuite

object LongitudeSpec extends FunSuite {

  val longitudeDouble: Double = 4.899431
  val longitude: Longitude    = Longitude(longitudeDouble)

  test("Encode longitude") {
    expect(longitude.asJson.noSpaces == s"""$longitudeDouble""")
  }

  test("Decode longitude") {
    expect(Json.fromDouble(longitudeDouble).map(_.as[Longitude]).contains(Right(longitude)))
  }
}
