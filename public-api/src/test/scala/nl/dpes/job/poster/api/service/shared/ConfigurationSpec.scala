package nl.dpes.job.poster.api.service.shared

import io.circe.CursorOp.{DownArray, DownField}
import io.circe._
import io.circe.parser.decode
import io.circe.syntax._
import weaver._

import nl.dpes.job.poster.api.service.Cursor

import scala.concurrent.duration.DurationInt

object ConfigurationSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val jobPostingString =
    """{"publishOn":["Nationale Vacaturebank","Intermediair"],"publicationDuration":60,"features":["logo"]}"""

  val invalidJobPostingString =
    """{"publishOn":["Nationale Vacaturebank","Intermediair"],"publicationDuration":60,"features":["something"]}"""

  val performanceBasedString = """{"budget":500}"""

  val invalidPerformanceBasedString = """{"budget": -500}"""

  val jobPosting: JobPosting = JobPosting(
    Set(Site.SwaggerDoc.nvb, Site.SwaggerDoc.iol),
    60.days,
    Set(Feature.SwaggerDoc.logo)
  )

  val performanceBased: PerformanceBased = PerformanceBased(Budget.SwaggerDoc.example)

  test("Decode JobPosting") {
    expect(decode[JobPosting](jobPostingString) == Right(jobPosting))
  }

  test("Encode JobPosting") {
    expect(jobPosting.asJson.noSpaces == jobPostingString)
  }

  test("Unable to decode an invalid JobPosting") {
    expect(
      decode[JobPosting](invalidJobPostingString) == Left(
        DecodingFailure(s"Feature 'something' is not valid.", List(DownArray, DownField("features")))
      )
    )
  }

  test("Decode PerformanceBased") {
    expect(decode[PerformanceBased](performanceBasedString) == Right(performanceBased))
  }

  test("Encode PerformanceBased") {
    expect(performanceBased.asJson.noSpaces == performanceBasedString)
  }

  test("Unable to decode an invalid PerformanceBased") {
    expect(
      decode[PerformanceBased](invalidPerformanceBasedString) == Left(
        DecodingFailure("Budget '-500' must not be negative.", List(DownField("budget")))
      )
    )
  }
}
