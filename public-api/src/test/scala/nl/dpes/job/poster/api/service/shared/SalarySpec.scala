package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import cats.implicits._
import weaver._
import io.circe._
import io.circe.syntax._

object SalarySpec extends FunSuite {

  val salary: Float = 10000

  test("Decode salary") {
    expect(Salary.SwaggerDoc.minSalaryExample.asJson.noSpaces == s"""$salary""")
  }

  test("Encode salary") {
    Json.fromFloat(salary) match {
      case Some(value) => expect(value.as[Salary] == Right(Salary.SwaggerDoc.minSalaryExample))
      case None        => failure("Failed to create a salary")
    }
  }

  test("Unable to decode an invalid salary") {
    expect(Json.fromInt(-250).as[Salary] == Left(DecodingFailure("Salary '-250.00' cannot be less than 1.00.", List())))
  }

  test("Creating an salary") {
    expect(Salary(10000).isValid)
  }

  test("It should return an error when salary is invalid") {
    val invalidSalary = -1
    expect(<PERSON><PERSON>(invalidSalary) == Invalid(s"Salary '-1.00' cannot be less than 1.00."))
  }
}
