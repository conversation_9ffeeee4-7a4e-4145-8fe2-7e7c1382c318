package nl.dpes.job.poster.api.jobapplication.v1

import cats.data.Validated
import cats.effect.IO
import cats.effect.unsafe.implicits.global
import cats.implicits.{catsSyntaxOptionId, toBifunctorOps}
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.jobapplication.v1.JobApplicationClient.{
  ApplicationNotFound,
  CVDownloadError,
  CVNotFound,
  JobApplicationClientError,
  ServiceNotAvailable
}
import nl.dpes.job.poster.api.jobapplication.v1.jobapplication.{Application, EndDate, RawDate, SearchDateTimeRange, StartDate}
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import nl.dpes.job.poster.api.job.v1.job.{JobId => ApiJobId}
import nl.dpes.job.poster.api.jobapplication.v1.cv.{ApplicationId => CVApplicationId}
import nl.dpes.job.poster.api.jobapplication.mapper.{
  ApplicationId,
  Email,
  JobApplication,
  JobId,
  JobSeeker,
  JobSeekerId,
  JobSeekerName,
  Motivation,
  Name,
  Phone,
  Site,
  Timestamp,
  RecruiterId => MappedRecruiterId
}
import nl.dpes.job.poster.api.service.job.{ReferenceId => MapReferenceId}
import nl.dpes.job.poster.api.reference_id.{ReferenceId, ReferenceIdService}
import nl.dpes.job.poster.api.{Conflict, NotFound, ServiceUnavailable, Unknown}
import nl.dpes.job.poster.api.service.recruiter.AccessToken
import nl.dpes.job.poster.api.tracing.CorrelationId
import org.mockito.ArgumentMatchers.{any, anyString}
import org.mockito.MockitoSugar.{mock, when}
import org.mockito.stubbing.ScalaFirstStubbing
import org.scalatest.Assertions.intercept
import sttp.model.{Header, HeaderNames}
import sttp.tapir.header
import weaver.SimpleIOSuite

case class Input(recruiterId: SalesForceId, searchDateTimeRange: SearchDateTimeRange)
case object Error extends Throwable("Something went wrong")

object JobApplicationServiceSpec extends SimpleIOSuite {

  private val application: Application = Application(
    id = "String",
    recruiterId = "0038E00001JQPCnQAP".some,
    jobId = "String",
    createdAt = 1682951210L,
    jobSeekerId = "String".some,
    jobSeekerFirstName = "String",
    jobSeekerLastName = "String",
    jobSeekerEmail = "String",
    jobSeekerPhone = "String".some,
    motivation = "String".some,
    url = "String",
    filename = "CV.pdf",
    site = "String"
  )

  val jobApplications: Seq[Application] = Seq(
    application,
    Application(
      id = "String",
      recruiterId = "0038E00001JQPCnQAP".some,
      jobId = "String",
      createdAt = 1682951210L,
      jobSeekerId = "String".some,
      jobSeekerFirstName = "String",
      jobSeekerLastName = "String",
      jobSeekerEmail = "String",
      jobSeekerPhone = "String".some,
      motivation = "String".some,
      url = "String",
      filename = "CV.pdf",
      site = "String"
    )
  )

  val mappedJobApplications: Seq[JobApplication] = Seq(
    JobApplication(
      applicationId = ApplicationId("String"),
      recruiterId = MappedRecruiterId("0038E00001JQPCnQAP").some,
      jobId = JobId("String"),
      referenceId = None,
      applicationDate = Timestamp("2023-05-01T16:26:50Z"),
      jobSeeker = JobSeeker(
        id = JobSeekerId("String").some,
        name = JobSeekerName(firstName = Name("String"), lastName = Name("String")),
        email = Email("String"),
        phone = Phone("String").some
      ),
      motivation = Motivation("String").some,
      cv = s"/api/v1/application/${application.id}/cv",
      site = Site("String")
    ),
    JobApplication(
      applicationId = ApplicationId("String"),
      recruiterId = MappedRecruiterId("0038E00001JQPCnQAP").some,
      jobId = JobId("String"),
      referenceId = None,
      applicationDate = Timestamp("2023-05-01T16:26:50Z"),
      jobSeeker = JobSeeker(
        id = JobSeekerId("String").some,
        name = JobSeekerName(firstName = Name("String"), lastName = Name("String")),
        email = Email("String"),
        phone = Phone("String").some
      ),
      motivation = Motivation("String").some,
      cv = s"/api/v1/application/${application.id}/cv",
      site = Site("String")
    )
  )
  val accessToken: AccessToken     = AccessToken("this-is-an-access-token")
  val recruiterId: SalesForceId    = SalesForceId.unsafeApply("0038E00001JQPCnQAP")
  val bearerToken: BearerToken     = BearerToken("bearer_token")
  val correlationId: CorrelationId = CorrelationId("id")
  val startRawDate: RawDate        = RawDate(StartDate.SwaggerDoc.infStartDateTime.value)
  val endRawDate: RawDate          = RawDate(EndDate.SwaggerDoc.supEndDateTime.value)

  val validSearchRange: Validated[String, SearchDateTimeRange] =
    SearchDateTimeRange(StartDate.SwaggerDoc.infStartDateTime, EndDate.SwaggerDoc.supEndDateTime)

  val invalidSearchRange: Validated[String, SearchDateTimeRange] =
    SearchDateTimeRange(StartDate.SwaggerDoc.supStartDateTime, EndDate.SwaggerDoc.infEndDateTime)

  val headers: List[Header] = List(header(HeaderNames.ContentDisposition, s"attachment; filename=${application.filename}").h)

  test("Able to get job applications without referenceIds for specific recruiter and specific search datetime range") {
    for {
      (_, client, service, refService) <- mockClientAndService
      searchRange                      <- IO.fromEither(validSearchRange.toEither.leftMap(ex => new Throwable(ex)))
      _                                <- IO(When the client having (recruiterId, searchRange) thenReturn IO(jobApplications))
      _                                <- IO(when(refService.getReferenceId(any[ApiJobId])).thenReturn(IO.none))
      response                         <- service.getJobApplications(recruiterId)((correlationId, startRawDate, endRawDate.some))
    } yield expect(response == Right(mappedJobApplications))
  }

  test("Able to get job applications with referenceIds for specific recruiter and specific search datetime range") {
    for {
      (_, client, service, refService) <- mockClientAndService
      searchRange                      <- IO.fromEither(validSearchRange.toEither.leftMap(ex => new Throwable(ex)))
      _                                <- IO(When the client having (recruiterId, searchRange) thenReturn IO(jobApplications))
      _                                <- IO(when(refService.getReferenceId(any[ApiJobId])).thenReturn(IO(ReferenceId("123-456-789").some)))
      response                         <- service.getJobApplications(recruiterId)((correlationId, startRawDate, endRawDate.some))
      expectedApplications             <- IO(mappedJobApplications.map(app => app.copy(referenceId = MapReferenceId("123-456-789").some)))
    } yield expect(response == Right(expectedApplications))
  }

  test("Return an error when the client fails to return job applications") {
    for {
      (_, client, service, _) <- mockClientAndService
      searchRange             <- IO.fromEither(validSearchRange.toEither.leftMap(ex => new Throwable(ex)))
      _                       <- IO(When the client having (recruiterId, searchRange) thenReturn IO.raiseError(JobApplicationClientError(Error)))
    } yield {
      val response = intercept[JobApplicationClientError] {
        service.getJobApplications(recruiterId)((correlationId, startRawDate, endRawDate.some)).unsafeRunSync()
      }
      expect(response == JobApplicationClientError(Error))
    }
  }

  test("Return a CV when the client can find a file") {
    for {
      (_, client, service, _) <- mockClientAndService
      _                       <- IO(when(client.getJobApplication(CVApplicationId(any[String]))).thenAnswer(IO.delay(application)))
      _                       <- IO(when(client.downloadFile(any[String])).thenAnswer(IO.delay(new Array[Byte](20))))
      response                <- service.downloadCV(recruiterId)((correlationId, CVApplicationId("some_id")))
    } yield expect(response.isRight && response.map(_._1 == headers) == Right(true))
  }

  test("Return a file_not_found_exception when the client cannot find the desired CV") {
    for {
      (_, client, service, _) <- mockClientAndService
      _                       <- IO(when(client.getJobApplication(CVApplicationId(any[String]))).thenAnswer(IO.delay(application)))
      _                       <- IO(when(client.downloadFile(any[String])).thenAnswer(IO.raiseError(CVNotFound("not found"))))
      response                <- service.downloadCV(recruiterId)((correlationId, CVApplicationId("some_id")))
    } yield expect(response == Left(NotFound("The CV 'not found' was not found.")))
  }

  test("Return an application_not_found_exception when the client cannot find the desired application") {
    for {
      (_, client, service, _) <- mockClientAndService
      _ <- IO(
        when(client.getJobApplication(CVApplicationId(any[String])))
          .thenAnswer(IO.raiseError(ApplicationNotFound(CVApplicationId("not_found_id"))))
      )
      response <- service.downloadCV(recruiterId)((correlationId, CVApplicationId("not_found_id")))
    } yield expect(response == Left(NotFound("Application 'not_found_id' was not found.")))
  }

  test("Return an error when something wrong has occurred while retrieving the application") {
    for {
      (_, client, service, _) <- mockClientAndService
      _                       <- IO(when(client.getJobApplication(CVApplicationId(any[String]))).thenAnswer(IO.raiseError(JobApplicationClientError(Error))))
      response                <- service.downloadCV(recruiterId)((correlationId, CVApplicationId("some_id")))
    } yield expect(response == Left(Unknown("Error occurred when invoking the job application client, caused by: Something went wrong")))
  }

  test("Return an error when the application service is not reachable") {
    for {
      (_, client, service, _) <- mockClientAndService
      _                       <- IO(when(client.getJobApplication(CVApplicationId(any[String]))).thenAnswer(IO.raiseError(ServiceNotAvailable("Error"))))
      response                <- service.downloadCV(recruiterId)((correlationId, CVApplicationId("some_id")))
    } yield expect(response == Left(ServiceUnavailable("The application service is not available.")))
  }

  test("Return an error when something wrong has occurred while downloading the CV") {
    for {
      (_, client, service, _) <- mockClientAndService
      _                       <- IO(when(client.getJobApplication(CVApplicationId(any[String]))).thenAnswer(IO.delay(application)))
      _                       <- IO(when(client.downloadFile(any[String])).thenAnswer(IO.raiseError(CVDownloadError("Error"))))
      response                <- service.downloadCV(recruiterId)((correlationId, CVApplicationId("some_id")))
    } yield expect(response == Left(Unknown("Error occurred when downloading CV, caused by: Error")))
  }

  test("Return an error when an application does not belong to a recruiter") {
    for {
      (_, client, service, _) <- mockClientAndService
      _                       <- IO(when(client.getJobApplication(CVApplicationId(anyString()))).thenAnswer(IO.delay(application.copy(id = "some_id"))))
      response <- service.downloadCV(SalesForceId.unsafeApply("0038E00001KHNMZQA5"))(
        (correlationId, CVApplicationId("some_id"))
      )
    } yield expect(response == Left(Conflict("Application 'some_id' does not belong to recruiter '0038E00001KHNMZQA5'.")))
  }

  test("Return an error when no recruiter is set for application") {
    for {
      (_, client, service, _) <- mockClientAndService
      _ <- IO(
        when(client.getJobApplication(CVApplicationId(anyString())))
          .thenAnswer(IO.delay(application.copy(id = "some_id", recruiterId = None)))
      )
      response <- service.downloadCV(SalesForceId.unsafeApply("0038E00001KHNMZQA5"))(
        (correlationId, CVApplicationId("some_id"))
      )
    } yield expect(response == Left(NotFound("Job Application Recruiter is missing for application 'some_id'.")))
  }

  def mockClientAndService: IO[(AuthenticationService[IO], JobApplicationClient[IO], JobApplicationService[IO], ReferenceIdService[IO])] =
    for {
      jobApplicationClientMock  <- IO(mock[JobApplicationClient[IO]])
      authenticationServiceMock <- IO(mock[AuthenticationService[IO]])
      referenceIdServiceMock    <- IO(mock[ReferenceIdService[IO]])
      jobApplicationService     <- IO(JobApplicationService[IO](jobApplicationClientMock, referenceIdServiceMock))
    } yield (authenticationServiceMock, jobApplicationClientMock, jobApplicationService, referenceIdServiceMock)

  object When {
    def the(client: JobApplicationClient[IO]): StubbedClient = StubbedClient(client)

    case class StubbedClient(client: JobApplicationClient[IO]) extends AnyVal {

      def having(recruiterId: SalesForceId, searchDateTimeRange: SearchDateTimeRange): ScalaFirstStubbing[IO[Seq[Application]]] =
        when(client.getJobApplications(recruiterId, searchDateTimeRange))
    }
  }
}
