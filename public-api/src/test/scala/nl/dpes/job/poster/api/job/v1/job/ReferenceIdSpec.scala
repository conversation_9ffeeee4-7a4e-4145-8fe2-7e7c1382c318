package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.data.Validated.{Invalid, Valid}
import io.circe.{DecodingFailure, Json}
import io.circe.syntax.EncoderOps
import nl.dpes.job.poster.api.job.v1.job.ReferenceId.{EmptyReferenceId, InvalidReferenceIdLength, UrlDecoder}
import weaver.FunSuite

import scala.util.Random

object ReferenceIdSpec extends FunSuite {

  val referenceIdString                                        = "12345-67890"
  val referenceId: Validated[Throwable, ReferenceId]           = ReferenceId(referenceIdString)
  val urlDecodedReferenceId: Validated[Throwable, ReferenceId] = ReferenceId("123//%%??!!456")
  val invalidReferenceIdLength: String                         = Random.alphanumeric.take(101).mkString

  test("Encode reference_id") {
    expect(referenceId.map(_.asJson.noSpaces) == Valid(s""""$referenceIdString""""))
  }

  test("Decode reference_id") {
    expect(Json.fromString(referenceIdString).as[ReferenceId] == Right(referenceId.toOption.get))
  }

  test("Unable to decode an empty reference_id") {
    expect(
      Json.fromString("    ").as[ReferenceId] == Left(
        DecodingFailure(EmptyReferenceId.getMessage, List())
      )
    )
  }

  test("Unable to decode reference_id with invalid length") {
    expect(
      Json.fromString(invalidReferenceIdLength).as[ReferenceId] == Left(
        DecodingFailure(InvalidReferenceIdLength(invalidReferenceIdLength).getMessage, List())
      )
    )
  }

  test("It should encode/decode reference_id url correctly") {
    val actual = for {
      encoded <- urlDecodedReferenceId.toOption.get.asUrlEncoded
      decoded <- encoded.asReferenceId
    } yield decoded

    expect(actual == Right(urlDecodedReferenceId.toOption.get))
  }

  test("Reference_Id should not be empty") {
    expect(ReferenceId("") == Invalid(EmptyReferenceId))
  }

  test("Reference_Id should not contain whitespace-only values") {
    expect(ReferenceId("     ") == Invalid(EmptyReferenceId))
  }

  test("Reference_Id should be trimmed") {
    expect(ReferenceId(" 123-456-789 ") == ReferenceId("123-456-789"))
  }

  test("Reference_Id should not be longer than 100 characters") {
    expect(ReferenceId(invalidReferenceIdLength) == Invalid(InvalidReferenceIdLength(invalidReferenceIdLength)))
  }
}
