package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import io.circe._
import io.circe.syntax._

object SiteSpec extends FunSuite {

  test("Site is encoded to string") {
    expect(""""Intermediair"""" == Site.SwaggerDoc.iol.asJson.noSpaces)
  }

  test("Site is decoded from string") {
    expect(Json.fromString("Intermediair").as[Site] == Site("Intermediair").toEither) and
    expect(Site("Intermediair").isValid)
  }

  test("Unable to decode an invalid site") {
    expect(
      Json.fromString("something").as[Site] == Left(
        DecodingFailure(s"Site 'something' is not valid.", List())
      )
    )
  }

  test("Creating a site") {
    expect(Site("Nationale Vacaturebank").isValid)
  }

  test("It should return an error when site is invalid") {
    val invalidSite = "invalid site"
    expect(Site(invalidSite) == Invalid(s"Site '$invalidSite' is not valid."))
  }
}
