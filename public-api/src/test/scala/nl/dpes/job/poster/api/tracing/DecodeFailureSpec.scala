package nl.dpes.job.poster.api.tracing

import cats.effect.IO
import sttp.tapir.DecodeResult.Error.{JsonDecodeException, JsonError}
import sttp.tapir.{DecodeResult, FieldName}
import weaver.SimpleIOSuite

object DecodeFailureSpec extends SimpleIOSuite {
  test("The tapir failure can be formatted into a DecodeFailure") {
    for {
      originalFailure <- IO(
        DecodeResult.Error(
          "original input",
          JsonDecodeException(
            List(JsonError("An error", List(FieldName("foo bar", "FooBar"), FieldName("baz")))),
            new Throwable("Original exception")
          )
        )
      )
      formattedFailure <- DecodeFailure.fromDecodeResultFailure(originalFailure)
    } yield expect(
      formattedFailure == DecodeFailure(
        "Failed decoding body",
        Map("$.FooBar.baz" -> "An error"),
        "original input"
      )
    )
  }

  test("The tapir failure cannot be formatted into a DecodeFailure when the error has an incorrect type") {
    for {
      originalFailure <- IO(
        DecodeResult.Error(
          "original input",
          new Throwable("Unknown error")
        )
      )
      formattedFailure <- DecodeFailure.fromDecodeResultFailure(originalFailure).attempt
    } yield formattedFailure match {
      case Left(error) =>
        expect(
          error.getMessage == "No JSON decode exception found: class sttp.tapir.DecodeResult$Error - Error(original input,java.lang.Throwable: Unknown error)"
        )
      case Right(value) => failure(s"Error expected, but got '$value'")
    }
  }
  test("The when the FieldName list is empty the element should be a single $ sign") {
    for {
      originalFailure <- IO(
        DecodeResult.Error(
          "original input",
          JsonDecodeException(
            List(JsonError("An error", List())),
            new Throwable("Original exception")
          )
        )
      )
      formattedFailure <- DecodeFailure.fromDecodeResultFailure(originalFailure)
    } yield expect(
      formattedFailure == DecodeFailure(
        "Failed decoding body",
        Map("$" -> "An error"),
        "original input"
      )
    )
  }
  test("The tapir failure can be formatted into a DecodeFailure when the failure is a mismatch") {
    for {
      originalFailure <- IO(
        DecodeResult.Mismatch("GET", "POST")
      )
      formattedFailure <- DecodeFailure.fromDecodeResultFailure(originalFailure)
    } yield expect(
      formattedFailure == DecodeFailure(
        "HTTP method mismatch",
        Map("expected" -> "GET", "actual" -> "POST"),
        "Expected method: GET, but got: POST"
      )
    )
  }
}
