package nl.dpes.job.poster.api.job.v1.job

import io.circe.parser.decode
import io.circe.syntax._
import weaver.FunSuite

object GeolocationSpec extends FunSuite {

  val geolocationJson          = """{"longitude":4.9041,"latitude":52.3676}"""
  val longitude: Longitude     = Longitude(4.9041)
  val latitude: Latitude       = Latitude(52.3676)
  val geolocation: Geolocation = Geolocation(longitude, latitude)

  test("Encode geolocation") {
    expect(geolocation.asJson.noSpaces == s"""$geolocationJson""")
  }

  test("Decode geolocation") {
    expect(decode[Geolocation](geolocationJson) == Right(geolocation))
  }
}
