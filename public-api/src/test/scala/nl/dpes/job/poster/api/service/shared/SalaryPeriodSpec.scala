package nl.dpes.job.poster.api.service.shared

import cats.syntax.validated._
import weaver.FunSuite

object SalaryPeriodSpec extends FunSuite {
  test("Hours can be created case insensitive") {
    val hours = List("hour", "HOUR", "Hour", "hOuR")
    hours.foldLeft(success) { case (acc, input) => acc and expect(SalaryPeriod(input) == SalaryPeriod.Hour.valid) }
  }

  test("Months can be created case insensitive") {
    val months = List("month", "MONTH", "Month", "MoNtH")
    months.foldLeft(success) { case (acc, input) => acc and expect(SalaryPeriod(input) == SalaryPeriod.Month.valid) }
  }

  test("Years can be created case insensitive") {
    val years = List("year", "YEAR", "Year", "YeAr")
    years.foldLeft(success) { case (acc, input) => acc and expect(SalaryPeriod(input) == SalaryPeriod.Year.valid) }
  }

  test("Unspecified can be created case insensitive") {
    val unspecifieds = List("unspecified", "UNSPECIFIED", "Unspecified", "UnSpEcIfIeD")
    unspecifieds.foldLeft(success) { case (acc, input) => acc and expect(SalaryPeriod(input) == SalaryPeriod.Unspecified.valid) }
  }

  test("Unknown values will result in an error") {
    expect(SalaryPeriod("Some unknown value") == "Cannot get period from 'Some unknown value'".invalid)
  }
}
