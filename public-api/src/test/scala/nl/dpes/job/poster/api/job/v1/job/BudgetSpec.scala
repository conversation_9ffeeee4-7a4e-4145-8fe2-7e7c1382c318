package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import io.circe._
import io.circe.syntax._
import weaver._

object BudgetSpec extends FunSuite {

  val budget = 500

  test("Encode budget") {
    expect(Budget.SwaggerDoc.example.asJson.noSpaces == s"""$budget""")
  }

  test("Decode budget") {
    expect(Json.fromInt(budget).as[Budget] == Right(Budget.SwaggerDoc.example))
  }

  test("Unable to decode an invalid budget") {
    expect(Json.fromInt(-500).as[Budget] == Left(DecodingFailure("Budget '-500' must not be negative.", List())))
  }

  test("Creating a budget") {
    expect(Budget(budget) == Valid(Budget.SwaggerDoc.example))
  }

  test("It should return an error when budget is invalid") {
    val invalidBudget = -500
    expect(Budget(invalidBudget) == Invalid(s"Budget '$invalidBudget' must not be negative."))
  }
}
