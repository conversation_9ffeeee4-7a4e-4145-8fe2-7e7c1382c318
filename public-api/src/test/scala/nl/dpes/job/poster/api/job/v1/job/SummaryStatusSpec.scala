package nl.dpes.job.poster.api.job.v1.job

import nl.dpes.job.poster.api.service.shared.JobStatus
import weaver.FunSuite

object SummaryStatusSpec extends FunSuite {

  test("It should return the correct SummaryStatus for a JobStatus") {
    expect(SummaryStatus.fromJobStatus(JobStatus.ValidDraft) == SummaryStatus.ValidDraft) and
    expect(SummaryStatus.fromJobStatus(JobStatus.Expired) == SummaryStatus.Expired) and
    expect(SummaryStatus.fromJobStatus(JobStatus.MultiStepDraft) == SummaryStatus.MultiStepDraft) and
    expect(SummaryStatus.fromJobStatus(JobStatus.Draft) == SummaryStatus.Draft) and
    expect(SummaryStatus.fromJobStatus(JobStatus.Published) == SummaryStatus.Published) and
    expect(SummaryStatus.fromJobStatus(JobStatus.Suspended) == SummaryStatus.Suspended) and
    expect(SummaryStatus.fromJobStatus(JobStatus.Rejected) == SummaryStatus.Rejected) and
    expect(SummaryStatus.fromJobStatus(JobStatus.Approved) == SummaryStatus.Approved) and
    expect(SummaryStatus.fromJobStatus(JobStatus.Submitted) == SummaryStatus.Submitted) and
    expect(SummaryStatus.fromJobStatus(JobStatus.Deleted) == SummaryStatus.Deleted)
  }
}
