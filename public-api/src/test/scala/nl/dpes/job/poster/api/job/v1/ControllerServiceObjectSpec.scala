package nl.dpes.job.poster.api.job.v1

import cats.effect.IO
import cats.effect.unsafe.implicits.global
import cats.implicits.catsSyntaxOptionId
import nl.dpes.job.poster.api.job.v1.job.{
  ApplyViaJobBoard,
  CareerLevel,
  Company,
  Contact,
  ContactInformation,
  ContractTypes,
  EducationLevels,
  Feature,
  Geolocation,
  Hour,
  Html,
  IndustryCategories,
  Job,
  JobCategories,
  JobPosting,
  Latitude,
  Logo,
  Longitude,
  Name,
  Occupation,
  PostalAddress,
  PublicationPeriod,
  Range,
  SalaryRange,
  Site,
  Video,
  Workplace,
  Zipcode
}
import nl.dpes.job.poster.api.service.addressservice.{AddressInfo, JobAddressService}
import nl.dpes.job.poster.api.service.job.{Job => AppJob}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{ApplyViaJobBoard => AppApplyViaJobBoard, Zipcode => AppZipcode}
import org.mockito.MockitoSugar.{mock, when}
import weaver._

import scala.concurrent.duration.DurationInt

object ControllerServiceObjectSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  private val apiCompany: Option[Company] = Company.SwaggerDoc.example.some

  private val apiContactInformation = ContactInformation(
    Contact(
      Name("John", "Doe"),
      "0103456789".some,
      "<EMAIL>"
    ),
    PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
    "www.example.org".some
  ).some

  private val apiConfiguration: Option[JobPosting] = JobPosting(
    Set(Site.SwaggerDoc.nvb, Site.SwaggerDoc.iol),
    60.days,
    Set(Feature.SwaggerDoc.logo)
  ).some

  val validApiJob: Job = Job(
    None,
    "Scala developer gezocht".some,
    Html.SwaggerDoc.example.some,
    Occupation.SwaggerDoc.example.some,
    JobCategories.SwaggerDoc.example.some,
    IndustryCategories.SwaggerDoc.example.some,
    EducationLevels.SwaggerDoc.example.some,
    CareerLevel.SwaggerDoc.example.some,
    ContractTypes.SwaggerDoc.example.some,
    Workplace.SwaggerDoc.example.some,
    Range.SwaggerDoc.hourExample.some,
    SalaryRange.SwaggerDoc.example.some,
    Zipcode("1018LL").some,
    PublicationPeriod.SwaggerDoc.example.some,
    ApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
    Logo("https://picsum.photos/200/300").toOption,
    Video.SwaggerDoc.example.some,
    apiCompany,
    apiContactInformation,
    apiConfiguration
  )

  val geolocation: Geolocation              = Geolocation(Longitude(52.378), Latitude(4.900))
  val addressInfo: AddressInfo              = AddressInfo("1234AB")
  val addressService: JobAddressService[IO] = mock[JobAddressService[IO]]

  val applicationJob: AppJob = AppJob(
    "Scala developer gezocht".some,
    Html.map(Html.SwaggerDoc.example.some).toOption.flatten,
    Occupation.map(Occupation.SwaggerDoc.example.some).toOption.flatten,
    JobCategories.map(JobCategories.SwaggerDoc.example.some).toOption.flatten,
    IndustryCategories.map(IndustryCategories.SwaggerDoc.example.some).toOption.flatten,
    EducationLevels.map(EducationLevels.SwaggerDoc.example.some).toOption.flatten,
    CareerLevel.map(CareerLevel.SwaggerDoc.example.some).toOption.flatten,
    ContractTypes.map(ContractTypes.SwaggerDoc.example.some).toOption.flatten,
    Workplace.map(Workplace.SwaggerDoc.example.some).toOption.flatten,
    Range.mapWorkingHours(Range.SwaggerDoc.hourExample.some).toOption.flatten,
    SalaryRange.map(SalaryRange.SwaggerDoc.example.some).toOption.flatten,
    AppZipcode("1018LL").some,
    PublicationPeriod.map(PublicationPeriod.SwaggerDoc.example.some).toOption.flatten,
    AppApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
    Logo.map(job.Logo("https://picsum.photos/200/300").toOption).toOption.flatten,
    Video.map(Video.SwaggerDoc.example.some)(cursor("video")).toOption.flatten,
    Company.map(apiCompany).toOption.flatten,
    apiContactInformation.map(ContactInformation.mapToService)
  )

  test("Mapping API Job to Application Job with zipcode") {
    expect(ControllerService.mapJob(validApiJob, addressService).unsafeRunSync() == applicationJob)
  }

  test("Mapping API Job to Application Job with geolocation") {
    when(addressService.getZipcodeByGeolocation(geolocation)).thenReturn(IO(addressInfo))
    expect(
      ControllerService.mapJob(validApiJob.copy(location = Some(geolocation)), addressService).unsafeRunSync() ==
        applicationJob.copy(location = AppZipcode("1234AB").some)
    )
  }

  test("Combining errors when trying map API Job to Application Job") {
    when(addressService.getZipcodeByGeolocation(geolocation)).thenReturn(IO(addressInfo))
    val invalidApiJob = validApiJob.copy(
      workingHours = Range.SwaggerDoc.hourExample
        .copy(lower = Hour.SwaggerDoc.maxHourExample.copy(30), upper = Hour.SwaggerDoc.maxHourExample.copy(10))
        .some,
      video = Video.SwaggerDoc.example.copy(url = "video").some
    )
    val actualResult = ControllerService.mapJob(invalidApiJob, addressService).attempt.unsafeRunSync()
    expect(
      actualResult == Left(
        MappingError(
          List(
            Cursor("body")("workingHours") -> "Lower value 'Hour(30)' cannot be greater than upper value 'Hour(10)'.",
            Cursor("body")("video")        -> "Cannot parse url 'video'."
          )
        )
      )
    )
  }
}
