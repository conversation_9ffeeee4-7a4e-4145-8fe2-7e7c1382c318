package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{CareerLevel => AppCareerLevel}
import weaver._

object CareerLevelSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val level = "Starter"

  test("Encode career level") {
    expect(CareerLevel.SwaggerDoc.example.asJson.noSpaces == s""""$level"""")
  }

  test("Decode career level") {
    expect(Json.fromString(level).as[CareerLevel] == Right(CareerLevel.SwaggerDoc.example))
  }

  test("Unable to decode an invalid career level") {
    expect(
      Json.fromString("level").as[CareerLevel] == Left(
        DecodingFailure(s"Career level 'level' is not valid.", List())
      )
    )
  }

  test("Creating a career level") {
    expect(CareerLevel(level) == Valid(CareerLevel.SwaggerDoc.example))
  }

  test("It should return an error when career level is wrong") {
    val invalidLevel = "invalid level"
    expect(CareerLevel(invalidLevel) == Invalid(s"Career level '$invalidLevel' is not valid."))
  }

  test("Mapping CareerLevel from API to Application model") {
    val actualAppCareerLevel   = CareerLevel.map(CareerLevel.SwaggerDoc.example.some)
    val expectedAppCareerLevel = AppCareerLevel(CareerLevel.SwaggerDoc.example.level).some.sequence.toValidatedNec

    expect(actualAppCareerLevel == expectedAppCareerLevel)
  }

  test("Returning error when unable to map CareerLevel from API to Application model") {
    val invalidLevel         = "Something invalid"
    val actualAppCareerLevel = CareerLevel.map(CareerLevel.SwaggerDoc.example.copy(level = invalidLevel).some)

    val error = s"Career level '$invalidLevel' is not valid."
    expect(actualAppCareerLevel == MappingError(cursor -> error).invalid)
  }
}
