package nl.dpes.job.poster.api.jobperformance.v1

import cats.effect.IO
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.job.v1.job.JobId
import nl.dpes.job.poster.api.jobperformance.v1.PerformanceServiceClient.{JobPerformanceError, UnauthorizationError}
import nl.dpes.job.poster.api.reference_id.{ReferenceIdService, ReferenceId => AppReferenceId}
import nl.dpes.job.poster.api.{Unauthorized, Unknown}
import nl.dpes.job.poster.api.service.recruiter.AccessToken
import nl.dpes.job.poster.api.service.job.ReferenceId
import nl.dpes.job.poster.api.tracing.CorrelationId
import org.mockito.MockitoSugar.{mock, when}
import weaver.SimpleIOSuite

case class Service(service: JobPerformanceService[IO], client: PerformanceServiceClient[IO], referenceIdService: ReferenceIdService[IO])

object JobPerformanceServiceSpec extends SimpleIOSuite {

  val accessToken: AccessToken           = AccessToken("token")
  val recruiterId: SalesForceId          = SalesForceId.unsafeApply("0038E00001KHYV4QAP")
  val correlationId: CorrelationId       = CorrelationId.apply("something")
  val jobId: JobId                       = JobId("job_id")
  val performance: Performance           = Performance("job_id", 1, 1, 1)
  val jobPerformance: JobPerformance     = JobPerformance("job_id", None, 1, 1, 1)
  val response: Map[String, Performance] = Map("job_id" -> performance)
  val referenceId: ReferenceId           = ReferenceId("reference_id")
  val appReferenceId: AppReferenceId     = AppReferenceId("reference_id")

  def createService(): IO[Service] =
    for {
      client     <- IO(mock[PerformanceServiceClient[IO]])
      refService <- IO(mock[ReferenceIdService[IO]])
      service    <- IO(JobPerformanceService.impl[IO](client, refService))
    } yield Service(service, client, refService)

  test("It should return job performance for a job with no reference_id") {
    for {
      serviceWithMocks <- createService()
      _                <- IO(when(serviceWithMocks.client.getJobsPerformance(recruiterId)).thenAnswer(IO.delay(response)))
      _                <- IO(when(serviceWithMocks.referenceIdService.getReferenceId(jobId)).thenReturn(IO.none))
      result           <- serviceWithMocks.service.getJobsPerformance(recruiterId)(correlationId)
    } yield expect(result == Right(List(jobPerformance)))
  }

  test("It should return job performance for a job with reference_id") {
    for {
      serviceWithMocks <- createService()
      _                <- IO(when(serviceWithMocks.client.getJobsPerformance(recruiterId)).thenAnswer(IO.delay(response)))
      _                <- IO(when(serviceWithMocks.referenceIdService.getReferenceId(jobId)).thenReturn(IO.some(appReferenceId)))
      result           <- serviceWithMocks.service.getJobsPerformance(recruiterId)(correlationId)
    } yield expect(result == Right(List(jobPerformance.copy(referenceId = Some(referenceId)))))
  }

  test("It should return empty job performance when no metrics have been returned") {
    for {
      serviceWithMocks <- createService()
      _                <- IO(when(serviceWithMocks.client.getJobsPerformance(recruiterId)).thenAnswer(IO.delay(Map.empty[String, Performance])))
      result           <- serviceWithMocks.service.getJobsPerformance(recruiterId)(correlationId)
    } yield expect(result == Right(List.empty[JobPerformance]))
  }

  test("It should return an job_performance_error when job performance client encounters an issue") {
    for {
      serviceWithMocks <- createService()
      _                <- IO(when(serviceWithMocks.client.getJobsPerformance(recruiterId)).thenAnswer(IO.raiseError(JobPerformanceError("Error"))))
      result           <- serviceWithMocks.service.getJobsPerformance(recruiterId)(correlationId)
    } yield expect(result == Left(Unknown("Error occurred when trying to get the jobs performance, due to: Error")))
  }

  test("It should return an unauthorization_error when an unauthorized token was used") {
    for {
      serviceWithMocks <- createService()
      _                <- IO(when(serviceWithMocks.client.getJobsPerformance(recruiterId)).thenAnswer(IO.raiseError(UnauthorizationError("Error"))))
      result           <- serviceWithMocks.service.getJobsPerformance(recruiterId)(correlationId)
    } yield expect(result == Left(Unauthorized("Error")))
  }
}
