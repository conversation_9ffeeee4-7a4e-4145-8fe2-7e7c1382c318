package nl.dpes.job.poster.api.tracing

import cats.effect.IO
import cats.syntax.option._
import nl.dpes.job.poster.api.service.BearerToken
import org.mockito.ArgumentMatchersSugar.any
import org.mockito.MockitoSugar.{mock, when}
import sttp.tapir.model.ServerRequest
import weaver.SimpleIOSuite

object HeaderSpec extends SimpleIOSuite {
  import Header._

  test("Headers can be extracted") {
    for {
      request <- IO(mock[ServerRequest])
      _       <- IO(when(request.header(any[String])).thenReturn("A Header Value".some))
      header  <- request.extractHeader[AuthorizationHeader]
    } yield expect(header.value == "A Header Value")
  }

  test("An incorrect AuthorizationHeader cannot produce a BearerToken") {
    for {
      request     <- IO(mock[ServerRequest])
      _           <- IO(when(request.header(any[String])).thenReturn("An incorrect authorization header".some))
      authHeader  <- request.extractHeader[AuthorizationHeader]
      bearerToken <- authHeader.extractBearerToken.attempt
    } yield bearerToken match {
      case Right(token) => failure(s"Expected an error, but got a bearer token: '$token'")
      case Left(value)  => expect(value.getMessage == "No bearer token found in authorization header")
    }
  }

  test("A correct AuthorizationHeader can produce a BearerToken") {
    for {
      request     <- IO(mock[ServerRequest])
      _           <- IO(when(request.header(any[String])).thenReturn("Bearer some-bearer-token".some))
      authHeader  <- request.extractHeader[AuthorizationHeader]
      bearerToken <- authHeader.extractBearerToken
    } yield expect(bearerToken == BearerToken("some-bearer-token"))
  }

  test("The request id can be extracted from the headers") {
    for {
      request         <- IO(mock[ServerRequest])
      _               <- IO(when(request.header(any[String])).thenReturn("a-shiny-request-id".some))
      requestIdHeader <- request.extractHeader[RequestIdHeader]
    } yield expect(requestIdHeader.value == RequestId("a-shiny-request-id"))
  }
}
