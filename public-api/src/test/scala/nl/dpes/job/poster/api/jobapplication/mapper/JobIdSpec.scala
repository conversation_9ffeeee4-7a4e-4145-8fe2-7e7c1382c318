package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object JobIdSpec extends FunSuite {

  val jobIdString  = "*********"
  val jobId: JobId = JobId(jobIdString)

  test("Encode JobId") {
    expect(jobId.asJson.noSpaces == s""""$jobIdString"""")
  }

  test("Decode JobId") {
    expect(Json.fromString(jobIdString).as[JobId] == Right(jobId))
  }
}
