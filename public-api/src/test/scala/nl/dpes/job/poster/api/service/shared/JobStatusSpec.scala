package nl.dpes.job.poster.api.service.shared

import weaver.FunSuite

object JobStatusSpec extends FunSuite {

  test("It should return the correct JobStatus for a status string") {
    expect(JobStatus.fromString("validdraft") == JobStatus.ValidDraft) and
    expect(JobStatus.fromString("expired") == JobStatus.Expired) and
    expect(JobStatus.fromString("multistepdraft") == JobStatus.MultiStepDraft) and
    expect(JobStatus.fromString("draft") == JobStatus.Draft) and
    expect(JobStatus.fromString("published") == JobStatus.Published) and
    expect(JobStatus.fromString("suspended") == JobStatus.Suspended) and
    expect(JobStatus.fromString("rejected") == JobStatus.Rejected) and
    expect(JobStatus.fromString("approved") == JobStatus.Approved) and
    expect(JobStatus.fromString("submitted") == JobStatus.Submitted) and
    expect(JobStatus.fromString("deleted") == JobStatus.Deleted)
  }
}
