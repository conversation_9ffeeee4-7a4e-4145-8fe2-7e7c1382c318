package nl.dpes.job.poster.api.service.jobmanager

import cats.effect.IO
import cats.effect.kernel.Resource
import cats.implicits.{catsSyntaxOptionId, toBifunctorOps, toTraverseOps}
import nl.dpes.b2b.domain.{GenericError, JobConfiguration, ValidationError}
import nl.dpes.b2b.jobmanager.domain.InitialJobContent
import nl.dpes.b2b.jobmanager.domain.Status.Published
import nl.dpes.b2b.jobmanager.domain.jobsummary.{JobSummary, JobTitle, Summary, PublicationPeriod => SummaryPublicationPeriod}
import nl.dpes.b2b.jobmanager.service.GrpcJobService
import nl.dpes.b2b.salesforce.domain.{
  Address,
  Recruiter,
  SalesForceId,
  UnverifiedRecruiter,
  VerifiedRecruiter,
  Company => SalesforceGatewayCompany
}
import nl.dpes.job.poster.api.defaults.Defaults
import nl.dpes.job.poster.api.job.v1.job.JobStatus._
import nl.dpes.job.poster.api.service.job.{Job, JobUpdate}
import nl.dpes.job.poster.api.service.jobmanager.JobManager._
import nl.dpes.job.poster.api.service.logo.{LogoKey, LogoService}
import nl.dpes.job.poster.api.service.occupationclassification.OccupationClassifier
import nl.dpes.job.poster.api.service.occupationclassification.OccupationClassifier.{UnclassifiedPBPJob, UnrecognizedJob}
import nl.dpes.job.poster.api.service.prediction
import nl.dpes.job.poster.api.service.prediction.VacancyEnricher
import nl.dpes.job.poster.api.service.recruiter.AccessToken
import nl.dpes.job.poster.api.service.shared.{
  ApplyViaJobBoard,
  Budget,
  CareerLevel,
  Company,
  Configuration,
  Contact,
  ContactInformation,
  ContractTypes,
  Date,
  EducationLevels,
  Feature,
  Html,
  IndustryCategories,
  JobCategories,
  JobId,
  JobPosting,
  Logo,
  Name,
  Occupation,
  PerformanceBased,
  PostalAddress,
  PublicationPeriod,
  Range,
  SalaryRange,
  Site,
  Video,
  Workplace,
  Zipcode
}
import nl.dpes.job.poster.api.tracing.CorrelationId
import org.mockito.ArgumentMatchers.{any, anyString}
import org.mockito.MockitoSugar
import org.mockito.verification.VerificationMode
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.loggerFactoryforSync
import weaver.SimpleIOSuite

import java.time.{LocalDate, Period}
import scala.concurrent.Future
import scala.concurrent.duration.DurationInt

object JobManagerSpec extends SimpleIOSuite with MockitoSugar {

  val accessToken: AccessToken  = AccessToken("access_token")
  val jobId: JobId              = JobId("job_id")
  val recruiterId: SalesForceId = SalesForceId.unsafeApply("A Salesforce id")

  val unit: Unit             = ()
  val once: VerificationMode = times(1)

  val recruiter: Recruiter = VerifiedRecruiter(
    recruiterId,
    firstName = None,
    lastName = Some("lastname"),
    title = Some("title"),
    phoneNumber = Some("phone"),
    emailAddress = Some("<EMAIL>"),
    address = Some(Address("Street 66", "Amsterdam", "1033SN")),
    company =
      SalesforceGatewayCompany(Some("salesforceId"), nl.dpes.b2b.common.CompanyType.DirectEmployer, "CompanyName", Set(), None, None, None)
  )
  val loggerFactory: LoggerFactory[IO] = loggerFactoryforSync[IO]
  val logoService: LogoService[IO]     = mock[LogoService[IO]]

  val logoServiceResource: Resource[IO, LogoService[IO]] = Resource.pure[IO, LogoService[IO]](logoService)
  val occupationClassifier: OccupationClassifier[IO]     = mock[OccupationClassifier[IO]]

  val job: Job = Job(
    "Scala developer gezocht".some,
    Html.SwaggerDoc.example.some,
    Occupation.SwaggerDoc.example.some,
    JobCategories.SwaggerDoc.example.some,
    IndustryCategories.SwaggerDoc.example.some,
    EducationLevels.SwaggerDoc.example.some,
    CareerLevel.SwaggerDoc.example.some,
    ContractTypes.SwaggerDoc.example.some,
    Workplace.SwaggerDoc.example.some,
    Range.SwaggerDoc.hourExample.some,
    SalaryRange.SwaggerDoc.example.some,
    Zipcode("1018LL").some,
    PublicationPeriod(Date.SwaggerDoc.start, Date.SwaggerDoc.end.some).toOption,
    ApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
    Logo.SwaggerDoc.example.some,
    Video.SwaggerDoc.example.some,
    Company.SwaggerDoc.example.some,
    ContactInformation(
      Contact(
        Name("John", "Doe"),
        "**********".some,
        "<EMAIL>"
      ),
      PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
      "https://www.nationalevacaturebank.nl".some
    ).some
  )

  val jobUpdate: JobUpdate = JobUpdate(
    "Scala developer gezocht".some,
    Html.SwaggerDoc.example.some,
    Occupation.SwaggerDoc.example.some,
    JobCategories.SwaggerDoc.example.some,
    IndustryCategories.SwaggerDoc.example.some,
    EducationLevels.SwaggerDoc.example.some,
    CareerLevel.SwaggerDoc.example.some,
    ContractTypes.SwaggerDoc.example.some,
    Workplace.SwaggerDoc.example.some,
    Range.SwaggerDoc.hourExample.some,
    SalaryRange.SwaggerDoc.example.some,
    Zipcode("1018LL").some,
    PublicationPeriod(Date.SwaggerDoc.start, Date.SwaggerDoc.end.some).toOption,
    ApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
    Logo.SwaggerDoc.example.some,
    Video.SwaggerDoc.example.some,
    Company.SwaggerDoc.example.some,
    ContactInformation(
      Contact(
        Name("John", "Doe"),
        "**********".some,
        "<EMAIL>"
      ),
      PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
      "https://www.nationalevacaturebank.nl".some
    ).some
  )

  val pbpConfiguration: PerformanceBased = PerformanceBased(Budget.SwaggerDoc.example)

  val jobPosting: JobPosting = JobPosting(
    Set(Site.SwaggerDoc.nvb, Site.SwaggerDoc.iol),
    60.days,
    Set(Feature.SwaggerDoc.logo)
  )

  val start: LocalDate       = LocalDate.of(2023, 3, 3)
  val end: LocalDate         = LocalDate.of(2023, 3, 3).plus(Period.ofDays(10))
  val summary: Summary       = Summary(JobTitle("Scala developer"), Published, SummaryPublicationPeriod(start, end))
  val jobSummary: JobSummary = JobSummary(JobTitle("Scala developer"), Published, SummaryPublicationPeriod(start, end))

  test("it should delete a job successfully") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.deleteJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Right()))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.deleteJob(accessToken, jobId, recruiterId)
    } yield expect(result == ())
  }

  test("it should fail if the gRPC client returns an error when deleting a job") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.deleteJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Left(GenericError(500, "error"))))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.deleteJob(accessToken, jobId, recruiterId).attempt
    } yield expect(result == Left(JobCannotBeDeleted("error")))
  }

  test("It should fail if the gRPC client returns a forbidden error when deleting a job") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.deleteJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Left(GenericError(403, "This doesn't belong to this recruiter"))))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.deleteJob(accessToken, jobId, recruiterId).attempt
    } yield expect(result == Left(NoJobOwnership("This doesn't belong to this recruiter")))
  }

  test("it should suspend a job successfully") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.suspendJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Right()))
      )

      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.updateJobStatus(accessToken, jobId, recruiterId, Suspend)
    } yield expect(result == ())
  }

  test("it should fail if the gRPC client returns an error when suspending a job") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.suspendJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Left(GenericError(500, "GRPC server was FAILED_PRECONDITION: 'error'"))))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.updateJobStatus(accessToken, jobId, recruiterId, Suspend).attempt
    } yield expect(result == Left(JobManagerError(jobId.value, "error")))
  }

  test("it should indicate a job was not found when suspending with 'malformed' id") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.suspendJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Left(GenericError(400, s"INVALID_ARGUMENT: Malformed job id: '${jobId.value}'"))))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.updateJobStatus(accessToken, jobId, recruiterId, Suspend).attempt
    } yield expect(result == Left(JobNotFound(jobId)))
  }

  test("it should indicate a job was not found when suspending") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.suspendJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Left(GenericError(404, "any message"))))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.updateJobStatus(accessToken, jobId, recruiterId, Suspend).attempt
    } yield expect(result == Left(JobNotFound(jobId)))
  }

  test("it should indicate a job is not allowed to be updated in a certain state") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.suspendJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Left(GenericError(409, "any message"))))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.updateJobStatus(accessToken, jobId, recruiterId, Suspend).attempt
    } yield expect(result == Left(JobCannotBeSuspended("any message")))
  }

  test("it should indicate a job is not allowed to be updated if recruiter is not authorized to do so") {
    val client = mock[GrpcJobService]
    when(client.suspendJob(anyString, anyString, anyString, any))
      .thenAnswer(Future.successful(Left(GenericError(403, "any message"))))

    val vacancyEnricherService = mock[VacancyEnricher[IO]]
    val connection             = Resource.pure[IO, GrpcJobService](client)
    val service                = JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService)

    for {
      result <- service.updateJobStatus(accessToken, jobId, recruiterId, Suspend).attempt
    } yield expect(result == Left(JobManagerError(jobId.value, "any message")))
  }

  test("it should resume a job successfully") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.resumeJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Right()))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.updateJobStatus(accessToken, jobId, recruiterId, Resume)
    } yield expect(result == ())
  }

  test("it should indicate a job was not found when resuming with 'malformed' id") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.resumeJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Left(GenericError(400, s"INVALID_ARGUMENT: Malformed job id: '${jobId.value}'"))))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.updateJobStatus(accessToken, jobId, recruiterId, Resume).attempt
    } yield expect(result == Left(JobNotFound(jobId)))
  }

  test("it should indicate a job was not found when resuming") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.resumeJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Left(GenericError(404, "any message"))))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.updateJobStatus(accessToken, jobId, recruiterId, Resume).attempt
    } yield expect(result == Left(JobNotFound(jobId)))
  }

  test("it should fail if the gRPC client returns an error when resuming a job") {
    for {
      client <- IO(mock[GrpcJobService])
      _ <- IO(
        when(client.resumeJob(anyString, anyString, anyString, any))
          .thenAnswer(Future.successful(Left(GenericError(500, "GRPC server was FAILED_PRECONDITION: 'error'"))))
      )
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      connection             <- IO(Resource.pure[IO, GrpcJobService](client))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
      result                 <- service.updateJobStatus(accessToken, jobId, recruiterId, Resume).attempt
    } yield expect(result == Left(JobManagerError(jobId.value, "error")))
  }

  test("it should fail when unable to classify a PBP job") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
          .thenAnswer(IO.raiseError(UnclassifiedPBPJob))
      )
      result <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job,
          pbpConfiguration.some,
          Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
          recruiter,
          CorrelationId("something")
        )
        .attempt
    } yield expect(result == Left(UnclassifiedPBPJob))
  }

  test("it should fail when unable to classify a job when no occupation was provided and no classification was computed") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
          .thenAnswer(IO.raiseError(UnrecognizedJob))
      )
      result <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job,
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
          recruiter,
          CorrelationId("something")
        )
        .attempt
    } yield expect(result == Left(UnrecognizedJob))
  }

  test("postJob should fail if recruiter has no company") {
    val recruiterWithNoCompany = UnverifiedRecruiter(recruiterId)
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      result <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job,
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
          recruiterWithNoCompany,
          CorrelationId("something")
        )
        .attempt
    } yield expect(result.left.map(_.getMessage) == Left("No company"))
  }

  test("postJob should handle NoSuitableCredits error") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
          .thenReturn(Future.successful(Left(GenericError(409, "NoCredits"))))
      )
      result <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job,
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
          recruiter,
          CorrelationId("something")
        )
        .attempt
    } yield expect(result == Left(NoSuitableCredits("NoCredits")))
  }

  test("postJob should handle NotAJob error") {
    for {
      serviceWithMocks <- createService()
      _                <- IO(when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]])).thenReturn(IO.none))
      _ <- IO(
        when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
          .thenReturn(
            Future.successful(Left(GenericError(409, "GRPC server was FAILED_PRECONDITION: 'The provided content is not a job'")))
          )
      )
      result <- serviceWithMocks.service
        .postJob(accessToken, jobId, job, jobPosting.some, Defaults.SwaggerDoc.defaultsExample, recruiter, CorrelationId("something"))
        .attempt
    } yield expect(result == Left(NotAJob("The provided content is not a job")))
  }

  test("postJob should handle Timeout error") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
          .thenReturn(Future.successful(Left(GenericError(503, "Timeout"))))
      )
      result <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job,
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
          recruiter,
          CorrelationId("something")
        )
        .attempt
    } yield expect(result == Left(Timeout("Timeout")))
  }

  test("postJob should handle ServiceUnavailable error for 'unavailable' message") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
          .thenReturn(Future.successful(Left(GenericError(500, "GRPC server was unavailable: 'io exception'"))))
      )
      result <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job,
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
          recruiter,
          CorrelationId("something")
        )
        .attempt
    } yield expect(result.left.map(_.getClass.getSimpleName) == Left("ServiceUnavailable"))
  }

  test("postJob should handle ServiceUnavailable error for 'io exception' message") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
          .thenReturn(Future.successful(Left(GenericError(400, "Connection failed: io exception"))))
      )
      result <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job,
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
          recruiter,
          CorrelationId("something")
        )
        .attempt
    } yield expect(result.left.map(_.getClass.getSimpleName) == Left("ServiceUnavailable"))
  }

  test("postJob should handle various gRPC UNAVAILABLE status patterns") {
    val unavailableMessages = List(
      "GRPC server was UNAVAILABLE: 'connection refused'",
      "Service unavailable due to network issues",
      "gRPC service temporarily unavailable",
      "Server unavailable - please retry later"
    )

    unavailableMessages
      .traverse { message =>
        for {
          serviceWithMocks <- createService()
          _ <- IO(
            when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
              .thenReturn(IO.none)
          )
          _ <- IO(
            when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
              .thenReturn(Future.successful(Left(GenericError(503, message))))
          )
          result <- serviceWithMocks.service
            .postJob(
              accessToken,
              jobId,
              job,
              jobPosting.some,
              Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
              recruiter,
              CorrelationId("something")
            )
            .attempt
        } yield expect(result.left.map(_.getClass.getSimpleName) == Left("ServiceUnavailable"))
      }
      .map(_.reduce(_ and _))
  }

  test("postJob should handle various IO exception patterns") {
    val ioExceptionMessages = List(
      "Network IO exception occurred",
      "Connection reset by peer: io exception",
      "Socket timeout: io exception",
      "Failed to connect: io exception during handshake"
    )

    ioExceptionMessages
      .traverse { message =>
        for {
          serviceWithMocks <- createService()
          _ <- IO(
            when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
              .thenReturn(IO.none)
          )
          _ <- IO(
            when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
              .thenReturn(Future.successful(Left(GenericError(500, message))))
          )
          result <- serviceWithMocks.service
            .postJob(
              accessToken,
              jobId,
              job,
              jobPosting.some,
              Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
              recruiter,
              CorrelationId("something")
            )
            .attempt
        } yield expect(result.left.map(_.getClass.getSimpleName) == Left("ServiceUnavailable"))
      }
      .map(_.reduce(_ and _))
  }

  test("postJob should handle connection timeout scenarios") {
    val timeoutMessages = List(
      "Connection timeout after 30 seconds",
      "Request timeout: operation took too long",
      "Deadline exceeded: timeout waiting for response"
    )

    timeoutMessages
      .traverse { message =>
        for {
          serviceWithMocks <- createService()
          _ <- IO(
            when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
              .thenReturn(IO.none)
          )
          _ <- IO(
            when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
              .thenReturn(Future.successful(Left(GenericError(504, message))))
          )
          result <- serviceWithMocks.service
            .postJob(
              accessToken,
              jobId,
              job,
              jobPosting.some,
              Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
              recruiter,
              CorrelationId("something")
            )
            .attempt
        } yield expect(result.left.map(_.getClass.getSimpleName) == Left("Throwable"))
      }
      .map(_.reduce(_ and _))
  }

  test("postJob should NOT classify business logic errors as ServiceUnavailable") {
    val businessLogicMessages = List(
      "Invalid job data provided",
      "Insufficient credits for posting",
      "Job validation failed",
      "Recruiter not authorized"
    )

    businessLogicMessages
      .traverse { message =>
        for {
          serviceWithMocks <- createService()
          _ <- IO(
            when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
              .thenReturn(IO.none)
          )
          _ <- IO(
            when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
              .thenReturn(Future.successful(Left(GenericError(400, message))))
          )
          result <- serviceWithMocks.service
            .postJob(
              accessToken,
              jobId,
              job,
              jobPosting.some,
              Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
              recruiter,
              CorrelationId("something")
            )
            .attempt
        } yield expect(result.left.map(_.getClass.getSimpleName) == Left("Throwable"))
      }
      .map(_.reduce(_ and _))
  }

  test("postJob should handle case-insensitive matching for unavailable and io exception") {
    val caseVariations = List(
      "GRPC SERVER WAS UNAVAILABLE: 'CONNECTION FAILED'",
      "Service UNAVAILABLE due to maintenance",
      "Network IO EXCEPTION occurred",
      "Connection failed: IO EXCEPTION during setup"
    )

    caseVariations
      .traverse { message =>
        for {
          serviceWithMocks <- createService()
          _ <- IO(
            when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
              .thenReturn(IO.none)
          )
          _ <- IO(
            when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
              .thenReturn(Future.successful(Left(GenericError(500, message))))
          )
          result <- serviceWithMocks.service
            .postJob(
              accessToken,
              jobId,
              job,
              jobPosting.some,
              Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
              recruiter,
              CorrelationId("something")
            )
            .attempt
        } yield expect(result.left.map(_.getClass.getSimpleName) == Left("ServiceUnavailable"))
      }
      .map(_.reduce(_ and _))
  }

  test("postJob should handle unexpected errors") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
          .thenReturn(Future.successful(Left(GenericError(500, "something went wrong"))))
      )
      result <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job,
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
          recruiter,
          CorrelationId("something")
        )
        .attempt
    } yield expect(result.left.map(_.getMessage) == Left("something went wrong"))
  }

  test("postJob should handle validation errors") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[Job], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.postJob(anyString, any, any, any, any))
          .thenReturn(Future.successful(Left(ValidationError("error", "message"))))
      )
      result <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job,
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(occupation = None),
          recruiter,
          CorrelationId("something")
        )
        .attempt
    } yield expect(result == Left(InvalidData("error -> List(message)")))
  }

  test("updateJob should update a job successfully") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[JobUpdate], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.updateJob(anyString, anyString, any(), any, any))
          .thenAnswer(Future.successful(Right()))
      )
      result <- serviceWithMocks.service
        .updateJob(
          accessToken,
          jobId,
          jobUpdate,
          Defaults.SwaggerDoc.defaultsExample,
          recruiter
        )
    } yield expect(result == ())
  }

  test("updateJob should fail if recruiter has no company") {
    val recruiterWithNoCompany = UnverifiedRecruiter(recruiterId)
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[JobUpdate], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      result <- serviceWithMocks.service
        .updateJob(
          accessToken,
          jobId,
          jobUpdate,
          Defaults.SwaggerDoc.defaultsExample,
          recruiterWithNoCompany
        )
        .attempt
    } yield expect(result.left.map(_.getMessage) == Left("No company"))
  }

  test("updateJob should fail when unable to classify a PBP job") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[JobUpdate], any[Option[Configuration]]))
          .thenAnswer(IO.raiseError(UnclassifiedPBPJob))
      )
      result <- serviceWithMocks.service
        .updateJob(
          accessToken,
          jobId,
          jobUpdate,
          Defaults.SwaggerDoc.defaultsExample,
          recruiter
        )
        .attempt
    } yield expect(result.left.exists(_ == UnclassifiedPBPJob))
  }

  test("updateJob should fail when unable to classify a job when no occupation was provided and no classification was computed") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[JobUpdate], any[Option[Configuration]]))
          .thenAnswer(IO.raiseError(UnrecognizedJob))
      )
      result <- serviceWithMocks.service
        .updateJob(
          accessToken,
          jobId,
          jobUpdate,
          Defaults.SwaggerDoc.defaultsExample,
          recruiter
        )
        .attempt
    } yield expect(result.left.exists(_ == UnrecognizedJob))
  }

  test("updateJob should handle unexpected errors") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[JobUpdate], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.updateJob(anyString, anyString, any(), any, any))
          .thenReturn(Future.successful(Left(GenericError(500, "something went wrong"))))
      )
      result <- serviceWithMocks.service
        .updateJob(
          accessToken,
          jobId,
          jobUpdate,
          Defaults.SwaggerDoc.defaultsExample,
          recruiter
        )
        .attempt
    } yield expect(result.left.map(_.getMessage) == Left("something went wrong"))
  }

  test("updateJob should handle Forbidden error") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[JobUpdate], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.updateJob(anyString, anyString, any(), any, any))
          .thenReturn(Future.successful(Left(GenericError(403, "Forbidden error"))))
      )
      result <- serviceWithMocks.service
        .updateJob(accessToken, jobId, jobUpdate, Defaults.SwaggerDoc.defaultsExample, recruiter)
        .attempt
    } yield expect(result == Left(JobCannotBeUpdated("Forbidden error")))
  }

  test("updateJob should handle Conflict error") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[JobUpdate], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.updateJob(anyString, anyString, any(), any, any))
          .thenReturn(Future.successful(Left(GenericError(409, "Conflict error"))))
      )
      result <- serviceWithMocks.service
        .updateJob(accessToken, jobId, jobUpdate, Defaults.SwaggerDoc.defaultsExample, recruiter)
        .attempt
    } yield expect(result == Left(NonUpdatableJob("Conflict error")))
  }

  test("updateJob should handle validation errors") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.occupationClassifier.classify(any[JobUpdate], any[Option[Configuration]]))
          .thenReturn(IO.none)
      )
      _ <- IO(
        when(serviceWithMocks.grpcJobService.updateJob(anyString, anyString, any(), any, any))
          .thenReturn(Future.successful(Left(ValidationError("error", "message"))))
      )
      result <- serviceWithMocks.service
        .updateJob(
          accessToken,
          jobId,
          jobUpdate,
          Defaults.SwaggerDoc.defaultsExample,
          recruiter
        )
        .attempt
    } yield expect(result == Left(InvalidData("error -> List(message)")))
  }

  test("getJob should return JobNotFound error if a job cannot be found") {
    for {
      serviceWithMocks <- createService()
      _ <- IO(
        when(serviceWithMocks.grpcJobService.getJobSummary(anyString, anyString, any[SalesForceId]))
          .thenAnswer(Future.successful(Left(GenericError(404, "JobNotFound"))))
      )
      result <- serviceWithMocks.service.getJob(accessToken, recruiterId, jobId).attempt
    } yield expect(result == Left(JobNotFound(jobId)))
  }

  test("getJob should return InvalidData error if a job is invalid") {
    for {
      serviceWithMocks <- createService({ case jobService: GrpcJobService =>
        when(jobService.getJobSummary(anyString, anyString, any[SalesForceId]))
          .thenAnswer(Future.successful(Left(GenericError(400, "InvalidData"))))
      })
      result <- serviceWithMocks.service.getJob(accessToken, recruiterId, jobId).attempt
    } yield expect(result == Left(InvalidData("InvalidData")))
  }

  test("It should detect if a job-manager error occurred when trying to get a job") {
    for {
      serviceWithMocks <- createService({ case jobService: GrpcJobService =>
        when(jobService.getJobSummary(anyString, anyString, any[SalesForceId]))
          .thenAnswer(Future.successful(Left(GenericError(500, "Error"))))
      })
      result <- serviceWithMocks.service.getJob(accessToken, recruiterId, jobId).attempt
    } yield expect(result == Left(JobManagerError(jobId.value, "Error")))
  }

  test("construct publication period from job summary") {
    for {
      publicationPeriod <- IO.delay(JobManager.constructPublicationPeriod(jobSummary)).attempt
      start             <- IO.fromEither(Date("2023-03-03").toEither.leftMap(x => new Throwable(x)))
      end               <- IO.fromEither(Date("2023-03-13").toEither.leftMap(x => new Throwable(x)))
    } yield expect(publicationPeriod == Right(PublicationPeriod(start, end.some)))
  }

  test("when all predicatable fields are set in payload no prediction will be done") {
    for {
      serviceWithMocks <- createService()
      _ <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job.copy(industryCategories = IndustryCategories.SwaggerDoc.example.some),
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(industryCategories = None),
          recruiter,
          CorrelationId.generate
        )
      verified <- IO(
        verify(serviceWithMocks.vacancyEnricherService, never)
          .predict(prediction.JobTitle(any[String]), prediction.JobDescription(any[String]))
      ).attempt
    } yield expect(verified.isInstanceOf[Right[Throwable, Null]])
  }

  test("when all predicatable fields are set in defaults no prediction will be done") {
    for {
      serviceWithMocks <- createService()
      _ <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job.copy(industryCategories = None),
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(industryCategories = IndustryCategories.SwaggerDoc.example.some),
          recruiter,
          CorrelationId.generate
        )
      verified <- IO(
        verify(serviceWithMocks.vacancyEnricherService, never)
          .predict(prediction.JobTitle(any[String]), prediction.JobDescription(any[String]))
      ).attempt
    } yield expect(verified.isInstanceOf[Right[Throwable, Null]])
  }

  test("when a predicatable field is missing in both defaults and payload a prediction will still NOT be used") {
    for {
      serviceWithMocks <- createService()
      _ <- serviceWithMocks.service
        .postJob(
          accessToken,
          jobId,
          job.copy(industryCategories = None),
          jobPosting.some,
          Defaults.SwaggerDoc.defaultsExample.copy(industryCategories = None),
          recruiter,
          CorrelationId.generate
        )
      verified <- IO(
        verify(serviceWithMocks.vacancyEnricherService, never)
          .predict(
            prediction.JobTitle(job.title.getOrElse("")),
            prediction.JobDescription(job.description.map(_.text).getOrElse(""))
          )
      ).attempt
    } yield expect(verified.isInstanceOf[Right[Throwable, Null]])
  }

  test("Errors that do not need to be stripped are left intact") {
    val grpcError     = "No need to strip"
    val expectedError = "No need to strip"
    IO(expect(JobManager.stripGrpcError(grpcError) == expectedError))
  }

  test("Grpc error 'Failed precondition' can be stripped") {
    val grpcError =
      "GRPC server was FAILED_PRECONDITION: 'Cannot update job: 'Updating PBP job b86bc0ff-e5d0-42a4-bcdf-0ceedd0416f4 is not allowed''"
    val expectedError = "Cannot update job: 'Updating PBP job b86bc0ff-e5d0-42a4-bcdf-0ceedd0416f4 is not allowed'"
    IO(expect(JobManager.stripGrpcError(grpcError) == expectedError))
  }

  def createService(mockBehavior: PartialFunction[Any, Unit] = PartialFunction.empty): IO[JobManagerService] = {

    def setupMock(mock: Any): IO[Unit] =
      if ((mockBehavior orElse defaultMockBehavior).isDefinedAt(mock)) IO((mockBehavior orElse defaultMockBehavior)(mock))
      else IO.unit

    for {
      grpcJobService         <- IO(mock[GrpcJobService])
      _                      <- setupMock(grpcJobService)
      occupationClassifier   <- IO(mock[OccupationClassifier[IO]])
      _                      <- setupMock(occupationClassifier)
      logoService            <- IO(mock[LogoService[IO]])
      _                      <- setupMock(logoService)
      vacancyEnricherService <- IO(mock[VacancyEnricher[IO]])
      _                      <- setupMock(vacancyEnricherService)
      connection             <- IO(Resource.pure[IO, GrpcJobService](grpcJobService))
      logoServiceResource    <- IO(Resource.pure[IO, LogoService[IO]](logoService))
      service                <- IO(JobManager[IO](connection, logoServiceResource, occupationClassifier, vacancyEnricherService))
    } yield JobManagerService(grpcJobService, service, logoService, logoServiceResource, occupationClassifier, vacancyEnricherService)
  }

  lazy val defaultMockBehavior: PartialFunction[Any, Unit] = {
    case m: LogoService[IO] =>
      when(m.store(Logo.SwaggerDoc.example, 0))
        .thenAnswer(IO.pure(LogoKey("key")))
    case m: VacancyEnricher[IO] =>
      when(m.predict(prediction.JobTitle(any[String]), prediction.JobDescription(any[String])))
        .thenReturn(
          IO(
            prediction.Prediction(
              None,
              List(prediction.JobCategory("Juridisch")),
              prediction.EducationLevel("Havo"),
              prediction.CareerLevel("Directie"),
              prediction.ContractType("Freelance")
            )
          )
        )
    case jobService: GrpcJobService =>
      when(jobService.postJob(anyString, any[VerifiedRecruiter], any[InitialJobContent], any[JobConfiguration], any[Option[String]]))
        .thenAnswer(Future.successful(Right(())))
    case classifier: OccupationClassifier[IO] =>
      when(classifier.classify(any[Job], any[Option[Configuration]]))
        .thenReturn(IO.none)
  }

  case class JobManagerService(
    grpcJobService: GrpcJobService,
    service: JobManager[IO],
    logoService: LogoService[IO],
    logoServiceResource: Resource[IO, LogoService[IO]],
    occupationClassifier: OccupationClassifier[IO],
    vacancyEnricherService: VacancyEnricher[IO]
  )
}
