package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import io.circe.CursorOp.DownArray
import io.circe._
import io.circe.syntax._
import io.circe.parser.decode
import weaver._
import cats.implicits._

object JobCategoriesSpec extends FunSuite {

  val jobCategories = """["Administratief/Secretarieel","Automatisering/Internet"]"""

  test("Encode job categories") {
    expect(JobCategories.SwaggerDoc.example.asJson.noSpaces == jobCategories)
  }

  test("Decode job categories") {
    expect(decode[JobCategories](jobCategories) == Right(JobCategories.SwaggerDoc.example))
  }

  test("Unable to decode an invalid job categories") {
    expect(
      decode[JobCategories]("""["something"]""") == Left(
        DecodingFailure(s"Job category 'something' is not valid.", List(DownArray))
      )
    )
  }

  test("Unable to decode job categories with empty value") {
    expect(
      decode[JobCategories]("""[]""") == Left(
        DecodingFailure("At least 1 categories should be chosen", List())
      )
    )
  }

  test("Unable to decode job categories with too many values") {
    expect(
      decode[JobCategories]("""["Administratief/Secretarieel","Automatisering/Internet","Marketing/PR/Communicatie"]""") == Left(
        DecodingFailure("At most 2 categories should be chosen", List())
      )
    )
  }

  test("It fails when less than minimum categories have been provided") {
    expect(JobCategories(Set()) == Invalid("At least 1 categories should be chosen"))
  }

  test("It fails when more than maximum categories have been provided") {
    val categories = List(
      JobCategory("Administratief/Secretarieel"),
      JobCategory("Automatisering/Internet"),
      JobCategory("Marketing/PR/Communicatie")
    ).sequence.map(_.toSet).andThen(JobCategories.apply)

    expect(categories == Invalid("At most 2 categories should be chosen"))
  }

  test("It succeeds when a correct amount of categories have been provided") {
    val categories = List(
      JobCategory("Administratief/Secretarieel"),
      JobCategory("Automatisering/Internet")
    ).sequence.map(_.toSet).andThen(JobCategories.apply)

    expect(categories.isValid)
  }
}
