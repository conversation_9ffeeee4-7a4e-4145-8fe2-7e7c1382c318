package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import cats.implicits._
import io.circe.{parser, DecodingFailure}
import io.circe.syntax._
import weaver._
import io.circe.parser._
import nl.dpes.job.poster.api.service.Cursor
import nl.dpes.job.poster.api.service.shared.{Hour => AppHour, Range => AppRange, Salary => AppSalary}

object RangeSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val hourRange          = """{"lower":10,"upper":30}"""
  val invalidHourRange   = """{"lower":30,"upper":10}"""
  val salaryRange        = """{"lower":10000,"upper":30000}"""
  val invalidSalaryRange = """{"lower":30000,"upper":10000}"""

  test("Encode hour range") {
    expect(Range.SwaggerDoc.hourExample.asJson.noSpaces == hourRange)
  }
  test("Decoding fails when providing an invalid hour range") {
    expect(
      parser
        .parse(invalidHourRange)
        .flatMap(_.as[Range[Hour]]) == DecodingFailure("Lower value 'Hour(30)' cannot be greater than upper value 'Hour(10)'.", List())
        .asLeft[Range[Hour]]
    )
  }

  test("Decode hour range") {
    expect(decode[Range[Hour]](hourRange) == Right(Range.SwaggerDoc.hourExample))
  }

  test("Creating a hour range") {
    expect(Range[Hour](Hour.SwaggerDoc.minHourExample, Hour.SwaggerDoc.maxHourExample) == Valid(Range.SwaggerDoc.hourExample))
  }

  test("It should return an error when hour range is invalid") {
    expect(
      Range[Hour](Hour.SwaggerDoc.maxHourExample, Hour.SwaggerDoc.minHourExample) == Invalid(
        s"Lower value '${Hour.SwaggerDoc.maxHourExample}' cannot be greater than upper value '${Hour.SwaggerDoc.minHourExample}'."
      )
    )
  }

  test("Mapping working hours from API to Application model") {
    val actualAppRangeWorkingHours =
      Range.mapWorkingHours(Range[Hour](Hour(10).toOption.get, Hour(30).toOption.get).toOption)
    val expectedAppRangeWorkingHours = AppRange[AppHour](AppHour(10).toOption.get, AppHour(30).toOption.get).some.sequence

    expect(actualAppRangeWorkingHours == expectedAppRangeWorkingHours)
  }
}
