package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import io.circe._
import io.circe.syntax._

object ContractTypeSpec extends FunSuite {

  val contractType = "Interim"

  test("Encode contract type") {
    expect(ContractType.SwaggerDoc.interim.asJson.noSpaces == s""""$contractType"""")
  }

  test("Decode contract type") {
    expect(Json.fromString(contractType).as[ContractType] == Right(ContractType.SwaggerDoc.interim))
  }

  test("Unable to decode an invalid contract type") {
    expect(
      Json.fromString("something").as[ContractType] == Left(
        DecodingFailure(s"Contract type 'something' is not valid.", List())
      )
    )
  }

  test("Creating a contract type") {
    expect(ContractType("Interim").isValid)
  }

  test("It should return an error when contract type is wrong") {
    val invalidContract = "invalid contract"
    expect(ContractType(invalidContract) == Invalid(s"Contract type '$invalidContract' is not valid."))
  }

  test("It should return an error when contract type is 'Arbeider'") {
    val invalidContract = "Arbeider"
    expect(ContractType(invalidContract) == Invalid(s"Contract type '$invalidContract' is not valid."))
  }

  test("It should return an error when contract type is 'Vakantiejob / studentenjob'") {
    val invalidContract = "Vakantiejob / studentenjob"
    expect(ContractType(invalidContract) == Invalid(s"Contract type '$invalidContract' is not valid."))
  }

  test("Unable to decode 'Arbeider' contract type") {
    val invalidContract = "Arbeider"
    expect(
      Json.fromString(invalidContract).as[ContractType] == Left(
        DecodingFailure(s"Contract type '$invalidContract' is not valid.", List())
      )
    )
  }

  test("Unable to decode 'Vakantiejob / studentenjob' contract type") {
    val invalidContract = "Vakantiejob / studentenjob"
    expect(
      Json.fromString(invalidContract).as[ContractType] == Left(
        DecodingFailure(s"Contract type '$invalidContract' is not valid.", List())
      )
    )
  }
}
