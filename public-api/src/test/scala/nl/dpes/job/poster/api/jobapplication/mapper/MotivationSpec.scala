package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object MotivationSpec extends FunSuite {

  val motivationString       = "some motivation"
  val motivation: Motivation = Motivation(motivationString)

  test("Encode Motivation") {
    expect(motivation.asJson.noSpaces == s""""$motivationString"""")
  }

  test("Decode Motivation") {
    expect(Json.fromString(motivationString).as[Motivation] == Right(motivation))
  }
}
