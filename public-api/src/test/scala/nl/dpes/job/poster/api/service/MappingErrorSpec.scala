package nl.dpes.job.poster.api.service

import weaver._

object MappingErrorSpec extends FunSuite {

  test("it has a nice error message") {
    expect(MappingError(Cursor("body")("foo") -> "Has an error").toString == "Invalid value for: body (Has an error at foo)")
  }

  test("it combines errors") {
    val cursor   = Cursor("body")
    val fooError = MappingError(cursor("foo") -> "has an error")
    val barError = MappingError(cursor("bar")("baz") -> "has another error")
    val combined = fooError combine barError

    expect(combined.getMessage == "Invalid value for: body (has an error at foo, has another error at bar.baz)") and
    expect(combined.getClass == classOf[MappingError])
  }

  test("it can handle multiple root elements") {
    val fooError = MappingError(Cursor("body1")("foo") -> "has an error")
    val barError = MappingError(Cursor("body2")("baz") -> "has another error")
    val combined = fooError combine barError

    expect(combined.getMessage.contains("Invalid value for: body1 (has an error at foo)")) and
    expect(combined.getMessage.contains("Invalid value for: body2 (has another error at baz)")) and
    expect(combined.getClass == classOf[MappingError])
  }
}
