package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object FileNameSpec extends FunSuite {

  val fileNameString     = "candidate.pdf"
  val fileName: FileName = FileName(fileNameString)

  test("Encode FileName") {
    expect(fileName.asJson.noSpaces == s""""$fileNameString"""")
  }

  test("Decode FileName") {
    expect(Json.fromString(fileNameString).as[FileName] == Right(fileName))
  }
}
