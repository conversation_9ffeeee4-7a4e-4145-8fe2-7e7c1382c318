package nl.dpes.job.poster.api.jobapplication.mapper

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

object UrlSpec extends FunSuite {

  val urlString = "https://google.com"
  val url: Url  = Url(urlString)

  test("Encode Url") {
    expect(url.asJson.noSpaces == s""""$urlString"""")
  }

  test("Decode Url") {
    expect(Json.fromString(urlString).as[Url] == Right(url))
  }
}
