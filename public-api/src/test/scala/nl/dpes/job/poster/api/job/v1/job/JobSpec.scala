package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.Valid
import cats.implicits._
import io.circe.CursorOp.{DownArray, DownField}
import io.circe._
import io.circe.parser.decode
import io.circe.syntax._
import nl.dpes.job.poster.api.job.v1.job.{
  Feature => ApiFeature,
  JobPosting => ApiJobPosting,
  PerformanceBased => ApiPerformanceBased,
  Site => ApiSite
}
import nl.dpes.job.poster.api.service.Cursor
import nl.dpes.job.poster.api.service.shared.{
  Budget => AppBudget,
  Feature => AppFeature,
  JobPosting => AppJobPosting,
  PerformanceBased => AppPerformanceBased,
  Site => AppSite
}
import weaver._

import scala.concurrent.duration.DurationInt

object JobSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val jobJsonString: String =
    """{
      |  "title": "Scala developer gezocht",
      |  "description": "<p>The description</p>\n<p>To describe the duties of the job.</p>",
      |  "occupation": "Developer",
      |  "jobCategories": [
      |    "Administratief/Secretarieel",
      |    "Automatisering/Internet"
      |  ],
      |  "industryCategories": [
      |    "Telecom",
      |    "Techniek"
      |  ],
      |  "educationLevels": [
      |    "HBO",
      |    "WO",
      |    "HAVO"
      |  ],
      |  "careerLevel": "Starter",
      |  "contractTypes": [
      |    "Interim",
      |    "Stage",
      |    "Tijdelijk"
      |  ],
      |  "workplace": "Remote",
      |  "workingHours": {
      |    "lower": 10,
      |    "upper": 30
      |  },
      |  "salary": {
      |    "lower": 10000,
      |    "upper": 30000
      |  },
      |  "location": "1018LL",
      |  "publicationPeriod": {
      |    "start": "2023-06-06",
      |    "end": "2023-06-12"
      |  },
      |  "applicationMethod": {
      |    "ApplyViaJobBoard": {
      |      "firstName": "John",
      |      "lastName": "Doe",
      |      "emailAddress": "<EMAIL>"
      |    }
      |  },
      |  "logo": "https://picsum.photos/200/300",
      |  "video": "https://www.youtube.com/watch?time_continue=4&v=jobvideo",
      |  "company": {
      |    "name": "ACME corp",
      |    "address": {
      |      "PostalAddress": {
      |        "streetNameAndHouseNumber": "mt Lincolnweg 40",
      |        "city": "Amsterdam",
      |        "zipCode": "1033SN"
      |      }
      |    },
      |    "companyType": "Direct employer",
      |    "website": "https://www.nationalevacaturebank.nl/"
      |  },
      |  "contactInformation": {
      |    "contact": {
      |      "name": {
      |        "firstName": "John",
      |        "lastName": "Doe"
      |      },
      |      "phoneNumber": "**********",
      |      "emailAddress": "<EMAIL>"
      |    },
      |    "address": {
      |      "streetNameAndHouseNumber": "mt Lincolnweg 40",
      |      "city": "Amsterdam",
      |      "zipCode": "1033SN"
      |    },
      |    "website": "https://www.nationalevacaturebank.nl"
      |  },
      |  "configuration": {
      |    "PerformanceBased":{"budget":500}
      |  }
      |}""".stripMargin

  val jobJsonStringWithGeolocation: String =
    """{
      |  "title": "Scala developer gezocht",
      |  "description": "<p>The description</p>\n<p>To describe the duties of the job.</p>",
      |  "occupation": "Developer",
      |  "jobCategories": [
      |    "Administratief/Secretarieel",
      |    "Automatisering/Internet"
      |  ],
      |  "industryCategories": [
      |    "Telecom",
      |    "Techniek"
      |  ],
      |  "educationLevels": [
      |    "HBO",
      |    "WO",
      |    "HAVO"
      |  ],
      |  "careerLevel": "Starter",
      |  "contractTypes": [
      |    "Interim",
      |    "Stage",
      |    "Tijdelijk"
      |  ],
      |  "workplace": "Remote",
      |  "workingHours": {
      |    "lower": 10,
      |    "upper": 30
      |  },
      |  "salary": {
      |    "lower": 10000,
      |    "upper": 30000
      |  },
      |  "location": {
      |    "Geolocation": {
      |      "longitude": 4.9284,
      |      "latitude": 52.3717
      |    }
      |  },
      |  "publicationPeriod": {
      |    "start": "2023-06-06",
      |    "end": "2023-06-12"
      |  },
      |  "applicationMethod": {
      |    "ApplyViaJobBoard": {
      |      "firstName": "John",
      |      "lastName": "Doe",
      |      "emailAddress": "<EMAIL>"
      |    }
      |  },
      |  "logo": "https://picsum.photos/200/300",
      |  "video": "https://www.youtube.com/watch?time_continue=4&v=jobvideo",
      |  "company": {
      |    "name": "ACME corp",
      |    "address": {
      |      "PostalAddress": {
      |        "streetNameAndHouseNumber": "mt Lincolnweg 40",
      |        "city": "Amsterdam",
      |        "zipCode": "1033SN"
      |      }
      |    },
      |    "companyType": "Direct employer",
      |    "website": "https://www.nationalevacaturebank.nl/"
      |  },
      |  "contactInformation": {
      |    "contact": {
      |      "name": {
      |        "firstName": "John",
      |        "lastName": "Doe"
      |      },
      |      "phoneNumber": "**********",
      |      "emailAddress": "<EMAIL>"
      |    },
      |    "address": {
      |      "streetNameAndHouseNumber": "mt Lincolnweg 40",
      |      "city": "Amsterdam",
      |      "zipCode": "1033SN"
      |    },
      |    "website": "https://www.nationalevacaturebank.nl"
      |  },
      |  "configuration": {
      |    "PerformanceBased":{"budget":500}
      |  }
      |}""".stripMargin

  test("Decode JobPosting for job with Zipcode") {
    val decoded: Either[Error, Job] = decode[Job](jobJsonString)

    expect {
      val encodedJob: Either[Error, String] = decoded.map(_.asJson.noSpaces)
      val decodedJob: Either[Error, Job]    = encodedJob.flatMap(decode[Job])

      encodedJob == decodedJob.map(_.asJson.noSpaces)
    }
  }

  test("Decode JobPosting for job with Geolocation") {
    val decoded: Either[Error, Job] = decode[Job](jobJsonStringWithGeolocation)

    expect {
      val encodedJob: Either[Error, String] = decoded.map(_.asJson.noSpaces)
      val decodedJob: Either[Error, Job]    = encodedJob.flatMap(decode[Job])

      encodedJob == decodedJob.map(_.asJson.noSpaces)
    }
  }
}
