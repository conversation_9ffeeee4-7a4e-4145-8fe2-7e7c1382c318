package nl.dpes.job.poster.api.service.apikey.api

import cats.implicits._
import io.circe.parser._
import io.circe.syntax._
import nl.dpes.job.poster.api.converter.Converter.{ConvertorOps, InvertorOps}
import nl.dpes.job.poster.api.service.{apikey => domain}

import java.sql.Timestamp
import java.time.Instant

object ApiKeyInformationSpec extends weaver.FunSuite {
  val now: Instant       = Instant.now
  val apiKeyId: ApiKeyId = ApiKeyId(domain.ApiKeyId.generate.value)

  val decoded: ApiKeyInformation = ApiKeyInformation(
    apiKeyId,
    Integration("An integration"),
    CreatedAt(Timestamp.from(now))
  )
  val encoded: String = s"""{"id":"${apiKeyId.value}","integration":"An integration","createdAt":"$now"}"""

  test("Encoded value does not contain nested objects") {
    expect(decoded.asJson.noSpaces == encoded)
  }

  test("The encoded value can be decoded") {
    expect(decode[ApiKeyInformation](encoded) == decoded.asRight)
  }

  test("can be converted to domain") {
    expect(decoded.toDomain.isInstanceOf[domain.ApiKeyInformation])
  }

  test("can be converted from domain") {
    expect(decoded.toDomain[domain.ApiKeyInformation].toApi[ApiKeyInformation] == decoded)
  }
}
