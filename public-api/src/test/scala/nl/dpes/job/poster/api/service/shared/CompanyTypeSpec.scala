package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import io.circe._
import io.circe.syntax._

object CompanyTypeSpec extends FunSuite {

  val companyType = "Direct employer"

  test("Encode company type") {
    expect(CompanyType.SwaggerDoc.example.asJson.noSpaces == s""""$companyType"""")
  }

  test("Decode company type") {
    expect(Json.fromString(companyType).as[CompanyType] == Right(CompanyType.SwaggerDoc.example))
  }

  test("Unable to decode an invalid company type") {
    expect(
      Json.fromString("someone").as[CompanyType] == Left(
        DecodingFailure(s"Company type 'someone' is not valid.", List())
      )
    )
  }

  test("It succeeds when a known category has been provided") {
    expect(CompanyType("Direct employer").isValid)
  }

  test("It fails when an unknown company type has been provided") {
    expect(CompanyType("Unknown") == Invalid(s"Company type 'Unknown' is not valid."))
  }
}
