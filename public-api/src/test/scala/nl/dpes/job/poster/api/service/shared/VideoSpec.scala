package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import io.circe._
import io.circe.syntax._

object VideoSpec extends FunSuite {

  val url               = "https://www.youtube.com/watch?time_continue=4&v=jobvideo"
  val invalidYoutubeUrl = "abc.youtube.com/watch?time_continue=4&v=jobvideo"

  test("Encode video url") {
    expect(Video.SwaggerDoc.example.asJson.noSpaces == s""""$url"""")
  }

  test("Decode video url") {
    expect(Json.fromString(url).as[Video] == Right(Video.SwaggerDoc.example))
  }

  test("Unable to decode an invalid video") {
    expect(Json.fromString("something").as[Video] == Left(DecodingFailure("Cannot parse url 'something'.", List())))
  }

  test("Unable to decode an invalid youtube video") {
    expect(
      Json.fromString(invalidYoutubeUrl).as[Video] == Left(
        DecodingFailure(s"java.net.MalformedURLException: no protocol: $invalidYoutubeUrl", List())
      )
    )
  }

  test("Creating a video url") {
    expect(Video("https://www.youtube.com/watch?time_continue=4&v=jobvideo").isValid)
  }

  test("It should return an error when video url is invalid") {
    val invalidUrl = "invalid url"
    expect(Video(invalidUrl) == Invalid(s"Cannot parse url '$invalidUrl'."))
  }

  test("It should return an error when a youtube video url is invalid") {
    expect(Video(invalidYoutubeUrl) == Invalid(s"java.net.MalformedURLException: no protocol: $invalidYoutubeUrl"))
  }
}
