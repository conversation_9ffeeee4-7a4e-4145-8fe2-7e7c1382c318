package nl.dpes.job.poster.api.service.apikey.api

import cats.implicits._
import io.circe.parser._
import io.circe.syntax._

object IntegrationInputSpec extends weaver.FunSuite {
  val decoded = IntegrationInput(Integration("An integration"))
  val encoded = """{"integration":"An integration"}"""

  test("The input can be encoded") {
    expect(decoded.asJson.noSpaces == encoded)
  }

  test("the encoded value can be decoded to the original") {
    expect(decode[IntegrationInput](encoded) == decoded.asRight)
  }
}
