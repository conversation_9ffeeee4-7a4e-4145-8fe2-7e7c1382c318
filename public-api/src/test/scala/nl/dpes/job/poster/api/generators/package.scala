package nl.dpes.job.poster.api

import cats.data.Validated
import nl.dpes.job.poster.api.defaults.Defaults
import nl.dpes.job.poster.api.service.shared.{
  Address,
  ApplicationMethod,
  ApplyViaExternalWebsite,
  ApplyViaJobBoard,
  CareerLevel,
  Company,
  CompanyType,
  ContractType,
  ContractTypes,
  DomesticAddress,
  EasyApply,
  EducationLevel,
  EducationLevels,
  Feature,
  ForeignAddress,
  Hour,
  Html,
  ISOAlpha2,
  IndustryCategories,
  IndustryCategory,
  JobCategories,
  JobCategory,
  JobPosting,
  Logo,
  Occupation,
  PostalAddress,
  Range,
  Salary,
  SalaryPeriod,
  SalaryRange,
  Site,
  Video,
  Website,
  Workplace,
  Zipcode
}
import org.scalacheck.Gen

import scala.concurrent.duration._
package object generators {

  implicit class ValidatedOps[E, A](val validated: Validated[E, A]) extends AnyVal {

    def gen: Gen[A] = validated.fold(
      { e =>
        println(e)
        Gen.fail
      },
      Gen.const
    )
  }

  val websiteUrls: List[String] = List("https://typelevel.org/cats/typeclasses/show.html")

  val videoUrls: List[String] =
    List(
      "https://www.youtu.be/kmFcNyZrUNM",
      "http://www.youtube.com/watch?v=kmFcNyZrUNM",
      "http://youtube.com/watch?v=kmFcNyZrUNM",
      "http://m.youtube.com/watch?v=kmFcNyZrUNM",
      "https://vimeo.com/165922514",
      "http://player.vimeo.com/video/165922514"
    )

  val imageUrls: List[String] = List(
    "https://static01.nyt.com/images/2022/09/01/business/00roose-1/merlin_212276709_3104aef5-3dc4-4288-bb44-9e5624db0b37-superJumbo.jpg",
    "https://cataas.com/cat"
  )
  implicit lazy val genHtml: Gen[Html] = Gen.oneOf("test", "html").flatMap(Html.apply(_).fold[Gen[Html]](_ => Gen.fail, Gen.const))

  implicit lazy val genOccupation: Gen[Occupation] =
    Gen.alphaNumStr.flatMap(generated => Occupation.apply(s"$generated plus characters").gen)

  implicit lazy val genJobCategory: Gen[JobCategory] =
    Gen.oneOf(JobCategory.validJobCategories).flatMap(JobCategory.apply(_).gen)

  implicit lazy val genJobCategories: Gen[JobCategories] = for {
    amount           <- Gen.choose(JobCategories.minimumAmountOfCategories, JobCategories.maximumAmountOfCategories)
    listOfCategories <- Gen.listOfN[JobCategory](amount, genJobCategory)
    categories       <- JobCategories(listOfCategories.toSet).fold(_ => Gen.fail[JobCategories], Gen.const)
  } yield categories

  implicit lazy val genIndustryCategory: Gen[IndustryCategory] =
    Gen.oneOf(IndustryCategory.validIndustryCategories).flatMap(IndustryCategory.apply(_).gen)

  implicit lazy val genIndustryCategories: Gen[IndustryCategories] = for {
    amount           <- Gen.const(IndustryCategories.maximumAmountOfCategories)
    listOfCategories <- Gen.listOfN[IndustryCategory](amount, genIndustryCategory)
    categories       <- IndustryCategories(listOfCategories.toSet).gen
  } yield categories

  implicit lazy val genEducationLevel: Gen[EducationLevel] =
    Gen.oneOf(EducationLevel.validEducationLevels).flatMap(EducationLevel.apply(_).gen)

  implicit lazy val genEducationLevels: Gen[EducationLevels] = for {
    amount          <- Gen.choose(EducationLevels.minEducationLevels, EducationLevels.maxEducationLevels)
    listOfLevels    <- Gen.listOfN[EducationLevel](amount, genEducationLevel)
    educationLevels <- EducationLevels(listOfLevels.toSet).gen
  } yield educationLevels

  implicit lazy val genCareerLevel: Gen[CareerLevel] =
    Gen.oneOf(CareerLevel.validCareerLevels).flatMap(CareerLevel.apply(_).gen)

  implicit lazy val genContractType: Gen[ContractType] =
    Gen.oneOf(ContractType.validContractTypes).flatMap(ContractType.apply(_).gen)

  implicit lazy val genContractTypes: Gen[ContractTypes] = for {
    amount        <- Gen.choose(ContractTypes.minContractTypes, ContractTypes.maxContractTypes)
    listOfTypes   <- Gen.listOfN[ContractType](amount, genContractType)
    contractTypes <- ContractTypes(listOfTypes.toSet).gen
  } yield contractTypes

  implicit lazy val genWorkPlace: Gen[Workplace] =
    Gen.oneOf(Workplace.validWorkplaces).flatMap(Workplace.apply(_).gen)

  implicit lazy val genHourRange: Gen[Range[Hour]] = for {
    minimum      <- Gen.chooseNum(1, 40)
    maximum      <- Gen.chooseNum(minimum, 40)
    minimumHours <- Hour(minimum).gen
    maximumHours <- Hour(maximum).gen
    hourRange    <- Range.apply(minimumHours, maximumHours).gen
  } yield hourRange

  implicit lazy val genSalaryPeriod: Gen[SalaryPeriod] = Gen.oneOf(SalaryPeriod.all.values)

  implicit lazy val genSalaryRange: Gen[SalaryRange] = for {
    salaryPeriod  <- genSalaryPeriod
    minimum       <- Gen.chooseNum(1, 100000)
    maximum       <- Gen.chooseNum(minimum, 100000)
    minimumSalary <- Salary(minimum).gen
    maximumSalary <- Salary(maximum).gen
    salaryRange   <- SalaryRange.apply(minimumSalary, maximumSalary, salaryPeriod).gen
  } yield salaryRange

  implicit lazy val genLocation: Gen[Zipcode] = Gen.alphaNumStr.map(Zipcode.apply)

  implicit lazy val genApplyViaJobBoard: Gen[ApplyViaJobBoard] = for {
    fName <- Gen.alphaNumStr
    lName <- Gen.alphaNumStr
    email <- Gen.alphaNumStr
  } yield ApplyViaJobBoard(fName, lName, email)

  implicit lazy val genWebsite: Gen[Website] = Gen.oneOf(websiteUrls).flatMap(Website.apply(_).gen)

  implicit lazy val genApplyViaExternalWebsite: Gen[ApplyViaExternalWebsite] = genWebsite.map(ApplyViaExternalWebsite.apply)

  implicit lazy val genEasyApply: Gen[EasyApply.type] = Gen.const(EasyApply)

  implicit lazy val genApplicationMethod: Gen[ApplicationMethod] =
    Gen.oneOf(genApplyViaExternalWebsite, genApplyViaJobBoard, genEasyApply)

  implicit lazy val genLogo: Gen[Logo] = Gen.oneOf(imageUrls).flatMap(Logo.apply(_).gen)

  implicit lazy val genVideo: Gen[Video] =
    Gen.oneOf(videoUrls).flatMap(Video.apply(_).gen)

  implicit lazy val genPostalAddress: Gen[PostalAddress] = for {
    streetAndNumber <- Gen.alphaNumStr
    city            <- Gen.alphaNumStr
    zipCode         <- Gen.alphaNumStr
  } yield PostalAddress(streetAndNumber, city, zipCode)

  implicit lazy val genDomesticAddress: Gen[DomesticAddress] = for {
    street      <- Gen.alphaNumStr
    houseNumber <- Gen.alphaNumStr
    addition    <- Gen.option(Gen.alphaNumStr)
    zipCode     <- Gen.alphaNumStr
    city        <- Gen.alphaNumStr
  } yield DomesticAddress(street, houseNumber, addition, zipCode, city)

  implicit lazy val genISOAlpha2: Gen[ISOAlpha2] = Gen.alphaNumStr.map(ISOAlpha2.apply)

  implicit lazy val genForeignAddress: Gen[ForeignAddress] = for {
    address <- Gen.alphaNumStr
    code    <- genISOAlpha2
  } yield ForeignAddress(address, code)

  implicit lazy val genAddress: Gen[Address] = Gen.oneOf(genPostalAddress, genDomesticAddress, genForeignAddress)

  implicit lazy val genCompanyType: Gen[CompanyType] =
    Gen.oneOf(CompanyType.validCompanyTypes).flatMap(CompanyType.apply(_).gen)

  implicit lazy val genCompany: Gen[Company] = for {
    name        <- Gen.alphaNumStr
    address     <- Gen.option(genAddress)
    companyType <- genCompanyType
    website     <- Gen.option(genWebsite)
  } yield Company(name, address, companyType, website)

  implicit lazy val genSite: Gen[Site] = Gen.oneOf(Site.allowedSites).flatMap(Site.apply(_).gen)

  implicit lazy val genFeature: Gen[Feature] = Gen.oneOf(Feature.allowedFeatures).flatMap(Feature.apply(_).gen)

  implicit lazy val genConfiguration: Gen[JobPosting] = for {
    amountOfSites       <- Gen.choose(1, Site.allowedSites.size)
    amountOfFeatures    <- Gen.choose(0, Feature.allowedFeatures.size)
    publishOn           <- Gen.listOfN(amountOfSites, genSite)
    publicationDuration <- Gen.choose(1, 356)
    features            <- Gen.listOfN(amountOfFeatures, genFeature)
  } yield JobPosting(publishOn.toSet, publicationDuration.days, features.toSet)

  implicit lazy val genDefaults: Gen[Defaults] = for {
    title              <- Gen.option(Gen.alphaNumStr)
    description        <- Gen.option(genHtml)
    occupation         <- Gen.option(genOccupation)
    jobCategories      <- Gen.option(genJobCategories)
    industryCategories <- Gen.option(genIndustryCategories)
    educationLevels    <- Gen.option(genEducationLevels)
    careerLevel        <- Gen.option(genCareerLevel)
    contractTypes      <- Gen.option(genContractTypes)
    workplace          <- Gen.option(genWorkPlace)
    workingHours       <- Gen.option(genHourRange)
    salary             <- Gen.option(genSalaryRange)
    location           <- Gen.option(genLocation)
    applicationMethod  <- Gen.option(genApplicationMethod)
    logo               <- Gen.option(genLogo)
    video              <- Gen.option(genVideo)
    company            <- Gen.option(genCompany)
    configuration      <- Gen.option(genConfiguration)
  } yield Defaults(
    title,
    description,
    occupation,
    jobCategories,
    industryCategories,
    educationLevels,
    careerLevel,
    contractTypes,
    workplace,
    workingHours,
    salary,
    location,
    applicationMethod,
    logo,
    video,
    company,
    configuration
  )
}
