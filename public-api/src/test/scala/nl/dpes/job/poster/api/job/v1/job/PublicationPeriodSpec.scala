package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.job.v1.job.Date.formatter
import nl.dpes.job.poster.api.service.Cursor
import weaver._
import nl.dpes.job.poster.api.service.shared.{Date => AppDate, PublicationPeriod => AppPublicationPeriod}

import java.time.LocalDate
import scala.concurrent.duration._

object PublicationPeriodSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  test("A publicationperiod can be decoded") {
    val parsed: Either[Error, PublicationPeriod] = parser
      .parse("""{"start":"2022-04-12","end":"2022-06-12"}""")
      .flatMap(_.as[PublicationPeriod])

    val expected = (Date("2022-04-12"), Date("2022-06-12").map(_.some)).tupled.andThen { case (start, end) =>
      PublicationPeriod(start, end)
    }.toEither

    expect(parsed == expected)
  }

  test("A publication period can be decoded without end date") {
    expect(
      parser
        .parse("""{"start":"2022-04-12"}""")
        .flatMap(_.as[PublicationPeriod]) == Date("2022-04-12").toEither.flatMap(startDate => PublicationPeriod(startDate).toEither)
    )
  }

  test("A publication period fails decoding with invalid end date") {
    expect(
      parser
        .parse("""{"start":"2022-04-12", "end":"2021-04-12"}""")
        .flatMap(_.as[PublicationPeriod]) == DecodingFailure(
        "End date '2021-04-12' cannot be before start date '2022-04-12'.",
        List()
      ).asLeft
    )
  }

  test("A publicationperiod can be encoded") {
    val unEncoded = (Date("2022-04-12"), Date("2022-06-12").map(_.some)).tupled.andThen { case (start, end) =>
      PublicationPeriod(start, end)
    }.toEither

    expect(unEncoded.map(_.asJson.noSpaces) == """{"start":"2022-04-12","end":"2022-06-12"}""".asRight)
  }

  test("An enddate cannot be before startdate") {
    val invalidPeriod = for {
      start  <- Date("2020-12-12").toEither
      end    <- Date("1984-08-04").toEither
      period <- PublicationPeriod(start, end.some).toEither
    } yield period
    expect(invalidPeriod == "End date '1984-08-04' cannot be before start date '2020-12-12'.".asLeft)
  }

  test("when no enddate is specified an enddate can be calculated based on the duration") {
    val period = for {
      start  <- Date("2020-12-12").toEither
      period <- PublicationPeriod(start, None).toEither
    } yield period

    expect(period.map(_.endForDuration(23.days)) == Date("2021-01-04").toEither)
  }

  test("when an enddate is specified it will be returned") {
    val period = for {
      start  <- Date("2020-12-12").toEither
      end    <- Date("2023-12-25").toEither
      period <- PublicationPeriod(start, end.some).toEither
    } yield period

    expect(period.map(_.endForDuration(23.days)) == Date("2023-12-25").toEither)
  }

  test("Mapping PublicationPeriod from API to Application model") {
    val actualAppPublicationPeriod = PublicationPeriod.map(PublicationPeriod.SwaggerDoc.example.some)
    val expectedAppPublicationPeriod =
      AppPublicationPeriod(
        AppDate(LocalDate.now().plusDays(5).format(formatter)).toOption.get,
        AppDate(LocalDate.now().plusDays(35).format(formatter)).toOption
      ).some.sequence.toValidatedNec

    expect(actualAppPublicationPeriod == expectedAppPublicationPeriod)
  }
}
