package nl.dpes.job.poster.api.reference_id

import cats.effect.IO
import cats.implicits.catsSyntaxOptionId
import nl.dpes.b2b.salesforce.domain.SalesForceId
import org.mockito.MockitoSugar.{mock, when}
import nl.dpes.job.poster.api.job.v1.job.{JobId => ApiJobId, ReferenceId => ApiReferenceId}
import nl.dpes.job.poster.api.reference_id.ReferenceIdRepository.DuplicateJobId
import weaver.SimpleIOSuite

object ReferenceIdServiceSpec extends SimpleIOSuite {

  case class Error(message: String) extends Throwable(message)

  val referenceIdRepository: ReferenceIdRepository[IO] = mock[ReferenceIdRepository[IO]]
  val referenceIdService: ReferenceIdService[IO]       = ReferenceIdService.impl(referenceIdRepository)
  val recruiterId: RecruiterId                         = RecruiterId("0038E00001JQPCnQAP")
  val recruiter: SalesForceId                          = SalesForceId.unsafeApply("0038E00001JQPCnQAP")
  val referenceId: ApiReferenceId                      = ApiReferenceId("referenceId").toOption.get
  val appReferenceId: ReferenceId                      = ReferenceId("referenceId")
  val jobId: ApiJobId                                  = ApiJobId("jobId")
  val appJobId: JobId                                  = JobId("jobId")
  val unit: Unit                                       = ()

  test("It should be able to store a referenceId") {
    when(referenceIdRepository.store(recruiterId, ReferenceId(referenceId.value), JobId(jobId.value)))
      .thenReturn(IO.unit)
    for {
      result <- referenceIdService.store(recruiter, referenceId, jobId)
    } yield expect(result == unit)
  }

  test("It should return an error when the job_id has been already stored") {
    when(referenceIdRepository.store(recruiterId, ReferenceId(referenceId.value), JobId(jobId.value)))
      .thenReturn(IO.raiseError(DuplicateJobId(JobId(jobId.value))))
    for {
      result <- referenceIdService.store(recruiter, referenceId, jobId).attempt
    } yield expect(result == Left(DuplicateJobId(JobId(jobId.value))))
  }

  test("It should be able to return a reference_id by job_id") {
    when(referenceIdRepository.getReferenceId(JobId(jobId.value))).thenReturn(IO(ReferenceId(referenceId.value).some))
    for {
      result <- referenceIdService.getReferenceId(jobId)
    } yield expect(result == ReferenceId(referenceId.value).some)
  }

  test("It should return None when no reference_id has been found for a specific job_id") {
    when(referenceIdRepository.getReferenceId(JobId(jobId.value))).thenReturn(IO.none)
    for {
      result <- referenceIdService.getReferenceId(jobId)
    } yield expect(result.isEmpty)
  }

  test("It should return an error when the referenceId repo encountered an issue") {
    when(referenceIdRepository.getReferenceId(JobId(jobId.value))).thenReturn(IO.raiseError(Error("Error occurred")))
    for {
      result <- referenceIdService.getReferenceId(jobId).attempt
    } yield expect(result == Left(Error("Error occurred")))
  }

  test("It should be able to return the latest jobId") {
    when(referenceIdRepository.getLatestJobId(appReferenceId, recruiterId)).thenReturn(IO(Some(appJobId)))
    for {
      result <- referenceIdService.getLatestJobId(recruiter, referenceId)
    } yield expect(result.contains(appJobId))
  }

  test("It should return an error when the referenceId repo encountered an issue while tying to get the latest jobId") {
    when(referenceIdRepository.getLatestJobId(appReferenceId, recruiterId)).thenReturn(IO.raiseError(Error("Error occurred")))
    for {
      result <- referenceIdService.getLatestJobId(recruiter, referenceId).attempt
    } yield expect(result == Left(Error("Error occurred")))
  }

  test("It should be able to delete a referenceId with a specific jobId") {
    when(referenceIdRepository.delete(recruiterId, appReferenceId, appJobId)).thenReturn(IO.unit)
    for {
      result <- referenceIdService.delete(recruiter, referenceId, jobId)
    } yield expect(result == unit)
  }

  test("It should return an error when unable to delete a referenceId with a specific jobId") {
    when(referenceIdRepository.delete(recruiterId, appReferenceId, appJobId)).thenReturn(IO.raiseError(Error("Error occurred")))
    for {
      result <- referenceIdService.delete(recruiter, referenceId, jobId).attempt
    } yield expect(result == Left(Error("Error occurred")))
  }
}
