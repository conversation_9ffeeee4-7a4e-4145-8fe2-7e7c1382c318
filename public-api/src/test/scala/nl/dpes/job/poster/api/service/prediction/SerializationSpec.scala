package nl.dpes.job.poster.api.service.prediction

import io.circe.parser._
import io.circe.syntax._
import weaver.FunSuite

object SerializationSpec extends FunSuite {
  test("Serializing a JobTitle should return a string") {
    expect(JobTitle("a title").asJson.noSpaces == """"a title"""")
  }

  test("Serializing a JobDescription should return a string") {
    expect(JobDescription("a description").asJson.noSpaces == """"a description"""")
  }

  test("An IndustryCategory can be deserialized from a string") {
    expect(decode[IndustryCategory](""""a branche"""") == Right(IndustryCategory("a branche")))
  }
}
