package nl.dpes.job.poster.api.job.v1

import cats.data.Validated
import cats.effect.IO
import cats.implicits.{catsSyntaxOptionId, none}
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api._
import nl.dpes.job.poster.api.job.v1.ControllerService.NonRepostableJob
import nl.dpes.job.poster.api.job.v1.job.JobStatus.{Resume, Suspend}
import nl.dpes.job.poster.api.job.v1.job.SummaryStatus.{Published => ApiPublished}
import nl.dpes.job.poster.api.job.v1.job.{
  Job,
  JobCreated,
  JobStatus,
  JobUpdate,
  ReferenceId,
  Date => ApiDate,
  JobId => ApiJobId,
  JobSummary => ApiJobSummary,
  JobTitle => ApiJobTitle,
  PublicationPeriod => ApiPublicationPeriod
}
import nl.dpes.job.poster.api.reference_id.{ReferenceIdService, JobId => RefJobId}
import nl.dpes.job.poster.api._
import nl.dpes.job.poster.api.job.v1.ControllerService.NonRepostableJob
import nl.dpes.job.poster.api.job.v1.job.ReferenceId.{EmptyReferenceId, InvalidReferenceIdLength}
import nl.dpes.job.poster.api.service.addressservice.{AddressInfo, JobAddressService}
import nl.dpes.job.poster.api.service.job.{JobSummary, Job => AppJob, JobUpdate => AppJobUpdate}
import nl.dpes.job.poster.api.service.jobmanager.JobManager.{
  InvalidData,
  JobCannotBeResumed,
  JobCannotBeSuspended,
  JobCannotBeUpdated,
  JobManagerError,
  JobNotFound,
  NoJobOwnership,
  NoSuitableCredits,
  NonUpdatableJob,
  NotAJob
}
import nl.dpes.job.poster.api.service.logo.LogoService.RetrieveLogoError
import nl.dpes.job.poster.api.service.occupationclassification.OccupationClassifier.{UnclassifiedPBPJob, UnrecognizedJob}
import nl.dpes.job.poster.api.service.recruiter.AccessToken
import nl.dpes.job.poster.api.service.shared.JobStatus.{Deleted, Expired, Published, Suspended}
import nl.dpes.job.poster.api.service.shared.{Configuration, JobId, JobTitle, Logo, PublicationPeriod, Date => AppDate}
import nl.dpes.job.poster.api.service.uuid.UuidGenerator
import nl.dpes.job.poster.api.service.{BearerToken, Cursor, JobPosterServiceFactory, MappingError, JobPosterService => ApplicationService}
import nl.dpes.job.poster.api.shutdownguard.{ShutdownGuard, TestRuntime}
import nl.dpes.job.poster.api.tracing.{CorrelationId, CorrelationLoggerFactory, RequestId}
import org.mockito.ArgumentMatchers._
import org.mockito.MockitoSugar._
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver._

import java.util.UUID

object ControllerServiceSpec extends SimpleIOSuite {

  case class ServiceError(message: String) extends Throwable(message)

  implicit def loggerFactory: Slf4jFactory[IO] = Slf4jFactory.create[IO]

  implicit val cursor: Cursor                        = Cursor("body")
  val jobId                                          = "valid-job-id"
  val bearerToken: BearerToken                       = BearerToken("this-is-a-bearer-token")
  val accessToken: AccessToken                       = AccessToken("this-is-an-access-token")
  val recruiterId: SalesForceId                      = SalesForceId.unsafeApply("0038E00001JQPCnQAP")
  val correlationId: CorrelationId                   = CorrelationId.generate
  val requestId: RequestId                           = RequestId.generate
  val apiJob: Job                                    = Job.SwaggerDoc.jobExample.copy(publicationPeriod = None)
  val apiUpdateJob: JobUpdate                        = JobUpdate.SwaggerDoc.example.copy(publicationPeriod = None)
  val referenceId: Validated[Throwable, ReferenceId] = ReferenceId("1234-5698")
  val addressInfo: AddressInfo                       = AddressInfo("1018LL")

  val mockedReferenceIdServiceF: IO[ReferenceIdService[IO]] = IO(mock[ReferenceIdService[IO]])

  implicit class referenceIdServiceExtension(val serviceF: IO[ReferenceIdService[IO]]) extends AnyVal {

    def getJobIdStub(returnValue: Option[RefJobId]): IO[ReferenceIdService[IO]] =
      serviceF.map { service =>
        when(service.getLatestJobId(any[SalesForceId], new ReferenceId(any[String]))).thenReturn(IO(returnValue))
        service
      }

    def referencingJobId(jobId: ApiJobId): IO[ReferenceIdService[IO]] =
      getJobIdStub(RefJobId(jobId.value).some)

    def notReferencingJobId: IO[ReferenceIdService[IO]] =
      getJobIdStub(none)
  }

  val jobSummary: JobSummary = (for {
    start             <- AppDate("2022-04-12").toEither
    end               <- AppDate("2022-06-12").toEither
    publicationPeriod <- PublicationPeriod(start, end.some).toEither
  } yield JobSummary(JobTitle("Scala developer"), Published, publicationPeriod)).toOption.get

  val apiJobSummary: ApiJobSummary = (for {
    start             <- ApiDate("2022-04-12").toEither
    end               <- ApiDate("2022-06-12").toEither
    publicationPeriod <- ApiPublicationPeriod(start, end.some).toEither
  } yield ApiJobSummary(ApiJobTitle("Scala developer"), ApiPublished, publicationPeriod)).toOption.get

  val addressService: JobAddressService[IO] = mock[JobAddressService[IO]]

  test("It should be able to create a job successfully without setting a reference_id") {

    for {
      referenceIdService  <- mockedReferenceIdServiceF
      _                   <- IO(when(referenceIdService.getLatestJobId(recruiterId, referenceId.toOption.get)).thenReturn(IO.none))
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      appService          <- IO(mock[ApplicationService[IO]])
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.unit)
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob.copy(referenceId = None)))
        } yield result
      )
    } yield expect(result.map(_._1) == Right(s"/api/v1/job/${uuid.toString}"))
  }

  test("It should be able to create a job successfully with a reference_id job reference as a result") {

    for {
      referenceIdService  <- mockedReferenceIdServiceF
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      _                   <- IO(when(referenceIdService.getLatestJobId(recruiterId, referenceId.toOption.get)).thenReturn(IO.none))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      appService          <- IO(mock[ApplicationService[IO]])
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.unit)
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob.copy(referenceId = referenceId.toOption)))
        } yield result
      )
    } yield expect(result.map(_._1) == Right(s"/api/v1/job/${referenceId.toOption.get.value}"))
  }

  test(
    "It should be able to create a job successfully with job_id job reference as a result, when the reference_id contains only whitespaces"
  ) {
    for {
      referenceIdService  <- mockedReferenceIdServiceF
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      appService          <- IO(mock[ApplicationService[IO]])
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.unit)
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob.copy(referenceId = ReferenceId("     ").toOption)))
        } yield result
      )
    } yield expect(result.map(_._1) == Right(s"/api/v1/job/${uuid.toString}"))
  }

  test("It should detect if data is invalid when trying to post a job") {
    for {
      referenceIdService  <- mockedReferenceIdServiceF
      _                   <- IO(when(referenceIdService.getLatestJobId(recruiterId, referenceId.toOption.get)).thenReturn(IO.none))
      appService          <- IO(mock[ApplicationService[IO]])
      error               <- IO(InvalidData("Invalid input"))
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(referenceIdService.delete(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenReturn(IO.raiseError(error))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(result == Left(BadRequest("Job manager did not accept the data: 'Invalid input'")))
  }

  test("It should detect if an unknown error occurred when trying to post a job") {
    for {
      referenceIdService  <- mockedReferenceIdServiceF
      _                   <- IO(when(referenceIdService.getLatestJobId(recruiterId, referenceId.toOption.get)).thenReturn(IO.none))
      appService          <- IO(mock[ApplicationService[IO]])
      error               <- IO(new Throwable("System error"))
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(referenceIdService.delete(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenReturn(IO.raiseError(error))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(result == Left(Unknown("An error occurred while posting the job")))
  }

  test(
    "It should return a NonRepostableJob exception when trying to repost a job with the same referenceId, having a status different from 'Deleted' or 'Expired'"
  ) {
    for {
      referenceIdService <- mockedReferenceIdServiceF
      _                  <- IO(when(referenceIdService.getLatestJobId(recruiterId, referenceId.toOption.get)).thenReturn(IO(RefJobId(jobId).some)))
      appService         <- IO(mock[ApplicationService[IO]])
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary)))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(
      result == Left(
        StatusConflict(NonRepostableJob(referenceId.toOption.get, RefJobId(jobId), jobSummary.status).getMessage, jobSummary.status)
      )
    )
  }

  test("It should post the job with a referenceId, after receiving a 'JobNotFound' error when attempting to repost the job") {
    for {
      referenceIdService  <- mockedReferenceIdServiceF
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(referenceIdService.getLatestJobId(recruiterId, referenceId.toOption.get)).thenReturn(IO(RefJobId(uuid.toString).some)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(referenceIdService.delete(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      appService          <- IO(mock[ApplicationService[IO]])
      appServiceFactory   <- IO(mock[JobPosterServiceFactory[IO]])
      _                   <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      _                   <- IO(when(appService.read(recruiterId, JobId(uuid.toString))).thenReturn(IO.raiseError(JobNotFound(JobId(uuid.toString)))))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.unit)
      )
      runtime <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(
      result == Right(
        (
          s"/api/v1/job/${referenceId.toOption.get.value}",
          RequestId(requestId.value),
          JobCreated(ApiJobId(uuid.toString))
        )
      )
    )
  }

  test("It should be able to repost a deleted job with the same referenceId") {
    for {
      referenceIdService  <- mockedReferenceIdServiceF
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(referenceIdService.getLatestJobId(recruiterId, referenceId.toOption.get)).thenReturn(IO(RefJobId(uuid.toString).some)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      appService          <- IO(mock[ApplicationService[IO]])
      appServiceFactory   <- IO(mock[JobPosterServiceFactory[IO]])
      _                   <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      _                   <- IO(when(appService.read(recruiterId, JobId(uuid.toString))).thenReturn(IO(jobSummary.copy(status = Deleted))))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.unit)
      )
      runtime <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(
      result == Right(
        (
          s"/api/v1/job/${referenceId.toOption.get.value}",
          RequestId(requestId.value),
          JobCreated(ApiJobId(uuid.toString))
        )
      )
    )
  }

  test("It should be able to repost an expired job with the same referenceId") {
    for {
      referenceIdService  <- mockedReferenceIdServiceF
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(referenceIdService.getLatestJobId(recruiterId, referenceId.toOption.get)).thenReturn(IO(RefJobId(uuid.toString).some)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      appService          <- IO(mock[ApplicationService[IO]])
      appServiceFactory   <- IO(mock[JobPosterServiceFactory[IO]])
      _                   <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      _                   <- IO(when(appService.read(recruiterId, JobId(uuid.toString))).thenReturn(IO(jobSummary.copy(status = Expired))))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.unit)
      )
      runtime <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(
      result == Right(
        (
          s"/api/v1/job/${referenceId.toOption.get.value}",
          RequestId(requestId.value),
          JobCreated(ApiJobId(uuid.toString))
        )
      )
    )
  }

  test("It should return an error when trying to post a poorly-described content that cannot be considered as a job") {
    for {
      referenceIdService  <- IO(mock[ReferenceIdService[IO]]).notReferencingJobId
      appService          <- IO(mock[ApplicationService[IO]])
      error               <- IO(NotAJob("The provided content is not a job"))
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(referenceIdService.delete(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.raiseError(error))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(result == Left(Teapot("The provided content is not a job")))
  }

  test("It should detect if a mapping error has occurred when trying to post a job") {
    for {
      referenceIdService  <- mockedReferenceIdServiceF
      appService          <- IO(mock[ApplicationService[IO]])
      _                   <- IO(when(referenceIdService.getLatestJobId(recruiterId, referenceId.toOption.get)).thenReturn(IO.none))
      errors              <- IO(MappingError(List(Cursor("body")("video") -> "Cannot parse url 'video'.")))
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(referenceIdService.delete(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.raiseError(errors))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(result == Left(BadRequest("Invalid value for: body (Cannot parse url 'video'. at video)")))
  }

  test("It should detect if a mapping error has occurred when trying to update a job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.notReferencingJobId
      appService         <- IO(mock[ApplicationService[IO]])
      errors             <- IO(MappingError(List(Cursor("body")("video") -> "Cannot parse url 'video'.")))
      _                  <- IO(when(appService.update(any[SalesForceId], JobId(any[String]), any[AppJobUpdate])).thenAnswer(IO.raiseError(errors)))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.update(recruiterId)((requestId, correlationId, ApiJobId(jobId), apiUpdateJob))
        } yield result
      )
    } yield expect(result == Left(BadRequest("Invalid value for: body (Cannot parse url 'video'. at video)")))
  }

  test("It should detect if the recruiter has not enough credits") {
    for {
      referenceIdService  <- IO(mock[ReferenceIdService[IO]]).notReferencingJobId
      appService          <- IO(mock[ApplicationService[IO]])
      error               <- IO(NoSuitableCredits("Not enough credits"))
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(referenceIdService.delete(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.raiseError(error))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(result == Left(Forbidden("We couldn't post your job as you didn't have sufficient credits of the right type to post")))
  }

  test("It should be able to detect if the pbp job is unclassified") {
    for {
      referenceIdService  <- mockedReferenceIdServiceF.notReferencingJobId
      appService          <- IO(mock[ApplicationService[IO]])
      error               <- IO(UnclassifiedPBPJob)
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(referenceIdService.delete(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.raiseError(error))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(result == Left(BadRequest(UnclassifiedPBPJob.getMessage)))
  }

  test("It should be able to detect if the retrieval of a logo failed when trying to post a job") {
    for {
      referenceIdService  <- mockedReferenceIdServiceF.notReferencingJobId
      appService          <- IO(mock[ApplicationService[IO]])
      logo                <- IO.fromEither(Logo("http://www.logo.com").toEither.left.map(error => new Throwable(error)))
      error               <- IO(RetrieveLogoError(logo))
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(referenceIdService.delete(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.raiseError(error))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(result == Left(BadRequest("Could not retrieve logo from 'http://www.logo.com'")))
  }

  test("It should be able to detect if no occupation was provided and no classification was computed") {
    for {
      referenceIdService  <- mockedReferenceIdServiceF.notReferencingJobId
      appService          <- IO(mock[ApplicationService[IO]])
      error               <- IO(UnrecognizedJob)
      mockedUuidGenerator <- IO(mock[UuidGenerator[IO]])
      uuid                <- IO(UUID.randomUUID())
      _                   <- IO(when(mockedUuidGenerator.generate).thenReturn(IO(uuid)))
      _                   <- IO(when(referenceIdService.store(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _                   <- IO(when(referenceIdService.delete(recruiterId, referenceId.toOption.get, ApiJobId(uuid.toString))).thenReturn(IO.unit))
      _ <- IO(
        when(appService.create(any[SalesForceId], JobId(any[String]), any[AppJob], any[Option[Configuration]], CorrelationId(any[String])))
          .thenAnswer(IO.raiseError(error))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, mockedUuidGenerator, addressService, referenceIdService, guard))
          result  <- service.create(recruiterId)((requestId, correlationId, apiJob))
        } yield result
      )
    } yield expect(result == Left(BadRequest(UnrecognizedJob.getMessage)))
  }

  test("It should detect if job manager has an issue with the data provided during update") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.notReferencingJobId
      appService         <- IO(mock[ApplicationService[IO]])
      error              <- IO(InvalidData("Invalid input"))
      _                  <- IO(when(appService.update(any[SalesForceId], JobId(any[String]), any[AppJobUpdate])).thenReturn(IO.raiseError(error)))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.update(recruiterId)((requestId, correlationId, ApiJobId(jobId), apiUpdateJob))
        } yield result
      )
    } yield expect(result == Left(BadRequest("Job manager did not accept the data: 'Invalid input'")))
  }

  test("It should detect if job manager detected a Forbidden error during update") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.notReferencingJobId
      appService         <- IO(mock[ApplicationService[IO]])
      error              <- IO(JobCannotBeUpdated("Cannot update"))
      _                  <- IO(when(appService.update(any[SalesForceId], JobId(any[String]), any[AppJobUpdate])).thenReturn(IO.raiseError(error)))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.update(recruiterId)((requestId, correlationId, ApiJobId(jobId), apiUpdateJob))
        } yield result
      )
    } yield expect(result == Left(Forbidden("Cannot update")))
  }

  test("It should detect if job manager detected a Conflict error when trying to update a deleted job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.notReferencingJobId
      appService         <- IO(mock[ApplicationService[IO]])
      error              <- IO(NonUpdatableJob("Cannot update"))
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary.copy(status = Deleted))))
      _                  <- IO(when(appService.update(any[SalesForceId], JobId(any[String]), any[AppJobUpdate])).thenReturn(IO.raiseError(error)))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.update(recruiterId)((requestId, correlationId, ApiJobId(jobId), apiUpdateJob))
        } yield result
      )
    } yield expect(result == Left(StatusConflict("Cannot update", Deleted)))
  }

  test("It should detect if job manager detected a Conflict error when trying to update a expired job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.notReferencingJobId
      appService         <- IO(mock[ApplicationService[IO]])
      error              <- IO(NonUpdatableJob("Cannot update"))
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary.copy(status = Expired))))
      _                  <- IO(when(appService.update(any[SalesForceId], JobId(any[String]), any[AppJobUpdate])).thenReturn(IO.raiseError(error)))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.update(recruiterId)((requestId, correlationId, ApiJobId(jobId), apiUpdateJob))
        } yield result
      )
    } yield expect(result == Left(StatusConflict("Cannot update", Expired)))
  }

  test("It should be able to detect if the retrieval of a logo failed when trying to update a job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.notReferencingJobId
      appService         <- IO(mock[ApplicationService[IO]])
      logo               <- IO.fromEither(Logo("http://www.logo.com").toEither.left.map(error => new Throwable(error)))
      error              <- IO(RetrieveLogoError(logo))
      _                  <- IO(when(appService.update(any[SalesForceId], JobId(any[String]), any[AppJobUpdate])).thenAnswer(IO.raiseError(error)))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.update(recruiterId)((requestId, correlationId, ApiJobId(jobId), apiUpdateJob))
        } yield result
      )
    } yield expect(result == Left(BadRequest("Could not retrieve logo from 'http://www.logo.com'")))
  }

  test("It should detect if an unknown error occurred when trying to update a job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.notReferencingJobId
      appService         <- IO(mock[ApplicationService[IO]])
      error              <- IO(new Throwable("System error"))
      _                  <- IO(when(appService.update(any[SalesForceId], JobId(any[String]), any[AppJobUpdate])).thenAnswer(IO.raiseError(error)))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.update(recruiterId)((requestId, correlationId, ApiJobId(jobId), apiUpdateJob))
        } yield result
      )
    } yield expect(result == Left(Unknown("An error occurred while updating the job")))
  }

  test("It should be able to delete a job successfully when provided with a valid bearer token and job id") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.notReferencingJobId
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.delete(any[SalesForceId], any[String])).thenAnswer(IO.unit))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.delete(recruiterId)(requestId, correlationId, ApiJobId(jobId))
        } yield result
      )
    } yield expect(result == Right(()))
  }

  test("It should be able to delete a job successfully when provided with a valid bearer token and reference id") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.delete(any[SalesForceId], any[String])).thenAnswer(IO.unit))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.delete(recruiterId)(requestId, correlationId, ApiJobId(jobId))
        } yield result
      )
    } yield expect(result == Right(()))
  }

  test("It should detect if an unknown error occurred when trying to delete a job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      error              <- IO(new Throwable("System error"))
      _                  <- IO(when(appService.delete(any[SalesForceId], any[String])).thenAnswer(IO.raiseError(error)))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.delete(recruiterId)(requestId, correlationId, ApiJobId(jobId))
        } yield result
      )
    } yield expect(result == Left(Unknown("An error occurred while deleting the job")))
  }

  test("It should be able to return a Forbidden error when attempting to delete a job that doesn't belong to the recruiter") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      error              <- IO(NoJobOwnership("This job doesn't belong to this recruiter"))
      _                  <- IO(when(appService.delete(any[SalesForceId], any[String])).thenAnswer(IO.raiseError(error)))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.delete(recruiterId)(requestId, correlationId, ApiJobId(jobId))
        } yield result
      )
    } yield expect(result == Left(Forbidden("This job doesn't belong to this recruiter")))
  }

  test("It should be able to suspend a job successfully when provided with a valid bearer token, job id and Suspend job status") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Suspend.type])).thenAnswer(IO.unit))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Suspend)
        } yield result
      )
    } yield expect(result == Right(()))
  }

  test("It should not be able to suspend a job when an error has occurred") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary)))
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Suspend.type]))
          .thenAnswer(IO.raiseError(JobCannotBeSuspended("error")))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Suspend)
        } yield result
      )
    } yield expect(result == Left(StatusConflict("error", Published)))
  }

  test("It should not be able to suspend a job when a job was not found") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Suspend.type]))
          .thenAnswer(IO.raiseError(JobNotFound(JobId("123"))))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Suspend)
        } yield result
      )
    } yield expect(result == Left(NotFound(s"Job '123' not found.")))
  }

  test("It should not be able to suspend a job when a job is expired") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      expiredJobId = "expired-job-id"
      appService <- IO(mock[ApplicationService[IO]])
      _          <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary.copy(status = Expired))))
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Suspend.type]))
          .thenAnswer(IO.raiseError(JobCannotBeSuspended("error")))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(expiredJobId), Suspend)
        } yield result
      )
    } yield expect(result == Left(StatusConflict("error", Expired)))
  }

  test("It should be able to resume a job successfully when provided with a valid bearer token, job id and Resume job status") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.notReferencingJobId
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Resume.type])).thenAnswer(IO.unit))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Resume)
        } yield result
      )
    } yield expect(result == Right(()))
  }

  test("It should be able to resume a job successfully when provided with a valid bearer token, reference id and Resume job status") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Resume.type])).thenAnswer(IO.unit))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Resume)
        } yield result
      )
    } yield expect(result == Right(()))
  }

  test("It should not be able to resume a job when an error has occurred") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary.copy(status = Suspended))))
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Suspend.type]))
          .thenAnswer(IO.raiseError(JobCannotBeResumed("error")))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Resume)
        } yield result
      )
    } yield expect(result == Left(StatusConflict("error", Suspended)))
  }

  test("It should detect if a job-manager error occurred when trying to resume of a job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary.copy(status = Suspended))))
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Resume.type]))
          .thenAnswer(IO.raiseError(JobManagerError(jobId, "error")))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Resume)
        } yield result
      )
    } yield expect(result == Left(Unknown("An error occurred while updating the status of the job to 'Resume'")))
  }

  test("It should detect if an error occurred when trying to resume of a job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary.copy(status = Suspended))))
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Resume.type]))
          .thenAnswer(IO.raiseError(new Throwable("error")))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Resume)
        } yield result
      )
    } yield expect(result == Left(Unknown("An error occurred while updating the status of the job to 'Resume'")))
  }

  test("It should detect if a job-manager error occurred when trying to suspend of a job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary.copy(status = Suspended))))
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Suspend.type]))
          .thenAnswer(IO.raiseError(JobManagerError(jobId, "error")))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Suspend)
        } yield result
      )
    } yield expect(result == Left(Unknown("An error occurred while updating the status of the job to 'Suspend'")))
  }

  test("It should detect if an error occurred when trying to suspend of a job") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary.copy(status = Suspended))))
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Suspend.type]))
          .thenAnswer(IO.raiseError(new Throwable("error")))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Suspend)
        } yield result
      )
    } yield expect(result == Left(Unknown("An error occurred while updating the status of the job to 'Suspend'")))
  }

  test("It should not be able to resume a job when a job was not found") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      appService         <- IO(mock[ApplicationService[IO]])
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Resume.type]))
          .thenAnswer(IO.raiseError(JobNotFound(JobId("123"))))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(jobId), Resume)
        } yield result
      )
    } yield expect(result == Left(NotFound(s"Job '123' not found.")))
  }

  test("It should not be able to resume a job when a job is expired") {
    for {
      referenceIdService <- mockedReferenceIdServiceF.referencingJobId(ApiJobId(jobId))
      expiredJobId = "expired-job-id"
      appService <- IO(mock[ApplicationService[IO]])
      _          <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary.copy(status = Expired))))
      _ <- IO(
        when(appService.updateJobStatus(any[SalesForceId], any[String], any[JobStatus.Resume.type]))
          .thenAnswer(IO.raiseError(JobCannotBeResumed("error")))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.updateJobStatus(recruiterId)(requestId, correlationId, ApiJobId(expiredJobId), Resume)
        } yield result
      )
    } yield expect(result == Left(StatusConflict("error", Expired)))
  }

  test("It should be able get job summary by its referenceId") {
    for {
      referenceIdService <- mockedReferenceIdServiceF
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary)))
      _                  <- IO(when(referenceIdService.getLatestJobId(recruiterId, ReferenceId(jobId).toOption.get)).thenReturn(IO.some(RefJobId(jobId))))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.read(recruiterId)((requestId, correlationId, ApiJobId(jobId)))
        } yield result
      )
    } yield expect(result == Right((apiJobSummary, requestId)))
  }

  test("It should be able get job summary by its jobId") {
    for {
      referenceIdService <- mockedReferenceIdServiceF
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO(jobSummary)))
      _                  <- IO(when(referenceIdService.getLatestJobId(recruiterId, ReferenceId(jobId).toOption.get)).thenReturn(IO.none))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.read(recruiterId)((requestId, correlationId, ApiJobId(jobId)))
        } yield result
      )
    } yield expect(result == Right((apiJobSummary, requestId)))
  }

  test("It should return a not-found error when trying to fetch a non-existing job summary") {
    for {
      referenceIdService <- mockedReferenceIdServiceF
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO.raiseError(JobNotFound(JobId(jobId)))))
      _                  <- IO(when(referenceIdService.getLatestJobId(recruiterId, ReferenceId(jobId).toOption.get)).thenReturn(IO.none))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.read(recruiterId)((requestId, correlationId, ApiJobId(jobId)))
        } yield result
      )
    } yield expect(result == Left(NotFound(s"No summary found for job '$jobId'")))
  }

  test("It should return a bad-request error when trying to fetch a job summary with provided invalid data") {
    for {
      referenceIdService <- mockedReferenceIdServiceF
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO.raiseError(InvalidData("Invalid input"))))
      _                  <- IO(when(referenceIdService.getLatestJobId(recruiterId, ReferenceId(jobId).toOption.get)).thenReturn(IO.none))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.read(recruiterId)((requestId, correlationId, ApiJobId(jobId)))
        } yield result
      )
    } yield expect(result == Left(BadRequest("Invalid input")))
  }

  test("It should return a bad-request error when trying to fetch a job summary with an empty referenceId") {
    for {
      referenceIdService <- mockedReferenceIdServiceF
      appService         <- IO(mock[ApplicationService[IO]])
      _ <- IO(
        when(referenceIdService.getLatestJobId(recruiterId, ReferenceId(jobId).toOption.get)).thenReturn(IO.raiseError(EmptyReferenceId))
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.read(recruiterId)((requestId, correlationId, ApiJobId(jobId)))
        } yield result
      )
    } yield expect(result == Left(BadRequest("ReferenceId cannot be empty.")))
  }

  test("It should return a bad-request error when trying to fetch a job summary with an invalid referenceId") {
    for {
      referenceIdService <- mockedReferenceIdServiceF
      appService         <- IO(mock[ApplicationService[IO]])
      _ <- IO(
        when(referenceIdService.getLatestJobId(recruiterId, ReferenceId(jobId).toOption.get))
          .thenReturn(
            IO.raiseError(
              InvalidReferenceIdLength(
                "mvnwacdqzitsgbxmezxwxfwodskfwfyxsftaptfsgekomxrmowaqtfmrsbinhofxgkkmrpdcmxsvydgqigkqucvlphgaeknahdsxjhgpt"
              )
            )
          )
      )
      appServiceFactory <- IO(mock[JobPosterServiceFactory[IO]])
      _                 <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime           <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.read(recruiterId)((requestId, correlationId, ApiJobId(jobId)))
        } yield result
      )
    } yield expect(
      result == Left(BadRequest(s"ReferenceId length should be between 1 and 100 characters. The provided id length is '105' characters."))
    )
  }

  test("It should return an error when unable to fetch the job summary") {
    for {
      referenceIdService <- mockedReferenceIdServiceF
      appService         <- IO(mock[ApplicationService[IO]])
      _                  <- IO(when(appService.read(recruiterId, JobId(jobId))).thenReturn(IO.raiseError(new Throwable("Error fetching job summary"))))
      _                  <- IO(when(referenceIdService.getLatestJobId(recruiterId, ReferenceId(jobId).toOption.get)).thenReturn(IO.none))
      appServiceFactory  <- IO(mock[JobPosterServiceFactory[IO]])
      _                  <- IO(when(appServiceFactory.createService(CorrelationId(any[String]), any[CorrelationLoggerFactory[IO]])).thenAnswer(appService))
      runtime            <- TestRuntime.impl[IO]
      result <- ShutdownGuard(runtime).use(guard =>
        for {
          service <- IO(ControllerService.apply(appServiceFactory, UuidGenerator[IO], addressService, referenceIdService, guard))
          result  <- service.read(recruiterId)((requestId, correlationId, ApiJobId(jobId)))
        } yield result
      )
    } yield expect(result == Left(Unknown("An error occurred while fetching the job summary")))
  }
}
