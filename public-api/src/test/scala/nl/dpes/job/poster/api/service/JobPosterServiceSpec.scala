package nl.dpes.job.poster.api.service

import cats.effect.IO
import cats.effect.unsafe.implicits.global
import nl.dpes.b2b.salesforce.domain.{Recruiter, SalesForceId}
import nl.dpes.job.poster.api.job.v1.job.JobStatus.{Resume, Suspend}
import nl.dpes.job.poster.api.defaults.DefaultsRepository
import nl.dpes.job.poster.api.service.jobmanager.JobManager
import nl.dpes.job.poster.api.service.recruiter.{AccessToken, RecruiterService}
import nl.dpes.job.poster.api.service.shared.JobId
import org.mockito.MockitoSugar._
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import org.typelevel.log4cats.LoggerFactory

class JobPosterServiceSpec extends AsyncWordSpec with Matchers {
  "delete" should {
    "delete the job using the provided access token and job ID" in {
      val mockJobManager         = mock[JobManager[IO]]
      val mockRecruiter          = mock[Recruiter]
      val mockRecruiterService   = mock[RecruiterService[IO]]
      val mockDefaultsRepository = mock[DefaultsRepository[IO]]
      val mockLoggerFactory      = mock[LoggerFactory[IO]]

      val service = JobPosterService[IO](mockJobManager, mockRecruiterService, mockDefaultsRepository, mockLoggerFactory)

      val accessToken  = AccessToken.JPA
      val jobId        = "test-job-id"
      val salesForceId = SalesForceId.unsafeApply("A Salesforce id")

      when(mockRecruiterService.getRecruiter(accessToken)).thenReturn(IO.pure(mockRecruiter))
      when(mockRecruiterService.getRecruiter(salesForceId)).thenReturn(IO.pure(mockRecruiter))
      when(mockRecruiter.salesforceId).thenReturn(salesForceId)
      when(mockJobManager.deleteJob(accessToken, JobId(jobId), salesForceId)).thenReturn(IO.unit)

      val result: Unit = service.delete(salesForceId, jobId).unsafeRunSync()
      verify(mockJobManager).deleteJob(accessToken, JobId(jobId), salesForceId)
      result shouldEqual (())
    }
  }

  "suspend" should {
    "suspend the job using the provided access token, job ID and job status" in {
      val mockJobManager         = mock[JobManager[IO]]
      val mockRecruiter          = mock[Recruiter]
      val mockRecruiterService   = mock[RecruiterService[IO]]
      val mockDefaultsRepository = mock[DefaultsRepository[IO]]
      val mockLoggerFactory      = mock[LoggerFactory[IO]]

      val service = JobPosterService[IO](mockJobManager, mockRecruiterService, mockDefaultsRepository, mockLoggerFactory)

      val accessToken  = AccessToken.JPA
      val jobId        = "test-job-id"
      val salesForceId = SalesForceId.unsafeApply("A Salesforce id")

      when(mockRecruiterService.getRecruiter(accessToken)).thenReturn(IO.pure(mockRecruiter))
      when(mockRecruiterService.getRecruiter(salesForceId)).thenReturn(IO.pure(mockRecruiter))
      when(mockRecruiter.salesforceId).thenReturn(salesForceId)
      when(mockJobManager.updateJobStatus(accessToken, JobId(jobId), salesForceId, Suspend)).thenReturn(IO.unit)

      val result: Unit = service.updateJobStatus(salesForceId, jobId, Suspend).unsafeRunSync()
      verify(mockJobManager).updateJobStatus(accessToken, JobId(jobId), salesForceId, Suspend)
      result shouldEqual (())
    }
  }

  "resume" should {
    "resume the job using the provided access token, job ID and job status" in {
      val mockJobManager         = mock[JobManager[IO]]
      val mockRecruiter          = mock[Recruiter]
      val mockRecruiterService   = mock[RecruiterService[IO]]
      val mockDefaultsRepository = mock[DefaultsRepository[IO]]
      val mockLoggerFactory      = mock[LoggerFactory[IO]]

      val service = JobPosterService[IO](mockJobManager, mockRecruiterService, mockDefaultsRepository, mockLoggerFactory)

      val accessToken  = AccessToken.JPA
      val jobId        = "test-job-id"
      val salesForceId = SalesForceId.unsafeApply("A Salesforce id")

      when(mockRecruiterService.getRecruiter(accessToken)).thenReturn(IO.pure(mockRecruiter))
      when(mockRecruiterService.getRecruiter(salesForceId)).thenReturn(IO.pure(mockRecruiter))
      when(mockRecruiter.salesforceId).thenReturn(salesForceId)
      when(mockJobManager.updateJobStatus(accessToken, JobId(jobId), salesForceId, Resume)).thenReturn(IO.unit)

      val result: Unit = service.updateJobStatus(salesForceId, jobId, Resume).unsafeRunSync()
      verify(mockJobManager).updateJobStatus(accessToken, JobId(jobId), salesForceId, Resume)
      result shouldEqual (())
    }
  }
}
