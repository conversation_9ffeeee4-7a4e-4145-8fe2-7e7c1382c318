package nl.dpes.job.poster.api.service.shared

import cats.data.Validated.Invalid
import weaver._
import io.circe._
import io.circe.syntax._

object CareerLevelSpec extends FunSuite {

  val level = "Starter"

  test("Encode career level") {
    expect(CareerLevel.SwaggerDoc.example.asJson.noSpaces == s""""$level"""")
  }

  test("Decode career level") {
    expect(Json.fromString(level).as[CareerLevel] == Right(CareerLevel.SwaggerDoc.example))
  }

  test("Unable to decode an invalid career level") {
    expect(
      Json.fromString("level").as[CareerLevel] == Left(
        DecodingFailure(s"Career level 'level' is not valid.", List())
      )
    )
  }

  test("Creating a career level") {
    expect(CareerLevel("Starter").isValid)
  }

  test("It should return an error when career level is wrong") {
    val invalidLevel = "invalid level"
    expect(CareerLevel(invalidLevel) == Invalid(s"Career level '$invalidLevel' is not valid."))
  }
}
