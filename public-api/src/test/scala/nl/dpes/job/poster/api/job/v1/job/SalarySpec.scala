package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.Cursor
import nl.dpes.job.poster.api.service.shared.{Salary => AppSalary}
import weaver._

object SalarySpec extends FunSuite {

  val salary = 10000.0

  test("Encode salary") {
    expect(Salary.SwaggerDoc.minSalaryExample.asJson.noSpaces == s"""$salary""")
  }

  test("Decode salary") {
    expect(Json.fromDouble(salary).map(_.as[Salary]).contains(Right(Salary.SwaggerDoc.minSalaryExample)))
  }

  test("Unable to decode an invalid salary") {
    Json.fromFloat(-250f) match {
      case Some(value) => expect(value.as[Salary] == Left(DecodingFailure("Salary '-250.0' cannot be less than 1.", List())))
      case None        => failure("Failed to create a salary")
    }
  }

  test("Creating an salary") {
    expect(Salary(salary.toInt) == Valid(Salary.SwaggerDoc.minSalaryExample))
  }

  test("It should return an error when salary is invalid") {
    val invalidSalary = -1
    expect(Salary(invalidSalary) == Invalid(f"Salary '-1.0' cannot be less than 1."))
  }

  test("The api model can be converted to the domain model") {
    implicit val cursor: Cursor.Root = Cursor.Root("")
    expect(Salary.map(Salary.SwaggerDoc.minSalaryExample) == AppSalary(Salary.SwaggerDoc.minSalaryExample.salary))
  }
}
