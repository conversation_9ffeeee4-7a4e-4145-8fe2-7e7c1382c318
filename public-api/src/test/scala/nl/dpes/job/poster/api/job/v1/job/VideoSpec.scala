package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated.{Invalid, Valid}
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import weaver._
import nl.dpes.job.poster.api.service.shared.{Video => AppVideo}

object VideoSpec extends FunSuite {

  implicit val cursor: Cursor = Cursor("body")

  val url               = "https://www.youtube.com/watch?time_continue=4&v=jobvideo"
  val invalidYoutubeUrl = "abc.youtube.com/watch?time_continue=4&v=jobvideo"

  test("Encode video url") {
    expect(Video.SwaggerDoc.example.asJson.noSpaces == s""""$url"""")
  }

  test("Decode video url") {
    expect(Json.fromString(url).as[Video] == Right(Video.SwaggerDoc.example))
  }

  test("Unable to decode an invalid video") {
    expect(Json.fromString("something").as[Video] == Left(DecodingFailure("Cannot parse url 'something'.", List())))
  }

  test("Unable to decode an invalid youtube video") {
    expect(
      Json.fromString(invalidYoutubeUrl).as[Video] == Left(
        DecodingFailure(s"java.net.MalformedURLException: no protocol: $invalidYoutubeUrl", List())
      )
    )
  }

  test("Creating a video url") {
    expect(Video(url) == Valid(Video.SwaggerDoc.example))
  }

  test("It should return an error when video url is invalid") {
    val invalidUrl = "invalid url"
    expect(Video(invalidUrl) == Invalid(s"Cannot parse url '$invalidUrl'."))
  }

  test("It should return an error when a youtube video url is invalid") {
    expect(Video(invalidYoutubeUrl) == Invalid(s"java.net.MalformedURLException: no protocol: $invalidYoutubeUrl"))
  }

  test("Mapping Video from API to Application model") {
    val actualAppVideo   = Video.map(Video.SwaggerDoc.example.some)
    val expectedAppVideo = AppVideo(Video.SwaggerDoc.example.url).some.sequence.toValidatedNec

    expect(actualAppVideo == expectedAppVideo)
  }

  test("Returning error when unable to map Video from API to Application model") {
    val invalidUrl     = "Something invalid"
    val actualAppVideo = Video.map(Video.SwaggerDoc.example.copy(url = invalidUrl).some)

    val error = s"Cannot parse url '$invalidUrl'."
    expect(
      actualAppVideo == MappingError(cursor -> error).invalid
    )
  }
}
