syntax = "proto3";

option java_package = "nl.dpes.fileservice.proto.joblogo";

import "chunk.proto";
import "google/protobuf/empty.proto";

package fileservice;

service JobLogoService {
    rpc PersistJobLogo (stream Chunk) returns (PersistJobLogoRes) {}
    rpc DeleteJobLogo (DeleteJobLogoReq) returns (google.protobuf.Empty) {}
}

message DeleteJobLogoReq {
    string key = 1;
}

message PersistJobLogoRes {
    string key = 1;
}

