package nl.dpes.job.poster.api.jobapplication.v1.jobapplication

import sttp.tapir.CodecFormat.TextPlain
import sttp.tapir.{Codec, DecodeResult}

case class RawDate private (value: String) extends AnyVal

object RawDate {

  implicit val dateCodec: Codec[String, RawDate, TextPlain] = Codec.string.mapDecode(decode)(encode)

  def decode(date: String): DecodeResult[RawDate] = DecodeResult.Value(new RawDate(date))

  def encode(date: RawDate): String = date.value

  object SwaggerDoc {
    val infRawDateTime = new RawDate("2023-01-03T10:30:15Z")
    val supRawDateTime = new RawDate("2023-12-03T10:30:15Z")
  }
}
