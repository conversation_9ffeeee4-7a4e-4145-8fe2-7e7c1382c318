package nl.dpes.job.poster.api.shutdownguard

import cats.effect.std.{Di<PERSON>atch<PERSON>, Semaphore}
import cats.effect.{Async, Deferred, Resource}
import cats.implicits._
import nl.dpes.job.poster.api.shutdownguard.ShutdownHandler.syntax._
import org.typelevel.log4cats.{Logger, LoggerFactory, LoggerName, SelfAwareStructuredLogger}

import scala.concurrent.duration.DurationInt

trait ShutdownGuard[F[_]] {
  def awaitCompletion: F[Unit]
  def run[A](f: F[A]): F[A]
}

object ShutdownGuard {
  private val maxRequests = 1000000

  def apply[F[_]: Async: LoggerFactory, R](runtime: R)(implicit handler: ShutdownHandler[F, R]): Resource[F, ShutdownGuard[F]] = for {
    dispatcher <- Dispatcher.sequential[F]
    guard      <- ShutdownGuard.resource(runtime, dispatcher)
  } yield guard

  def resource[F[_]: Async: LoggerFactory, R](runtime: R, dispatcher: Dispatcher[F])(implicit
    handler: ShutdownHandler[F, R]
  ): Resource[F, ShutdownGuard[F]] =
    Resource.eval {
      def registerShutDownHook(shutdownSignal: Deferred[F, Unit]): F[Unit] =
        runtime.registerShutdownHook(dispatcher.unsafeRunAndForget(shutdownSignal.complete(())))

      val logger: Logger[F] = LoggerFactory[F].getLogger(LoggerName("Shutdown process"))

      def waitForPendingRequests(semaphore: Semaphore[F]): F[Unit] = for {
        _ <- logger.info("Shutdown signal received, waiting for pending requests to complete")
        _ <- semaphore.acquireN(maxRequests)
        _ <- Async[F].sleep(1.second)
        _ <- logger.info("All pending requests completed")
      } yield ()

      for {
        shutdownSignal <- Deferred[F, Unit]
        _              <- registerShutDownHook(shutdownSignal)
        semaphore      <- Semaphore[F](maxRequests)
      } yield new ShutdownGuard[F] {
        def awaitCompletion: F[Unit] =
          Async[F].uncancelable(_ => shutdownSignal.get >> waitForPendingRequests(semaphore))
        def run[A](f: F[A]): F[A] = Async[F].uncancelable(_ => semaphore.permit.use(_ => f))
      }
    }
}
