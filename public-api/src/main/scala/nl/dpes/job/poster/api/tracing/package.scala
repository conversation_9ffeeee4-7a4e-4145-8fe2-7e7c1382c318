package nl.dpes.job.poster.api

import io.circe.Json
import nl.dpes.b2b.salesforce.domain.SalesForceId
import org.typelevel.log4cats.Logger

package object tracing {
  type RecruiterId = SalesForceId

  implicit class JsonLogger[F[_]](val logger: Logger[F]) extends AnyVal {
    private def disableJsonParsing(json: Json): String = s"ENCODED JSON ${json.noSpaces}"

    def info(json: Json): F[Unit]  = logger.info(disableJsonParsing(json))
    def warn(json: Json): F[Unit]  = logger.warn(disableJsonParsing(json))
    def error(json: Json): F[Unit] = logger.error(disableJsonParsing(json))
  }
}
