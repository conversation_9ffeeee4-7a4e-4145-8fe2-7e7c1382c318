package nl.dpes.job.poster.api.service

import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.prediction.OccupationClassification

package object prediction {
  final case class JobTitle(value: String) extends AnyVal
  implicit lazy val jobTitleEncoder: Encoder[JobTitle] = Encoder.encodeString.contramap(_.value)

  final case class JobDescription(value: String) extends AnyVal
  implicit lazy val jobDescriptionEncoder: Encoder[JobDescription] = Encoder.encodeString.contramap(_.value)

  final case class IndustryCategory(value: String) extends AnyVal
  implicit lazy val industryCategoryDecoder: Decoder[IndustryCategory] = Decoder.decodeString.map(IndustryCategory.apply)

  final case class JobCategory(value: String) extends AnyVal
  implicit lazy val jobCategoryDecoder: Decoder[JobCategory] = Decoder.decodeString.map(JobCategory.apply)

  final case class EducationLevel(value: String) extends AnyVal
  implicit lazy val educationLevelDecoder: Decoder[EducationLevel] = Decoder.decodeString.map(EducationLevel.apply)

  final case class CareerLevel(value: String) extends AnyVal
  implicit lazy val careerLevelDecoder: Decoder[CareerLevel] = Decoder.decodeString.map(CareerLevel.apply)

  final case class ContractType(value: String) extends AnyVal
  implicit lazy val contractTypeDecoder: Decoder[ContractType] = Decoder.decodeString.map(ContractType.apply)

  final case class Prediction(
    ISCO: Option[OccupationClassification],
    jobCategories: List[JobCategory],
    educationLevel: EducationLevel,
    careerLevel: CareerLevel,
    contractType: ContractType
  )
}
