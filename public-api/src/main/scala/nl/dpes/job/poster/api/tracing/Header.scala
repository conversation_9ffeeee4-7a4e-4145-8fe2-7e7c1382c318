package nl.dpes.job.poster.api.tracing

import cats.syntax.applicative._
import cats.syntax.applicativeError._
import cats.syntax.functor._
import cats.{ApplicativeThrow, MonadThrow}
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.tracing.Header.AuthorizationHeader.NoBearerTokenFound
import sttp.tapir.model.ServerRequest
import sttp.tapir.server.interceptor.DecodeFailureContext

object Header {

  trait Extractor[T] {
    def extract[F[_]: MonadThrow](request: ServerRequest): F[T]
  }

  implicit class ServerRequestOps[F[_]: MonadThrow](request: ServerRequest) {

    def getHeaderValue(name: String): F[String] = ApplicativeThrow[F]
      .fromOption(request.header(name), HeaderNotFound(name))
    def extractHeader[T: Extractor]: F[T] = implicitly[Extractor[T]].extract(request)
  }

  final case class HeaderNotFound(header: String) extends Throwable(s"Header '$header' not found in request")

  final case class AuthorizationHeader(value: String) {

    def extractBearerToken[F[_]: MonadThrow]: F[BearerToken] =
      if (value.startsWith("Bearer ")) BearerToken.apply(value.substring(7)).pure
      else NoBearerTokenFound(value).raiseError
  }

  object AuthorizationHeader {
    val name: String = "Authorization"

    // todo how to log, the header can contain sensitive data
    case class NoBearerTokenFound(header: String) extends Throwable("No bearer token found in authorization header")

    implicit def extractor: Extractor[AuthorizationHeader] =
      new Extractor[AuthorizationHeader] {

        override def extract[F[_]: MonadThrow](request: ServerRequest): F[AuthorizationHeader] =
          ApplicativeThrow[F]
            .fromOption(request.header(name), HeaderNotFound(name))
            .map(AuthorizationHeader.apply)
      }

    def fromContext[F[_]: MonadThrow](ctx: DecodeFailureContext): F[AuthorizationHeader] =
      fromRequest(ctx.request)

    def fromRequest[F[_]: MonadThrow](request: ServerRequest): F[AuthorizationHeader] =
      MonadThrow[F].fromOption(request.header(name), HeaderNotFound(name)).map(h => new AuthorizationHeader(h))
  }

  final case class RequestIdHeader(value: RequestId)

  object RequestIdHeader {
    val name = "X-Request-Id"

    implicit def extractor: Extractor[RequestIdHeader] = new Extractor[RequestIdHeader] {

      override def extract[F[_]: MonadThrow](request: ServerRequest): F[RequestIdHeader] =
        MonadThrow[F].fromOption(request.header(name), HeaderNotFound(name)).map(value => RequestIdHeader(RequestId(value)))
    }
  }
}
