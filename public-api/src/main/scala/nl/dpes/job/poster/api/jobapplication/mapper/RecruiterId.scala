package nl.dpes.job.poster.api.jobapplication.mapper

import cats.implicits.catsSyntaxEitherId
import io.circe.{Decoder, Encoder}

case class RecruiterId(recruiterId: String) extends AnyVal

object RecruiterId {
  implicit lazy val encoder: Encoder[RecruiterId] = Encoder.encodeString.contramap(_.recruiterId)
  implicit lazy val decoder: Decoder[RecruiterId] = Decoder.decodeString.emap(RecruiterId(_).asRight)
}
