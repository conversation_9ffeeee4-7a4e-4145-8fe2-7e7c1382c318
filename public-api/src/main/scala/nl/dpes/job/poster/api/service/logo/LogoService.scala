package nl.dpes.job.poster.api.service.logo

import cats.MonadThrow
import cats.effect.{Async, Resource}
import cats.syntax.applicative._
import cats.syntax.applicativeError._
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.monadError._
import fs2.grpc.syntax.all._
import io.grpc._
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder
import fs2._
import nl.dpes.fileservice.proto.joblogo.job_logo.{DeleteJobLogoReq, JobLogoServiceFs2Grpc}
import nl.dpes.job.poster.api.service.shared.Logo
import nl.dpes.job.poster.api.tracing.RequestTimer
import nl.dpes.job.poster.api.tracing.RequestTimer.ClientName.logoService
import org.typelevel.log4cats.LoggerFactory
import sttp.capabilities
import sttp.capabilities.fs2.Fs2Streams
import sttp.client3._
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend

trait LogoService[F[_]] {
  def store(logo: Logo, retries: Int = 0): F[LogoKey]
  def remove(key: LogoKey): F[Unit]
}

object LogoService {

  sealed abstract class LogoServiceError(message: String) extends Throwable(message)

  case class RetrieveLogoError(logo: Logo) extends LogoServiceError(s"Could not retrieve logo from '${logo.url}'")

  case class SendLogoError(logo: Logo, error: Throwable)
      extends LogoServiceError(s"Could not store logo from '${logo.url}': ${error.getMessage}")

  def client[F[_]: Async](host: String, port: Int): Resource[F, JobLogoServiceFs2Grpc[F, Metadata]] =
    NettyChannelBuilder
      .forAddress(host, port)
      .usePlaintext()
      .resource[F]
      .flatMap(ch => JobLogoServiceFs2Grpc.stubResource[F](ch))

  def impl[F[_]: Async: LoggerFactory](
    client: JobLogoServiceFs2Grpc[F, Metadata],
    downloadBackend: DownloadBackend[F]
  ): LogoService[F] =
    new LogoService[F] {

      override def store(logo: Logo, retries: Int = 0): F[LogoKey] =
        downloadBackend
          .use { backend =>
            for {
              getResponse <- RequestTimer(logoService).log(
                basicRequest
                  .get(uri"${logo.url}")
                  .response(asStreamUnsafe(Fs2Streams[F]))
                  .send(backend)
                  .adaptErr { case _ => RetrieveLogoError(logo) }
              )
              _      <- if (getResponse.isClientError) RetrieveLogoError(logo).raiseError else Async[F].unit
              bytes  <- Async[F].fromEither(getResponse.body.left.map(e => new Throwable(e)))
              chunks <- bytes.chunks.pure
              postResponse <- client.persistJobLogo(LogoChunk.mapChunks(chunks), new Metadata()).adaptErr {
                case e if e.getMessage.contains("INVALID_ARGUMENT") => SendLogoError(logo, e)
              }
            } yield LogoKey(postResponse.key)
          }
          .redeemWith(
            {
              case error: LogoServiceError => error.raiseError
              case error =>
                if (retries > 0) store(logo, retries - 1)
                else error.raiseError
            },
            _.pure
          )

      override def remove(key: LogoKey): F[Unit] =
        client.deleteJobLogo(DeleteJobLogoReq.defaultInstance.withKey(key.key), new Metadata()).void
    }

  private def validateContentSize[F[_]: Async: MonadThrow](bytes: Stream[F, Byte], threshold: Int = 2000000) =
    bytes.chunks
      .scan((0, Chunk.empty[Byte]))((scan, byte) =>
        scan match {
          case (n, _) =>
            val newSize = n + byte.size
            if (newSize > threshold) throw new Throwable("Filesize exceeded")
            else (newSize, byte)
        }
      )
      .map(_._2)
      .pure

  def resource[F[_]: Async: MonadThrow: LoggerFactory](
    client: Resource[F, JobLogoServiceFs2Grpc[F, Metadata]]
  ): Resource[F, LogoService[F]] =
    client.map(c => impl(c, downloadBackend))

  type DownloadBackend[F[_]] = Resource[F, SttpBackend[F, Fs2Streams[F] with capabilities.WebSockets]]
  def downloadBackend[F[_]: Async]: DownloadBackend[F] = AsyncHttpClientFs2Backend.resource[F]()
}
