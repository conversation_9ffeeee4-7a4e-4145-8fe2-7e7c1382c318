package nl.dpes.job.poster.api
package job.v1.job

import cats.implicits.catsSyntaxEitherId
import io.circe.{Decoder, Encoder}

case class Longitude(value: Double)

object Longitude {
  implicit lazy val longitudeEncoder: Encoder[Longitude] = Encoder.encodeDouble.contramap[Longitude](_.value)
  implicit lazy val longitudeDecoder: Decoder[Longitude] = Decoder.decodeDouble.emap(Longitude(_).asRight[String])
}
