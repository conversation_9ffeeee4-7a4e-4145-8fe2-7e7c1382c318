package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import io.circe.{Decoder, Encoder}

case class Logo(url: String) extends AnyVal

object Logo {

  implicit lazy val encoder: Encoder[Logo] = Encoder.encodeString.contramap(_.url)
  implicit lazy val decoder: Decoder[Logo] = Decoder.decodeString.emap(Logo(_).toEither)

  def apply(url: String): Validated[String, Logo] =
    Website(url).map(website => new Logo(website.url))

  object SwaggerDoc {
    val example = new Logo("https://picsum.photos/200/300")
  }
}
