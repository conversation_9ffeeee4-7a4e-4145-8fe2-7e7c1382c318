package nl.dpes.job.poster.api.service.jobmanager

import cats.Parallel
import cats.effect.{Async, Resource}
import nl.dpes.b2b.jobmanager.service.GrpcJobService
import nl.dpes.job.poster.api.service.logo.LogoService
import nl.dpes.job.poster.api.service.occupationclassification.OccupationClassifier
import nl.dpes.job.poster.api.service.prediction.VacancyEnricher
import nl.dpes.job.poster.api.tracing.CorrelationId
import org.typelevel.log4cats.LoggerFactory

trait JobManagerFactory[F[_]] {
  def createJobManager(correlationId: CorrelationId, loggerFactory: LoggerFactory[F]): JobManager[F]
}

object JobManagerFactory {

  def impl[F[_]: Async: Parallel: LoggerFactory](
    connection: Resource[F, GrpcJobService],
    logoServiceResource: Resource[F, LogoService[F]],
    occupationClassifier: OccupationClassifier[F],
    vacancyEnricherService: VacancyEnricher[F]
  ): JobManagerFactory[F] = new JobManagerFactory[F] {

    override def createJobManager(correlationId: CorrelationId, loggerFactory: LoggerFactory[F]): JobManager[F] =
      JobManager(connection, logoServiceResource, occupationClassifier, vacancyEnricherService)
  }

}
