package nl.dpes.job.poster.api.tracing

import cats.MonadThrow
import cats.syntax.flatMap._
import cats.syntax.functor._
import io.circe.Encoder
import io.circe.generic.semiauto.deriveEncoder
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import sttp.tapir.server.interceptor.DecodeFailureContext

final case class DecodeFailureLog(
  method: Method,
  path: Path,
  requestId: RequestId,
  recruiterId: RecruiterId,
  payload: String,
  errors: Map[String, String]
)

object DecodeFailureLog {
  implicit lazy val recruiterIdEncoder: Encoder[RecruiterId]        = Encoder.encodeString.contramap(_.idWithChecksum)
  implicit lazy val decodeFailureEncoder: Encoder[DecodeFailureLog] = deriveEncoder[DecodeFailureLog]

  import Header._

  // $COVERAGE-OFF$
  def fromContext[F[_]: MonadThrow](ctx: DecodeFailureContext, authenticationService: AuthenticationService[F]): F[DecodeFailureLog] =
    for {
      authHeader         <- ctx.request.extractHeader[AuthorizationHeader]
      bearerToken        <- authHeader.extractBearerToken
      credentialsOrError <- authenticationService.authenticate(bearerToken)
      recruiterId        <- MonadThrow[F].fromEither(credentialsOrError)
      requestId          <- ctx.request.extractHeader[RequestIdHeader]
      failure            <- DecodeFailure.fromDecodeResultFailure(ctx.failure)
    } yield new DecodeFailureLog(
      Method(ctx.request.method.method),
      Path(ctx.request.pathSegments),
      requestId.value,
      recruiterId,
      failure.original,
      failure.errors
    )
  // $COVERAGE-ON$
}
