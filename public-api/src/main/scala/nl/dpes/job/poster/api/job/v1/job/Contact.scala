package nl.dpes.job.poster.api
package job.v1.job

import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.shared.{Contact => ServiceContact}

case class Contact(name: Name, phoneNumber: Option[String] = None, emailAddress: String)

object Contact {

  def mapToService(contact: Contact): ServiceContact = ServiceContact(
    Name.mapToService(contact.name),
    contact.phoneNumber,
    contact.emailAddress
  )

  implicit lazy val encoder: Encoder[Contact] = deriveEncoder
  implicit lazy val decoder: Decoder[Contact] = deriveDecoder
}
