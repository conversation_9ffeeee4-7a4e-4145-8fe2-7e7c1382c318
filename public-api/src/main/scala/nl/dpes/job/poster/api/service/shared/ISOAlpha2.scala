package nl.dpes.job.poster.api.service.shared

import cats.syntax.either._
import io.circe.{Decoder, Encoder}
import sttp.tapir.Schema.annotations.description

@description("Represents the two-letter country code defined in ISO 3166-1 standard.")
case class ISOAlpha2(code: String) extends AnyVal

object ISOAlpha2 {
  implicit lazy val encoder: Encoder[ISOAlpha2] = Encoder.encodeString.contramap[ISOAlpha2](_.code)
  implicit lazy val decoder: Decoder[ISOAlpha2] = Decoder.decodeString.emap(ISOAlpha2(_).asRight[String])
}
