package nl.dpes.job.poster.api.jobapplication.v1.jobapplication

import cats.data.Validated

import scala.util.Failure

case class StartDate private (value: String) extends AnyVal

object StartDate {

  def apply(date: RawDate): Validated[String, StartDate] = Validated
    .fromTry(date.value match {
      case dateTimeFormat(year, month, day, hours, minutes, seconds) =>
        constructIsoInstant(year, month, day, hours, minutes, seconds)
          .map(new StartDate(_))
      case unsupportedDateFormat => Failure(UnsupportedDateFormat(unsupportedDateFormat))
    })
    .leftMap(_.getMessage)

  object SwaggerDoc {
    val infStartDateTime = new StartDate("2023-01-03T10:30:15Z")
    val supStartDateTime = new StartDate("2023-12-03T10:30:15Z")
  }
}
