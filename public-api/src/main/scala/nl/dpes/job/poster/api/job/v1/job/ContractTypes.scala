package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits.{catsSyntaxValidatedId, toTraverseOps}
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{ContractTypes => AppContractTypes, ContractType => AppContractType}

case class ContractTypes(types: Set[ContractType])

object ContractTypes {

  implicit lazy val encoder: Encoder[ContractTypes] = Encoder.encodeSet[ContractType].contramap(_.types)
  implicit lazy val decoder: Decoder[ContractTypes] = Decoder.decodeSet[ContractType].emap(ContractTypes(_).toEither)

  private val minContractTypes: Int = 1
  private val maxContractTypes: Int = 5

  def checkMinimumAmount(contractTypes: ContractTypes): Validated[String, ContractTypes] =
    Validated.cond(
      contractTypes.types.size >= minContractTypes,
      contractTypes,
      s"At least $minContractTypes type(s) should be chosen"
    )

  def checkMaximumAmount(contractTypes: ContractTypes): Validated[String, ContractTypes] =
    Validated.cond(
      contractTypes.types.size <= maxContractTypes,
      contractTypes,
      s"At most $maxContractTypes types should be chosen"
    )

  def apply(types: Set[ContractType]): Validated[String, ContractTypes] =
    new ContractTypes(types).valid andThen checkMinimumAmount andThen checkMaximumAmount

  def map(
    contractTypes: Option[ContractTypes]
  )(implicit cursor: Cursor): Validated[MappingError, Option[AppContractTypes]] =
    (for {
      types            <- contractTypes.map(types => types.types).map(_.toList)
      appContractTypes <- types.traverse(contractType => AppContractType(contractType.contractType).toOption).map(_.toSet)
    } yield AppContractTypes(appContractTypes)).sequence
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val example = new ContractTypes(Set(ContractType.SwaggerDoc.interim, ContractType.SwaggerDoc.stage, ContractType.SwaggerDoc.tijdelijk))
  }
}
