package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

case class IndustryCategories private (industryCategories: Set[IndustryCategory]) extends AnyVal

object IndustryCategories {

  implicit lazy val encoder: Encoder[IndustryCategories] = Encoder.encodeSet[IndustryCategory].contramap(_.industryCategories)
  implicit lazy val decoder: Decoder[IndustryCategories] = Decoder.decodeSet[IndustryCategory].emap(IndustryCategories(_).toEither)

  val maximumAmountOfCategories = 2

  def checkMaximumAmount(categories: IndustryCategories): Validated[String, IndustryCategories] =
    Validated.cond(
      categories.industryCategories.size <= maximumAmountOfCategories,
      categories,
      s"At most $maximumAmountOfCategories categories should be chosen"
    )

  def apply(items: Set[IndustryCategory]): Validated[String, IndustryCategories] =
    new IndustryCategories(items).valid andThen checkMaximumAmount

  def empty = new IndustryCategories(Set.empty)

  object SwaggerDoc {
    val example = new IndustryCategories(Set(IndustryCategory.SwaggerDoc.telecom, IndustryCategory.SwaggerDoc.techniek))
  }
}
