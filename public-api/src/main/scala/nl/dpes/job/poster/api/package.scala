package nl.dpes.job.poster

import cats.effect.Sync
import io.circe.Encoder
import io.circe.generic.auto._
import io.circe.generic.semiauto.deriveEncoder
import nl.dpes.job.poster.api.service.shared.JobStatus
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe._
import sttp.tapir.server.http4s.Http4sServerOptions
import sttp.tapir.server.model.ValuedEndpointOutput

package object api {

  sealed abstract class ErrorMessage(message: String)              extends Throwable(message) with Product with Serializable
  case class Unauthorized(message: String)                         extends ErrorMessage(s"Unauthorized: $message")
  case class Forbidden(message: String)                            extends ErrorMessage(s"Forbidden: $message")
  case class Conflict(message: String)                             extends ErrorMessage(s"Conflict: $message")
  case class StatusConflict(message: String, jobStatus: JobStatus) extends ErrorMessage(s"StatusConflict: $message")
  case class BadRequest(message: String)                           extends ErrorMessage(s"BadRequest: $message")
  case class Unknown(message: String)                              extends ErrorMessage(s"Unknown: $message")
  case class NotFound(message: String)                             extends ErrorMessage(s"NotFound: $message")
  case class Timeout(message: String)                              extends ErrorMessage(s"Timeout: $message")
  case class ServiceUnavailable(message: String)                   extends ErrorMessage(s"ServiceUnavailable: $message")
  case class Teapot(message: String)                               extends ErrorMessage(s"Teapot: $message")

  object ErrorMessage {
    implicit lazy val jsonEncoder: Encoder[ErrorMessage] = deriveEncoder
  }

  def wrapErrorResponse(m: String): ValuedEndpointOutput[_] =
    ValuedEndpointOutput(jsonBody[BadRequest], BadRequest(m))

  def serverOptions[F[_]: Sync]: Http4sServerOptions[F] =
    Http4sServerOptions.customiseInterceptors.defaultHandlers(wrapErrorResponse).options
}
