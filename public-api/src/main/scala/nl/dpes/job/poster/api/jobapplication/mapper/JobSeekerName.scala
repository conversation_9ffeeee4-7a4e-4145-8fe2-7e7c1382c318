package nl.dpes.job.poster.api.jobapplication.mapper

import cats.implicits.catsSyntaxEitherId
import io.circe.generic.semiauto.deriveEncoder
import io.circe.{Decoder, Encoder, HCursor}

case class JobSeekerName(firstName: Name, lastName: Name)

object JobSeekerName {
  implicit lazy val encoder: Encoder[JobSeekerName] = deriveEncoder

  implicit lazy val decoder: Decoder[JobSeekerName] = (c: HCursor) =>
    for {
      firstName     <- c.downField("firstName").as[Name]
      lastName      <- c.downField("lastName").as[Name]
      jobSeekerName <- JobSeekerName(firstName, lastName).asRight
    } yield jobSeekerName
}
