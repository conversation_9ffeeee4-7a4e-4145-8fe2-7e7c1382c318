package nl.dpes.job.poster.api.job.v1.job

import cats.implicits._
import io.circe.Encoder
import sttp.tapir.{Codec, Schema}
import sttp.tapir.Codec.PlainCodec

case class JobId(value: String)

object JobId {
  implicit lazy val codec: PlainCodec[JobId]    = Codec.string.map(JobId(_))(_.value)
  implicit lazy val jsonEncoder: Encoder[JobId] = Encoder.encodeString.contramap(_.value)
  implicit lazy val schema: Schema[JobId]       = Schema.schemaForString.map(value => JobId(value).some)(jobId => jobId.value)
}
