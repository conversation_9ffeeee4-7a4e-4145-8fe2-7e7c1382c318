package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.syntax.apply._
import cats.syntax.bifunctor._
import cats.syntax.option._
import cats.syntax.traverse._
import cats.syntax.validated._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.shared.{SalaryRange => AppSalaryRange}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}

case class SalaryRange private (lower: Salary, upper: Salary, period: Option[SalaryPeriod])

object SalaryRange {

  def apply(lower: Salary, upper: Salary, period: Option[SalaryPeriod]): Validated[String, SalaryRange] =
    if (lower > upper) s"Lower salary '${lower.salary}' cannot be more than upper salary '${upper.salary}'".invalid
    else new SalaryRange(lower, upper, period).valid

  implicit lazy val encoder: Encoder[SalaryRange] = (range: SalaryRange) =>
    Json.obj(
      "lower"  -> range.lower.asJson,
      "upper"  -> range.upper.asJson,
      "period" -> range.period.asJson
    )

  implicit lazy val decoder: Decoder[SalaryRange] = (c: HCursor) =>
    for {
      lower  <- c.downField("lower").as[Salary]
      upper  <- c.downField("upper").as[Salary]
      period <- c.downField("period").as[Option[SalaryPeriod]]
      range  <- SalaryRange(lower, upper, period).toEither.leftMap(e => DecodingFailure(e, c.history))
    } yield range

  def map(salaryRange: Option[SalaryRange])(implicit cursor: Cursor): Validated[MappingError, Option[AppSalaryRange]] =
    salaryRange.traverse(map)

  def map(salaryRange: SalaryRange)(implicit cursor: Cursor): Validated[MappingError, AppSalaryRange] =
    (
      Salary.map(salaryRange.lower)(cursor("lower")),
      Salary.map(salaryRange.upper)(cursor("upper")),
      SalaryPeriod.map(salaryRange.period)(cursor("period"))
    ).tupled.andThen { case (l, u, p) => AppSalaryRange(l, u, p) }.leftMap(error => MappingError(cursor -> error.toString))

  object SwaggerDoc {
    val example = new SalaryRange(Salary.SwaggerDoc.minSalaryExample, Salary.SwaggerDoc.maxSalaryExample, SalaryPeriod.Month.some)
  }
}
