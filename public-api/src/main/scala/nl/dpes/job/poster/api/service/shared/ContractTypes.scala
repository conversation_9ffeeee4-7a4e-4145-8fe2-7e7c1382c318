package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

case class ContractTypes private (contractTypes: Set[ContractType]) extends AnyVal

object ContractTypes {

  implicit lazy val encoder: Encoder[ContractTypes] = Encoder.encodeSet[ContractType].contramap(_.contractTypes)
  implicit lazy val decoder: Decoder[ContractTypes] = Decoder.decodeSet[ContractType].emap(ContractTypes(_).toEither)

  val minContractTypes: Int = 1
  val maxContractTypes: Int = 5

  def checkMinimumAmount(contractTypes: ContractTypes): Validated[String, ContractTypes] =
    Validated.cond(
      contractTypes.contractTypes.size >= minContractTypes,
      contractTypes,
      s"At least $minContractTypes type(s) should be chosen"
    )

  def checkMaximumAmount(contractTypes: ContractTypes): Validated[String, ContractTypes] =
    Validated.cond(
      contractTypes.contractTypes.size <= maxContractTypes,
      contractTypes,
      s"At most $maxContractTypes types should be chosen"
    )

  def apply(items: Set[ContractType]): Validated[String, ContractTypes] =
    new ContractTypes(items).valid andThen checkMinimumAmount andThen checkMaximumAmount

  object SwaggerDoc {
    val example = new ContractTypes(Set(ContractType.SwaggerDoc.interim, ContractType.SwaggerDoc.stage, ContractType.SwaggerDoc.tijdelijk))
  }
}
