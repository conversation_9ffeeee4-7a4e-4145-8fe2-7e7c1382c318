package nl.dpes.job.poster.api.reference_id

import cats.effect._
import cats.implicits._
import doobie._
import doobie.implicits._
import nl.dpes.job.poster.api.database.TableName

trait ReferenceIdRepository[F[_]] {
  def store(recruiterId: RecruiterId, referenceId: ReferenceId, jobId: JobId): F[Unit]
  def getReferenceId(jobId: JobId): F[Option[ReferenceId]]
  def getLatestJobId(referenceId: ReferenceId, recruiterId: RecruiterId): F[Option[JobId]]
  def delete(recruiterId: RecruiterId, referenceId: ReferenceId, jobId: JobId): F[Unit]
}

object ReferenceIdRepository {

  case class DuplicateReferenceId(recruiterId: RecruiterId, referenceId: ReferenceId)
      extends Throwable(s"Duplicate referenceId for recruiterId: $recruiterId, referenceId: $referenceId")
  case class DuplicateJobId(jobId: JobId) extends Throwable(s"Duplicate jobId: $jobId")

  def apply[F[_]: Async](tableName: TableName, xa: Transactor[F]): Resource[F, ReferenceIdRepository[F]] = {
    val tableNameFragment = Fragment.const(s"`${tableName.value}`")

    def initialize: F[Unit] =
      sql"""
        CREATE TABLE IF NOT EXISTS ${tableName.frag} (
          recruiterId VARCHAR(18)  NOT NULL,
          referenceId VARCHAR(100) NOT NULL,
          jobId       VARCHAR(36)  NOT NULL UNIQUE,
          createdAt   TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (recruiterId, referenceId, jobId),
          INDEX (createdAt)
        );
      """.update.run.transact(xa).void

    def repository: ReferenceIdRepository[F] = new ReferenceIdRepository[F] {
      override def store(recruiterId: RecruiterId, referenceId: ReferenceId, jobId: JobId): F[Unit] =
        sql"""
        INSERT INTO ${tableName.frag} (recruiterId, referenceId, jobId)
        VALUES ($recruiterId, $referenceId, $jobId)
        """.update.run
          .transact(xa)
          .void
          .adaptErr {
            case e: java.sql.SQLIntegrityConstraintViolationException if e.getMessage.contains("PRIMARY") =>
              DuplicateReferenceId(recruiterId, referenceId)
            case e: java.sql.SQLIntegrityConstraintViolationException if e.getMessage.contains("jobId") =>
              DuplicateJobId(jobId)
          }

      override def getReferenceId(jobId: JobId): F[Option[ReferenceId]] =
        sql"""
             SELECT referenceId
             FROM ${tableName.frag}
             WHERE jobId = $jobId
           """
          .query[ReferenceId]
          .option
          .transact(xa)

      override def getLatestJobId(referenceId: ReferenceId, recruiterId: RecruiterId): F[Option[JobId]] =
        sql"""
             SELECT jobId
             FROM ${tableName.frag}
             WHERE recruiterId = $recruiterId AND referenceId = $referenceId
             ORDER BY createdAt DESC
             LIMIT 1
           """
          .query[JobId]
          .option
          .transact(xa)

      override def delete(recruiterId: RecruiterId, referenceId: ReferenceId, jobId: JobId): F[Unit] =
        sql"""
          DELETE FROM ${tableName.frag}
          WHERE recruiterId = $recruiterId AND referenceId = $referenceId AND jobId = $jobId
        """.update.run
          .transact(xa)
          .void
    }

    Resource.eval(initialize as repository)
  }
}
