package nl.dpes.job.poster.api.jobperformance.v1

import cats.effect.kernel.{Async, Resource}
import cats.implicits.toBifunctorOps
import cats.syntax.functor._
import cats.syntax.monadError._
import io.circe.generic.codec.DerivedAsObjectCodec._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import sttp.client3._
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend
import sttp.client3.circe._
import sttp.model.StatusCode

trait PerformanceServiceClient[F[_]] {
  def getJobsPerformance(recruiterId: SalesForceId): F[Map[String, Performance]]
}

object PerformanceServiceClient {

  def impl[F[_]: Async](host: String, performanceBackend: Resource[F, SttpBackend[F, Any]]): PerformanceServiceClient[F] =
    new PerformanceServiceClient[F] {

      override def getJobsPerformance(recruiterId: SalesForceId): F[Map[String, Performance]] = performanceBackend.use { backend =>
        basicRequest
          .get(uri"$host/api/v1/jobPerformance?recruiterId=${recruiterId.idWithChecksum}")
          .response(asJson[Map[String, Performance]])
          .send(backend)
          .map(
            _.body
              .leftMap {
                case ex @ HttpError(_, StatusCode.InternalServerError) => JobPerformanceError(ex.getMessage)
                case ex @ HttpError(_, StatusCode.Unauthorized)        => UnauthorizationError(ex.getMessage)
                case ex @ DeserializationException(_, _)               => JobPerformanceError(ex.getMessage)
              }
          )
          .rethrow
      }
    }

  def performanceBackend[F[_]: Async]: Resource[F, SttpBackend[F, Any]] =
    AsyncHttpClientFs2Backend.resource[F]()

  case class JobPerformanceError(message: String)
      extends Throwable(s"Error occurred when trying to get the jobs performance, due to: $message")

  case class UnauthorizationError(message: String) extends Throwable(s"Unauthorized token: $message")
}
