package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits.{catsSyntaxValidatedId, toTraverseOps}
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{Occupation => AppOccupation}

case class Occupation private (group: String) extends AnyVal

object Occupation {

  implicit lazy val encoder: Encoder[Occupation] = Encoder.encodeString.contramap[Occupation](_.group)
  implicit lazy val decoder: Decoder[Occupation] = Decoder.decodeString.emap(Occupation(_).toEither)

  def apply(name: String): Validated[String, Occupation] =
    if (name.trim.nonEmpty) new Occupation(name).valid
    else s"The 'occupation' field should not be empty.".invalid

  def map(occupation: Option[Occupation])(implicit cursor: Cursor): Validated[MappingError, Option[AppOccupation]] =
    occupation
      .traverse(occupation => AppOccupation(occupation.group))
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val example = new Occupation("Developer")
  }
}
