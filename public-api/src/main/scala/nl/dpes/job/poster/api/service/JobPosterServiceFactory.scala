package nl.dpes.job.poster.api.service

import cats.Parallel
import cats.effect.Sync
import nl.dpes.job.poster.api.defaults.DefaultsRepository
import nl.dpes.job.poster.api.service.jobmanager.JobManagerFactory
import nl.dpes.job.poster.api.service.recruiter.RecruiterServiceFactory
import nl.dpes.job.poster.api.tracing.CorrelationId
import org.typelevel.log4cats.LoggerFactory

trait JobPosterServiceFactory[F[_]] {
  def createService(correlationId: CorrelationId, loggerFactory: LoggerFactory[F]): JobPosterService[F]
}

object JobPosterServiceFactory {

  def impl[F[_]: Sync: Parallel](
    jobManagerFactory: JobManagerFactory[F],
    recruiterServiceFactory: RecruiterServiceFactory[F],
    defaultsRepository: DefaultsRepository[F]
  ): JobPosterServiceFactory[F] = new JobPosterServiceFactory[F] {

    override def createService(correlationId: CorrelationId, loggerFactory: LoggerFactory[F]): JobPosterService[F] =
      JobPosterService(
        jobManagerFactory.createJobManager(correlationId, loggerFactory),
        recruiterServiceFactory.create(loggerFactory),
        defaultsRepository,
        loggerFactory
      )
  }
}
