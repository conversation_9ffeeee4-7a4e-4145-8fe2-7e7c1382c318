package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import io.circe.{Decoder, Encoder}

import java.net.URL
import scala.util.Try

case class Website private (url: String) extends AnyVal

object Website {

  implicit val encoder: Encoder[Website] = Encoder.encodeString.contramap[Website](_.url)
  implicit val decoder: Decoder[Website] = Decoder.decodeString.emap(Website(_).toEither)

  def apply(url: String): Validated[String, Website] =
    Validated.fromTry(Try(new URL(url))).bimap(e => e.toString, url => new Website(url.toString))

  object SwaggerDoc {
    val example = new Website("https://www.nationalevacaturebank.nl/")
  }
}
