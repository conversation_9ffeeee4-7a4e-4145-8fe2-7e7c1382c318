package nl.dpes.job.poster.api.defaults

import cats.effect.Sync
import cats.syntax.flatMap._
import cats.syntax.functor._
import io.getquill._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.service.recruiter._ //needed for the implicit quill encoder/decoder

/** NOTE THAT CHANGES IN THE MODEL FOR THIS REPOSITORY SHOULD ALSO BE IMPLEMENETED IN THE SHARED DEFAULTS
  */
trait DefaultsRepository[F[_]] {
  def storeDefaults(recruiterId: SalesForceId, defaults: Defaults): F[Unit]
  def readDefaults(recruiterId: SalesForceId): F[Defaults]
}

object DefaultsRepository {
  case class DefaultsEntry(recruiterId: SalesForceId, version: String, defaults: Defaults)

  def impl[F[_]: Sync](implicit context: SqliteJdbcContext[SnakeCase.type]): DefaultsRepository[F] = new DefaultsRepository[F] {
    import context._

    override def storeDefaults(recruiterId: SalesForceId, defaults: Defaults): F[Unit] = for {
      original <- select(recruiterId)
//      _ <- println(
//        original
//          .as(s"Found data for recruiter '${recruiterId.idWithChecksum}'.")
//          .getOrElse(s"sFound no data for recruiter '${recruiterId.idWithChecksum}'.")
//      ).pure
      _ <- original.as(update(recruiterId, defaults)).getOrElse(insert(recruiterId, defaults))
    } yield ()

    private def select(recruiterId: SalesForceId): F[Option[Defaults]] = for {
//      _ <- println(s"Selecting data for recruiter: '${recruiterId.idWithChecksum}'.").pure
      result <- Sync[F].blocking(
        context
          .run(
            quote(
              query[DefaultsEntry].filter(_.recruiterId == lift(recruiterId))
            )
          )
          .headOption
          .map(_.defaults)
      )
    } yield result

    private def insert(recruiterId: SalesForceId, defaults: Defaults): F[Unit] = for {
//      _ <- println(s"Inserting data for recruiter: '${recruiterId.idWithChecksum}'.").pure
      _ <- Sync[F].blocking(
        context.run(
          quote(
            query[DefaultsEntry]
              .insertValue(DefaultsEntry(lift(recruiterId), lift(Defaults.version), lift(defaults)))
          )
        )
      )
    } yield ()

    private def update(recruiterId: SalesForceId, defaults: Defaults): F[Unit] = for {
//      _ <- println(s"Updating data for recruiter: '${recruiterId.idWithChecksum}'.").pure
      _ <- Sync[F].blocking(
        context.run(
          quote(
            query[DefaultsEntry]
              .filter(_.recruiterId == lift(recruiterId))
              .updateValue(lift(DefaultsEntry(recruiterId, Defaults.version, defaults)))
          )
        )
      )
    } yield ()

    override def readDefaults(recruiterId: SalesForceId): F[Defaults] = select(recruiterId).map(_.getOrElse(Defaults.Empty))
  }
}
