package nl.dpes.job.poster.api.job.v1.job

import sttp.tapir.{Codec, Schema}
import sttp.tapir.Codec.PlainCodec

sealed trait JobStatus

object JobStatus {

  private case class UnsupportedJobStatus(status: String) extends Throwable(s"'$status' is an unsupported job status")

  implicit def jobStatusCodec: PlainCodec[JobStatus] =
    Codec.derivedEnumeration[String, JobStatus](
      (status: String) =>
        status match {
          case "suspend" => Some(Suspend)
          case "resume"  => Some(Resume)
          case _         => throw UnsupportedJobStatus(status)
        },
      _.toString.toLowerCase
    )

  implicit lazy val jobStatusSchema: Schema[JobStatus] = Schema.derived

  case object Suspend extends JobStatus
  implicit lazy val suspendSchema: Schema[Suspend.type] = Schema.derived

  case object Resume extends JobStatus
  implicit lazy val resumeSchema: Schema[Resume.type] = Schema.derived

  object SwaggerDoc {
    val example: JobStatus   = Suspend
    val jobStatusDescription = "A JSON representation for JobStatus"
  }
}
