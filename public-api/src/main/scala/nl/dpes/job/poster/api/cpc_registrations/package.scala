package nl.dpes.job.poster.api

import cats.Parallel
import cats.effect.kernel.Async
import cats.implicits._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.config.Configuration
import nl.dpes.job.poster.api.reference_id.ReferenceIdService
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.apikey.api.ApiKey.authenticationDescription
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import nl.dpes.job.poster.api.service.jobmanager.JobManager
import org.http4s.HttpRoutes
import org.typelevel.log4cats.LoggerFactory
import sttp.tapir._
import sttp.tapir.server.{PartialServerEndpoint, ServerEndpoint}

package object cpc_registrations extends Configuration {

  val cpcRegistrationClient: CpcEndpoint = CpcEndpoint("v1")

  case class CpcEndpoint(version: String) {

    def baseEndpoint[F[_]: Async, E <: ErrorMessage](
      securityLogic: BearerToken => F[Either[E, SalesForceId]],
      expectedErrors: Endpoint[Unit, Unit, Unit, Unit, Any] => Endpoint[Unit, Unit, E, Unit, Any]
    ): PartialServerEndpoint[BearerToken, SalesForceId, Unit, E, Unit, Any, F] =
      expectedErrors(endpoint.in("api" / version))
        .securityIn(auth.bearer[String]().mapTo[BearerToken].description(authenticationDescription(Dashboard.url)))
        .serverSecurityLogic(securityLogic)
  }

  case class Initializer[F[_]](cpcRegistrationController: CpcRegistrationController[F]) {
    def endpoints: F[List[ServerEndpoint[Any, F]]] = cpcRegistrationController.endpoints

    def routes: F[HttpRoutes[F]] = cpcRegistrationController.routes
  }

  def initializer[F[_]: Async: Parallel: LoggerFactory](
    authenticationService: AuthenticationService[F],
    referenceIdService: ReferenceIdService[F]
  ): F[Initializer[F]] = for {
    cpcService <- CpcService.impl(JobManager.connection(JobManagerConnection.host, JobManagerConnection.port), referenceIdService)
  } yield Initializer(CpcRegistrationController.impl[F](authenticationService, cpcService))
}
