package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

case class CareerLevel private (level: String) extends AnyVal

object CareerLevel {
  implicit val encoder: Encoder[CareerLevel] = Encoder.encodeString.contramap[CareerLevel](_.level)
  implicit val decoder: Decoder[CareerLevel] = Decoder.decodeString.emap(CareerLevel(_).toEither)

  val validCareerLevels: Set[String] = Set("Geen ervaring", "Starter", "E<PERSON>ren", "Leidinggevend", "Senior management", "Directie")

  def apply(level: String): Validated[String, CareerLevel] =
    if (validCareerLevels.containsCaseInsensitive(level)) new CareerLevel(level).valid
    else s"Career level '$level' is not valid.".invalid

  object SwaggerDoc {
    val example = new CareerLevel("Starter")
  }
}
