package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.syntax.validated._
import io.circe.{Decoder, Encoder}

case class Site private (name: String) extends AnyVal

object Site {

  val allowedSites: Set[String] = Set(
    "Nationale Vacaturebank",
    "Intermediair",
    "Tweakers Carrière"
  )

  def apply(name: String): Validated[String, Site] = if (allowedSites contains name) new Site(name).valid
  else s"Site '$name' is not valid.".invalid

  implicit lazy val encoder: Encoder[Site] = Encoder.encodeString.contramap(_.name)
  implicit lazy val decoder: Decoder[Site] = Decoder.decodeString.emap(Site(_).toEither)

  object SwaggerDoc {
    val iol = new Site("Intermediair")
    val nvb = new Site("Nationale Vacaturebank")
  }
}
