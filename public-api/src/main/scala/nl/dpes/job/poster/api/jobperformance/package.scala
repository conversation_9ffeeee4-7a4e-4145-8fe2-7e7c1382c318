package nl.dpes.job.poster.api

import cats.effect.kernel.Async
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.config.Configuration
import nl.dpes.job.poster.api.ErrorMessage
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.apikey.api.ApiKey.authenticationDescription
import nl.dpes.job.poster.api.tracing.CorrelationId
import sttp.tapir._
import sttp.tapir.server.PartialServerEndpoint

package object jobperformance extends Configuration {

  case class JobPerformanceEndpoint(version: String) {

    def baseEndpoint[F[_]: Async, E <: ErrorMessage](
      securityLogic: BearerToken => F[Either[E, SalesForceId]],
      expectedErrors: Endpoint[Unit, Unit, Unit, Unit, Any] => Endpoint[Unit, Unit, E, Unit, Any]
    ): PartialServerEndpoint[BearerToken, SalesForceId, CorrelationId, E, Unit, Any, F] =
      expectedErrors(endpoint.in("api" / version))
        .securityIn(auth.bearer[String]().mapTo[BearerToken].description(authenticationDescription(Dashboard.url)))
        .in(header[Option[CorrelationId]]("X-Correlation-ID").map(CorrelationId.generateIfNotProvided))
        .serverSecurityLogic(securityLogic)
  }
}
