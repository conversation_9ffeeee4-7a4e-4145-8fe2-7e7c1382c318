package nl.dpes.job.poster.api.service

sealed trait Cursor {

  def root: String
}

object Cursor {

  case class Field private[Cursor] (field: String, parent: Cursor) extends Cursor {

    override lazy val toString: String = parent match {
      case parent: Field => s"$parent.$field"
      case _             => field
    }

    override lazy val root: String = parent.root
  }

  case class Root(body: String) extends Cursor {

    val root: String = body

    override val toString: String = body
  }

  implicit class Ops(val cursor: Cursor) extends AnyVal {

    def apply(field: String): Field = Field(field, cursor)
  }

  def apply(body: String): Cursor = Root(body)
}
