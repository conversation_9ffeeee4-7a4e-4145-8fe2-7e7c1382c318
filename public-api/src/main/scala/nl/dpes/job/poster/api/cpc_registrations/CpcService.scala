package nl.dpes.job.poster.api.cpc_registrations

import cats.effect.kernel.Ref
import cats.effect.{Async, Resource}
import cats.implicits._
import cats.effect.implicits._
import io.scalaland.chimney.dsl._
import nl.dpes.b2b.jobmanager.service.cpc.CpcRecord
import nl.dpes.b2b.jobmanager.service.{cpc, GrpcJobService}
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.job.v1.job.{JobId => ApiJobId}
import nl.dpes.job.poster.api.reference_id.ReferenceIdService
import org.typelevel.log4cats.{Logger, LoggerFactory}

import scala.concurrent.Future

trait CpcService[F[_]] {
  def getRegisteredApplies(recruiterId: SalesForceId)(period: Period): F[CpcResponse]
}

object CpcService {

  def impl[F[_]: Async: LoggerFactory](
    connection: Resource[F, GrpcJobService],
    referenceIdService: ReferenceIdService[F]
  ): F[CpcService[F]] = for {
    cache <- Ref.of[F, Map[JobId, F[F[Option[ReferenceId]]]]](Map.empty)
  } yield new CpcService[F] {
    val logger: Logger[F] = LoggerFactory[F].getLogger

    // helper function that wraps the future response from jobmanager into a Async[F]
    def jobmanager[A](f: GrpcJobService => Future[A]): F[A] =
      Async[F].fromFuture(connection.use(f(_).pure[F]))

    // converts query input into domain model
    def constructCpcResultsRequest(recruiterId: RecruiterId, period: Period): cpc.CpcResultsRequest =
      period
        .into[CpcRequest]
        .withFieldComputed(_.recruiterId, _ => recruiterId)
        .transform
        .into[cpc.CpcResultsRequest]
        .transform

    // reads reference id using local subdomain model
    def readReferenceId(jobId: JobId): F[Option[ReferenceId]] = for {
      referenceId <- referenceIdService.getReferenceId(jobId.into[ApiJobId].transform)
    } yield referenceId.map(id => ReferenceId(id.value))

    // memoizes the result of the db calls into a map JobId -> F[Option[ReferenceId]]
    def cachedDbResult(jobId: JobId): F[Option[ReferenceId]] = for {
      idMap <- cache.updateAndGet { idMap =>
        idMap.get(jobId) match {
          case Some(_) => idMap
          case None    => idMap + (jobId -> readReferenceId(jobId).memoize)
        }
      }
      memoized    <- idMap(jobId)
      referenceId <- memoized
    } yield referenceId

    def calculateCpcBucket(record: CpcRecord): CpcBucket = record.cpc.value match {
      case "low"    => CpcBucket("low")
      case "middle" => CpcBucket("middle")
      case "high"   => CpcBucket("high")
      case unknown  => CpcBucket(unknown)
    }

    def responseToResult(record: CpcRecord): F[CpcResult] = for {
      referenceId <- cachedDbResult(record.jobId.into[JobId].transform)
    } yield record
      .into[CpcResult]
      .withFieldComputed(_.referenceId, _ => referenceId.map(id => ReferenceId(id.value)))
      .withFieldComputed(_.cpcBucket, calculateCpcBucket(_))
      .transform

    override def getRegisteredApplies(recruiterId: SalesForceId)(period: Period): F[CpcResponse] =
      (for {
        response <- jobmanager(_.cpcResultsForRecruiter(constructCpcResultsRequest(RecruiterId(recruiterId.idWithChecksum), period)))
        results  <- response.parTraverse(responseToResult)
      } yield CpcResponse(results))
        .onError { case e: Exception =>
          logger.warn(s"Error while getting CPC results for recruiterId: $recruiterId, period: $period: ${e.getMessage}")
        }
  }
}
