package nl.dpes.job.poster.api.service.apikey.api

import cats.implicits._
import io.circe.{Code<PERSON>, Decoder, Encoder}
import nl.dpes.job.poster.api.converter.Converter
import nl.dpes.job.poster.api.service.apikey.{CreatedAt => Domain}
import sttp.tapir.Schema

import java.sql.Timestamp
import java.time.Instant

case class CreatedAt(value: Timestamp) extends AnyVal

object CreatedAt {

  implicit lazy val convertor: Converter[CreatedAt, Domain] = new Converter[CreatedAt, Domain] {
    override def convert(from: CreatedAt): Domain = Domain(from.value)

    override def invert(from: Domain): CreatedAt = CreatedAt(from.value)
  }

  private def fromInstant(i: Instant): CreatedAt = CreatedAt(Timestamp.from(i))
  private def toInstant(c: CreatedAt): Instant   = c.value.toInstant
  private val instantCodec: Codec[Instant]       = Codec.from(Decoder[Instant], Encoder[Instant])
  implicit def apiKeyIdCodec: Codec[CreatedAt]   = instantCodec.imap(fromInstant)(toInstant)
  implicit def apiKeyIdSchema: Schema[CreatedAt] = Schema.schemaForInstant.map(fromInstant(_).some)(toInstant)
}
