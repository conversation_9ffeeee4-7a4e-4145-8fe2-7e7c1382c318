package nl.dpes.job.poster.api.service.apikey.api

import cats.implicits._
import io.circe.{Code<PERSON>, Decoder, Encoder}
import nl.dpes.job.poster.api.converter.Converter
import nl.dpes.job.poster.api.service.apikey.{ApiKeyId => Domain}
import sttp.tapir.Schema

case class ApiKeyId(value: String) extends AnyVal

object ApiKeyId {

  implicit lazy val convertor: Converter[ApiKeyId, Domain] = new Converter[ApiKeyId, Domain] {
    override def convert(from: ApiKeyId): Domain = Domain(from.value)

    override def invert(from: Domain): ApiKeyId = ApiKeyId(from.value)
  }

  implicit def apiKeyIdCodec: Codec[ApiKeyId] =
    Codec
      .from(Decoder.decodeString, Encoder.encodeString)
      .imap(ApiKeyId(_))(_.value)

  implicit def apiKeyIdSchema: Schema[ApiKeyId] = Schema.derived
}
