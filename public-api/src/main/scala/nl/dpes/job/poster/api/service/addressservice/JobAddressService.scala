package nl.dpes.job.poster.api.service.addressservice

import cats.effect.Async
import nl.dpes.job.poster.api.job.v1.job.{City, Geolocation}

trait JobAddressService[F[_]] {

  def getZipcodeByGeolocation(geolocation: Geolocation): F[AddressInfo]
  def getZipcodeByCity(city: City): F[AddressInfo]
}

object JobAddressService {

  def impl[F[_]: Async](client: AddressServiceClient[F]): JobAddressService[F] = new JobAddressService[F] {
    override def getZipcodeByGeolocation(geolocation: Geolocation): F[AddressInfo] = client.getZipcodeByGeolocation(geolocation)

    override def getZipcodeByCity(city: City): F[AddressInfo] = client.getZipcodeByCity(city)
  }
}
