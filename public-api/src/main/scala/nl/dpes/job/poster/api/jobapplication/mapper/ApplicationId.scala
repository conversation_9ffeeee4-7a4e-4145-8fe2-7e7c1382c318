package nl.dpes.job.poster.api.jobapplication.mapper

import cats.implicits.catsSyntaxEitherId
import io.circe.{Decoder, Encoder}

case class ApplicationId private (applicationId: String) extends AnyVal

object ApplicationId {

  implicit lazy val encoder: Encoder[ApplicationId] = Encoder.encodeString.contramap(_.applicationId)
  implicit lazy val decoder: Decoder[ApplicationId] = Decoder.decodeString.emap(ApplicationId(_).asRight)
}
