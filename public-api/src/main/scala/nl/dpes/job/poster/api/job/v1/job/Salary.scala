package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits.catsSyntaxValidatedId
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{Salary => AppSalary}

case class Salary private (salary: Float) extends AnyVal

object Salary {

  implicit lazy val encoder: Encoder[Salary]            = Encoder.encodeFloat.contramap[Salary](_.salary)
  implicit lazy val decoder: Decoder[Salary]            = Decoder.decodeFloat.emap(Salary(_).toEither)
  implicit lazy val ordering: Ordering[Salary]          = Ordering.by(_.salary)
  implicit def orderedSalary: Salary => Ordered[Salary] = Ordered.orderingToOrdered[Salary]

  def apply(salary: Float): Validated[String, Salary] =
    if (salary < 1) s"Salary '$salary' cannot be less than 1.".invalid
    else new Salary(salary).valid

  object SwaggerDoc {
    val minSalaryExample = new Salary(10000)
    val maxSalaryExample = new Salary(30000)
  }

  def map(salary: Salary)(implicit cursor: Cursor): Validated[MappingError, AppSalary] =
    AppSalary(salary.salary).leftMap(error => MappingError(cursor -> error))

}
