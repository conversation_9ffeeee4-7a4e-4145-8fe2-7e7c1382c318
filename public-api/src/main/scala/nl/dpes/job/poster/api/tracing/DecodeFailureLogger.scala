package nl.dpes.job.poster.api.tracing

import cats.MonadThrow
import cats.syntax.applicativeError._
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.traverse._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import org.typelevel.log4cats.Logger
import sttp.tapir.server.interceptor._
import sttp.tapir.server.interpreter.BodyListener
import sttp.tapir.server.model.ServerResponse

case class DecodeFailureLogger[F[_]: MonadThrow](authenticationService: AuthenticationService[F], logger: Logger[F])
    extends EndpointInterceptor[F] {

  override def apply[B](responder: Responder[F, B], next: EndpointHandler[F, B]): EndpointHandler[F, B] =
    new EndpointHandler[F, B] {

      import sttp.monad.MonadError

      override def onDecodeSuccess[A, U, I](
        ctx: DecodeSuccessContext[F, A, U, I]
      )(implicit monad: MonadError[F], bodyListener: BodyListener[F, B]): F[ServerResponse[B]] =
        next.onDecodeSuccess(ctx)

      override def onSecurityFailure[A](
        ctx: SecurityFailureContext[F, A]
      )(implicit monad: MonadError[F], bodyListener: BodyListener[F, B]): F[ServerResponse[B]] =
        next.onSecurityFailure(ctx)

      override def onDecodeFailure(
        ctx: DecodeFailureContext
      )(implicit F: MonadError[F], bodyListener: BodyListener[F, B]): F[Option[ServerResponse[B]]] = for {
        decodeFailure <- DecodeFailureLog
          .fromContext(ctx, authenticationService)
          .onError(e => logger.warn("Decode failure: " + e.getMessage))
          .attempt
        _      <- decodeFailure.traverse(f => logger.warn(f.asJson))
        result <- next.onDecodeFailure(ctx)
      } yield result
    }

}
