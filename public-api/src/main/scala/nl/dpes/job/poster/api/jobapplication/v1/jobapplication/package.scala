package nl.dpes.job.poster.api.jobapplication.v1

import java.time.{ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.util.Try
import scala.util.matching.Regex

package object jobapplication {
  val dateFormat: Regex     = "(\\d{4})-(\\d{1,2})-(\\d{1,2})".r
  val dateTimeFormat: Regex = "(\\d{4})-(\\d{1,2})-(\\d{1,2})T(\\d{1,2}):(\\d{1,2}):(\\d{1,2})Z".r
  val ZONE_ID               = "Europe/Amsterdam"
  val ISO_INSTANT_PATTERN   = "yyyy-MM-dd'T'HH:mm:ss'Z'"

  case class UnsupportedDateFormat(unsupportedDateFormat: String)
      extends Throwable(s"Cannot parse date '$unsupportedDateFormat'. The supported format is: '${ISO_INSTANT_PATTERN.replace("'", "")}'.")

  def constructIsoInstant(year: String, month: String, day: String, hours: String, minutes: String, seconds: String): Try[String] =
    Try(
      ZonedDateTime
        .of(year.toInt, month.toInt, day.toInt, hours.toInt, minutes.toInt, seconds.toInt, 0, ZoneId.of(ZONE_ID))
        .format(DateTimeFormatter.ofPattern(ISO_INSTANT_PATTERN))
    )
}
