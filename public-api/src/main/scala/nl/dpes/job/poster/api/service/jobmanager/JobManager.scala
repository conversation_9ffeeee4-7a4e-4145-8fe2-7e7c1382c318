package nl.dpes.job.poster.api.service.jobmanager

import akka.actor.ActorSystem
import akka.grpc.GrpcClientSettings
import cats.Parallel
import cats.data.{EitherT, Validated}
import cats.effect.{Async, Resource, Sync}
import cats.syntax.applicative._
import cats.syntax.applicativeError._
import cats.syntax.apply._
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.monadError._
import cats.syntax.option._
import cats.syntax.parallel._
import cats.syntax.traverse._
import cats.syntax.validated._
import io.grpc.{Status, StatusRuntimeException}
import nl.dpes.b2b.domain._
import nl.dpes.b2b.jobmanager.domain.{InitialJobContent, jobsummary, localDateOrdering, Range => JMRange}
import nl.dpes.b2b.jobmanager.service.GrpcJobService
import nl.dpes.b2b.jobmanager.v1.JobServiceClient
import nl.dpes.b2b.salesforce.domain
import nl.dpes.b2b.salesforce.domain.{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Id, VerifiedRecruiter}
import nl.dpes.job.poster.api.defaults.Defaults
import nl.dpes.job.poster.api.job.v1.job
import nl.dpes.job.poster.api.service.job.{Job, JobSummary, JobUpdate}
import nl.dpes.job.poster.api.service.jobmanager.JobManagerAdapter.{mapToConfiguration, mapToInitialJobContent, ErrorMessageOps}
import nl.dpes.job.poster.api.service.logo.{LogoKey, LogoService}
import nl.dpes.job.poster.api.service.occupationclassification.{Classification, OccupationClassifier}
import nl.dpes.job.poster.api.service.prediction.VacancyEnricher
import nl.dpes.job.poster.api.service.recruiter.AccessToken
import nl.dpes.job.poster.api.service.shared.{
  CareerLevel,
  Configuration,
  ContractType,
  ContractTypes,
  EducationLevel,
  EducationLevels,
  Html,
  JobCategories,
  JobCategory,
  JobId,
  JobStatus,
  JobTitle,
  Prediction,
  PublicationPeriod
}
import nl.dpes.job.poster.api.service.{prediction, Cursor}
import nl.dpes.job.poster.api.ServiceUnavailable
import nl.dpes.job.poster.api.tracing.RequestTimer.ClientName.jobManager
import nl.dpes.job.poster.api.tracing.{CorrelationId, RequestTimer}
import org.slf4j.{Logger, LoggerFactory => Slf4jLoggerFactory}
import org.typelevel.log4cats.LoggerFactory

import java.time.LocalDate
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

trait JobManager[F[_]] {
  def getJob(accessToken: AccessToken, recruiterId: SalesForceId, jobId: JobId): F[JobSummary]

  def postJob(
    accessToken: AccessToken,
    jobId: JobId,
    job: Job,
    configuration: Option[Configuration],
    defaults: Defaults,
    recruiter: Recruiter,
    correlationId: CorrelationId
  ): F[Unit]
  def updateJob(accessToken: AccessToken, jobId: JobId, job: JobUpdate, defaults: Defaults, recruiter: Recruiter): F[Unit]

  def deleteJob(accessToken: AccessToken, jobId: JobId, recruiterId: SalesForceId): F[Unit]
  def updateJobStatus(accessToken: AccessToken, jobId: JobId, recruiterId: SalesForceId, status: job.JobStatus): F[Unit]
}

object JobManager {

  implicit lazy val actorSystem: ActorSystem     = ActorSystem.create()
  implicit lazy val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit lazy val logger: Logger               = Slf4jLoggerFactory.getLogger("JobManager")

  def stripGrpcError(message: String): String = {
    val failedPrecondition       = "GRPC server was FAILED_PRECONDITION: '(.+')'".r
    val secondFailedPrecondition = "GRPC server was FAILED_PRECONDITION: '(.+)'".r
    message match {
      case failedPrecondition(innerMessage)       => innerMessage
      case secondFailedPrecondition(innerMessage) => innerMessage
      case _                                      => message
    }
  }

  def apply[F[_]: Async: Parallel: LoggerFactory](
    connection: Resource[F, GrpcJobService],
    logoServiceResource: Resource[F, LogoService[F]],
    occupationClassifier: OccupationClassifier[F],
    vacancyEnricherService: VacancyEnricher[F]
  ): JobManager[F] =
    new JobManager[F] {

      def mergeDefaults(job: Job, defaults: Defaults): F[Job] =
        Async[F].fromValidated(job.mergeDefaults(defaults).valid)

      def mergeDefaults(job: JobUpdate, defaults: Defaults): F[JobUpdate] =
        Async[F].fromValidated(job.mergeDefaults(defaults).valid)

      def mapConfiguration(configuration: Option[Configuration]): F[JobConfiguration] =
        Async[F].fromValidated(mapToConfiguration(configuration)(Cursor("body")("configuration")))

      def getDuration(configuration: JobConfiguration): F[Duration] =
        configuration match {
          case PostingDefinition(_, available, _, _) => available.pure
          case _: Budget                             => (60 days).pure.widen
        }

      def extractJobCategories(predictionFromService: prediction.Prediction): F[JobCategories] =
        Async[F].fromValidated(
          (predictionFromService.jobCategories.traverse(category => JobCategory(category.value)) andThen (categories =>
            JobCategories(categories.toSet.take(JobCategories.maximumAmountOfCategories))
          )).leftMap(e => InvalidData(e))
        )

      def extractEducationLevels(predictionFromService: prediction.Prediction): F[EducationLevels] =
        Async[F].fromValidated(
          (EducationLevel(predictionFromService.educationLevel.value) andThen (level => EducationLevels(Set(level)))).leftMap(e =>
            InvalidData(e)
          )
        )

      def extractCareerLevel(predictionFromService: prediction.Prediction): F[CareerLevel] =
        Async[F].fromValidated(CareerLevel(predictionFromService.careerLevel.value).leftMap(e => new Throwable(e)))

      def extractContractTypes(predictionFromService: prediction.Prediction): F[ContractTypes] =
        Async[F].fromValidated(
          (ContractType(predictionFromService.contractType.value) andThen (contractType => ContractTypes(Set(contractType)))).leftMap(e =>
            InvalidData(e)
          )
        )

      def predict(title: String, description: Html): F[Prediction] = {
        import prediction.{JobDescription, JobTitle}
        for {
          prediction      <- vacancyEnricherService.predict(JobTitle(title), JobDescription(description.text))
          jobCategories   <- extractJobCategories(prediction)
          educationLevels <- extractEducationLevels(prediction)
          careerLevel     <- extractCareerLevel(prediction)
          contractTypes   <- extractContractTypes(prediction)
        } yield Prediction(jobCategories, educationLevels, careerLevel, contractTypes)

      }

      def mapJob(
        job: Job,
        maximumProductDuration: Duration,
        logoKey: Option[LogoKey],
        classification: Option[Classification]
      )(implicit
        cursor: Cursor = Cursor("body")
      ): F[InitialJobContent] =
        Async[F].fromValidated(mapToInitialJobContent(job, maximumProductDuration, logoKey, classification))

      def mapJobUpdate(
        job: JobUpdate,
        maximumProductDuration: Duration,
        logoKey: Option[LogoKey],
        classification: Option[Classification]
      )(implicit
        cursor: Cursor = Cursor("body")
      ): F[InitialJobContent] =
        Async[F].fromValidated(mapToInitialJobContent(job, maximumProductDuration, logoKey, classification))

      val recruiterMapper: (domain.SalesForceId, domain.Company) => VerifiedRecruiter = VerifiedRecruiter.apply

      def mapRecruiter(recruiter: Recruiter): F[VerifiedRecruiter] = for {
        company <- Async[F].fromOption(recruiter.optionalCompany, new Throwable("No company"))
      } yield recruiterMapper(recruiter.salesforceId, company)

      def predictionNeeded(job: Job): Boolean =
        job.jobCategories.isEmpty || job.educationLevels.isEmpty || job.careerLevel.isEmpty || job.contractTypes.isEmpty

      def predictionNeeded(job: JobUpdate): Boolean =
        job.jobCategories.isEmpty || job.educationLevels.isEmpty || job.careerLevel.isEmpty || job.contractTypes.isEmpty

      def predictionProcess(job: Job): F[Option[Prediction]] =
        if (predictionNeeded(job)) (job.title, job.description).traverseN(predict)
        else none[Prediction].pure

      def predictionProcess(job: JobUpdate): F[Option[Prediction]] =
        if (predictionNeeded(job)) (job.title, job.description).traverseN(predict)
        else none[Prediction].pure

      override def postJob(
        accessToken: AccessToken,
        jobId: JobId,
        job: Job,
        configuration: Option[Configuration],
        defaults: Defaults,
        recruiter: Recruiter,
        correlationId: CorrelationId
      ): F[Unit] =
        logoServiceResource.use(logoService =>
          for {
            mergedConfiguration <- Async[F].delay(configuration orElse defaults.configuration)
            mergedJob           <- mergeDefaults(job, defaults)
            (predicted, classified) <- (
              predictionProcess(mergedJob),
              occupationClassifier.classify(mergedJob, mergedConfiguration)
            ).parTupled
            logoKey             <- mergedJob.logo.traverse(logoService.store(_))
            mappedConfiguration <- mapConfiguration(mergedConfiguration)
            duration            <- getDuration(mappedConfiguration)
            content             <- mapJob(mergedJob.mergePrediction(predicted), duration, logoKey, classified)
            verifiedRecruiter   <- mapRecruiter(recruiter)
            _ <- connection
              .use(service =>
                EitherT(
                  Async[F].fromFuture(
                    RequestTimer(jobManager).log(
                      service
                        .postJob(
                          jobId.value,
                          verifiedRecruiter,
                          content,
                          configuration = mappedConfiguration,
                          Some(correlationId.value)
                        )
                        .pure
                    )
                  )
                )
                  .leftMap {
                    case GenericError(409, message, _) if message.contains("not a job")              => NotAJob(stripGrpcError(message))
                    case GenericError(409, message, _)                                               => NoSuitableCredits(message)
                    case GenericError(_, message, _) if message.toLowerCase.contains("unavailable")  => ServiceUnavailable(message)
                    case GenericError(_, message, _) if message.toLowerCase.contains("io exception") => ServiceUnavailable(message)
                    case GenericError(503, message, _)                                               => Timeout(message)
                    case GenericError(_, message, _)                                                 => new Throwable(message)
                    case ValidationError(errors)                                                     => InvalidData(errors.mkString(", "))
                  }
                  .value
                  .rethrow
                  .onError { case e =>
                    logger.info(s" ${e.getClass} ${e.getMessage}").pure
                  }
              )
          } yield ()
        )

      override def updateJob(accessToken: AccessToken, jobId: JobId, job: JobUpdate, defaults: Defaults, recruiter: Recruiter): F[Unit] =
        logoServiceResource.use(logoService =>
          for {
            mergedJob                   <- mergeDefaults(job, defaults)
            (predicted, classification) <- (predictionProcess(mergedJob), occupationClassifier.classify(mergedJob, None)).parTupled
            logoKey                     <- mergedJob.logo.traverse(logoService.store(_))
            content                     <- mapJobUpdate(mergedJob.mergePrediction(predicted), 60.days, logoKey, classification)
            verifiedRecruiter           <- mapRecruiter(recruiter)
            _ <- connection
              .use(service =>
                EitherT(
                  Async[F].fromFuture(
                    RequestTimer(jobManager).log(
                      service.updateJob(accessToken.token, verifiedRecruiter.salesforceId.idWithChecksum, jobId.value, content, None).pure
                    )
                  )
                )
                  .leftMap {
                    case GenericError(404, _, _)                                             => JobNotFound(jobId)
                    case GenericError(403, message, _)                                       => JobCannotBeUpdated(stripGrpcError(message))
                    case GenericError(409, message, _)                                       => NonUpdatableJob(stripGrpcError(message))
                    case GenericError(_, message, _) if message.contains("Malformed job id") => JobNotFound(jobId)
                    case GenericError(_, message, _)                                         => new Throwable(message)
                    case ValidationError(errors) =>
                      logger.warn(s"""
                           |Validation error occurred while updating job:
                           |Job id: ${jobId.value}
                           |Input publication period: ${job.publicationPeriod}
                           |Merged publication period: ${mergedJob.publicationPeriod}
                           |Publication period to be sent to job-manager: ${content.publicationPeriod}
                           |""".stripMargin)
                      InvalidData(errors.mkString(", "))
                  }
                  .value
                  .rethrow
                  .onError { case e =>
                    logger.info(s" ${e.getClass} ${e.getMessage}").pure
                  }
              )
          } yield ()
        )

      override def deleteJob(accessToken: AccessToken, jobId: JobId, recruiterId: SalesForceId): F[Unit] = connection.use(service =>
        EitherT(
          Async[F].fromFuture(
            RequestTimer(jobManager).log(
              service.deleteJob(accessToken.token, recruiterId.idWithChecksum, jobId.value, responsibleUserId = None).pure
            )
          )
        )
          .leftMap {
            case GenericError(404, _, _)                                             => JobNotFound(jobId)
            case GenericError(403, message, _)                                       => NoJobOwnership(message)
            case GenericError(_, message, _) if message.contains("Malformed job id") => JobNotFound(jobId)
            case GenericError(_, message, _)                                         => JobCannotBeDeleted(message)
          }
          .value
          .rethrow
      )

      override def updateJobStatus(accessToken: AccessToken, jobId: JobId, recruiterId: SalesForceId, status: job.JobStatus): F[Unit] =
        status match {
          case job.JobStatus.Suspend => suspendJob(connection, accessToken, jobId, recruiterId)
          case job.JobStatus.Resume  => resumeJob(connection, accessToken, jobId, recruiterId)
        }

      override def getJob(accessToken: AccessToken, recruiterId: SalesForceId, jobId: JobId): F[JobSummary] = connection
        .use { service =>
          EitherT(
            Async[F].fromFuture(
              service.getJobSummary(accessToken.token, jobId.value, recruiterId).pure
            )
          )
            .leftMap {
              case GenericError(404, _, _)       => JobNotFound(jobId)
              case GenericError(400, message, _) => InvalidData(message)
              case GenericError(_, message, _)   => JobManagerError(jobId.value, message)
            }
            .value
            .rethrow
            .flatMap { jmJob =>
              Async[F].fromValidated(
                (
                  Validated.valid(JobTitle(jmJob.jobTitle.value)),
                  Validated.valid(JobStatus.fromJobManager(jmJob.status)),
                  constructPublicationPeriod(jmJob)
                ).mapN(JobSummary).leftMap(e => new Throwable(e))
              )
            }
        }
        .adaptError {
          case ex: StatusRuntimeException if ex.getStatus.getCode.value() == Status.NOT_FOUND.getCode.value() =>
            logger.warn(s"Error occurred: $ex")
            JobNotFound(jobId)
        }
    }

  def constructPublicationPeriod(jobSummary: jobsummary.JobSummary): Validated[String, PublicationPeriod] =
    Validated.fromEither(for {
      range <- Validated
        .fromEither(JMRange[LocalDate](jobSummary.publicationPeriod.start, jobSummary.publicationPeriod.end))
        .toEither
      publicationperiod <- PublicationPeriod.fromJobManager(range).toEither
    } yield publicationperiod)

  private def suspendJob[F[_]: Async: LoggerFactory](
    connection: Resource[F, GrpcJobService],
    accessToken: AccessToken,
    jobId: JobId,
    recruiterId: SalesForceId
  ) =
    connection.use(service =>
      RequestTimer(jobManager).log(
        EitherT(
          Async[F].fromFuture(
            service.suspendJob(accessToken.token, recruiterId.idWithChecksum, jobId.value, responsibleUserId = None).pure
          )
        )
          .leftMap {
            case GenericError(404, _, _)                                             => JobNotFound(jobId)
            case GenericError(409, message, _)                                       => JobCannotBeSuspended(message.cleanGrpcError)
            case GenericError(_, message, _) if message.contains("Malformed job id") => JobNotFound(jobId)
            case GenericError(_, message, _)                                         => JobManagerError(jobId.value, message.cleanGrpcError)
          }
          .value
          .rethrow
      )
    )

  private def resumeJob[F[_]: Async: LoggerFactory](
    connection: Resource[F, GrpcJobService],
    accessToken: AccessToken,
    jobId: JobId,
    recruiterId: SalesForceId
  ) =
    connection.use(service =>
      RequestTimer(jobManager).log(
        EitherT(
          Async[F].fromFuture(
            service.resumeJob(accessToken.token, recruiterId.idWithChecksum, jobId.value, responsibleId = None).pure
          )
        )
          .leftMap {
            case GenericError(404, _, _)                                             => JobNotFound(jobId)
            case GenericError(409, message, _)                                       => JobCannotBeResumed(message.cleanGrpcError)
            case GenericError(_, message, _) if message.contains("Malformed job id") => JobNotFound(jobId)
            case GenericError(_, message, _)                                         => JobManagerError(jobId.value, message.cleanGrpcError)
          }
          .value
          .rethrow
      )
    )

  def connection[F[_]: Async](host: String, port: Int): Resource[F, GrpcJobService] = for {
    client  <- client(host, port)
    service <- service(client)
  } yield service

  private def service[F[_]: Sync](client: JobServiceClient): Resource[F, GrpcJobService] =
    Resource.pure(new GrpcJobService(client))

  private def client[F[_]: Async](host: String, port: Int): Resource[F, JobServiceClient] =
    Resource.make(
      Async[F].delay(
        JobServiceClient(
          GrpcClientSettings
            .connectToServiceAt(host, port)
            .withTls(false)
            .withConnectionAttempts(3)
            .withResolveTimeout(5.seconds)
        )
      )
    )(client => Async[F].delay(client.close()))

  case class InvalidData(message: String) extends Throwable(message)

  case class NoSuitableCredits(message: String) extends Throwable(message)
  case class NotAJob(message: String)           extends Throwable(message)

  case class JobCannotBeDeleted(reason: String) extends Throwable(reason)
  case class JobNotFound(id: JobId)             extends Throwable(s"Job ${id.value} not found")

  case class JobCannotBeUpdated(reason: String) extends Throwable(reason)

  case class JobCannotBeSuspended(reason: String)           extends Throwable(reason)
  case class JobCannotBeResumed(reason: String)             extends Throwable(reason)
  case class Timeout(reason: String)                        extends Throwable(reason)
  case class NonUpdatableJob(reason: String)                extends Throwable(reason)
  case class NoJobOwnership(reason: String)                 extends Throwable(reason)
  case class JobManagerError(jobId: String, reason: String) extends Throwable(s"Error occurred for job '$jobId', due to '$reason'")
}
