package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

case class JobCategories private (jobCategories: Set[JobCategory]) extends AnyVal

object JobCategories {

  implicit lazy val encoder: Encoder[JobCategories] = Encoder.encodeSet[JobCategory].contramap(_.jobCategories)
  implicit lazy val decoder: Decoder[JobCategories] = Decoder.decodeSet[JobCategory].emap(JobCategories(_).toEither)

  val minimumAmountOfCategories = 1
  val maximumAmountOfCategories = 2

  def checkMinimumAmount(categories: JobCategories): Validated[String, JobCategories] =
    Validated.cond(
      categories.jobCategories.size >= minimumAmountOfCategories,
      categories,
      s"At least $minimumAmountOfCategories categories should be chosen"
    )

  def checkMaximumAmount(categories: JobCategories): Validated[String, JobCategories] =
    Validated.cond(
      categories.jobCategories.size <= maximumAmountOfCategories,
      categories,
      s"At most $maximumAmountOfCategories categories should be chosen"
    )

  def apply(items: Set[JobCategory]): Validated[String, JobCategories] =
    new JobCategories(items).valid andThen checkMinimumAmount andThen checkMaximumAmount

  object SwaggerDoc {
    val example = new JobCategories(Set(JobCategory.SwaggerDoc.administratief, JobCategory.SwaggerDoc.automatisering))
  }
}
