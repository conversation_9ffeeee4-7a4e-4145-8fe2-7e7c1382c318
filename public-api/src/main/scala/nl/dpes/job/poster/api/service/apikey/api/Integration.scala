package nl.dpes.job.poster.api.service.apikey.api

import cats.implicits._
import io.circe.{<PERSON><PERSON>, Decoder, Encoder}
import sttp.tapir.Schema
import nl.dpes.job.poster.api.service.apikey.{Integration => Domain}
import nl.dpes.job.poster.api.converter.Converter

case class Integration(value: String) extends AnyVal

object Integration {

  implicit val convertor: Converter[Integration, Domain] = new Converter[Integration, Domain] {
    override def convert(from: Integration): Domain = Domain(from.value)

    override def invert(from: Domain): Integration = Integration(from.value)
  }

  implicit def apiKeyInformationCodec: Codec[Integration] =
    Codec
      .from(Decoder.decodeString, Encoder.encodeString)
      .imap(Integration(_))(_.value)
  implicit def apiKeyInformationSchema: Schema[Integration] = Schema.schemaForString.map(Integration(_).some)(_.value)
}
