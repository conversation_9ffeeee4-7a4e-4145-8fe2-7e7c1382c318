package nl.dpes.job.poster.api.jobperformance

import cats.Parallel
import cats.effect.kernel.Async
import cats.implicits.catsSyntaxApplicativeId
import nl.dpes.job.poster.api.config.Configuration
import nl.dpes.job.poster.api.reference_id.{ReferenceIdRepository, ReferenceIdService}
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import org.http4s.HttpRoutes
import org.typelevel.log4cats.LoggerFactory
import sttp.tapir.server.ServerEndpoint

package object v1 extends Configuration {

  val jobPerformanceClient: JobPerformanceEndpoint = JobPerformanceEndpoint("v1")

  case class Initializer[F[_]](jobPerformanceController: JobPerformanceController[F]) {
    def endpoints: F[List[ServerEndpoint[Any, F]]] = jobPerformanceController.endpoints

    def routes: F[HttpRoutes[F]] = jobPerformanceController.routes
  }

  def initializer[F[_]: Async: Parallel: LoggerFactory](
    authenticationService: AuthenticationService[F],
    referenceIdRepository: ReferenceIdRepository[F]
  ): F[Initializer[F]] =
    Initializer(
      JobPerformanceController
        .impl[F](
          authenticationService,
          JobPerformanceService.impl(
            PerformanceServiceClient.impl(
              ReportingService.host,
              PerformanceServiceClient.performanceBackend
            ),
            ReferenceIdService.impl(referenceIdRepository)
          )
        )
    ).pure
}
