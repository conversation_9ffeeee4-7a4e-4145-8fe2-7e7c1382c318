package nl.dpes.job.poster.api.jobperformance.v1

import cats.effect.kernel.Async
import cats.implicits.{catsSyntaxApplicativeError, catsSyntaxEitherId}
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.traverse._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.jobperformance.v1.PerformanceServiceClient.{JobPerformanceError, UnauthorizationError}
import nl.dpes.job.poster.api.reference_id.ReferenceIdService
import nl.dpes.job.poster.api.{ErrorMessage, Unauthorized, Unknown}
import nl.dpes.job.poster.api.tracing.CorrelationId
import org.slf4j.{Logger, LoggerFactory}

trait JobPerformanceService[F[_]] {

  def getJobsPerformance(recruiterId: SalesForceId)(
    correlationId: CorrelationId
  ): F[Either[ErrorMessage, List[JobPerformance]]]
}

object JobPerformanceService {

  lazy val logger: Logger = LoggerFactory.getLogger(getClass.getName)

  def impl[F[_]: Async](
    performanceServiceClient: PerformanceServiceClient[F],
    referenceIdService: ReferenceIdService[F]
  ): JobPerformanceService[F] = new JobPerformanceService[F] {

    def getJobsPerformance(
      recruiterId: SalesForceId
    )(correlationId: CorrelationId): F[Either[ErrorMessage, List[JobPerformance]]] = (for {
      jobsPerformance <- performanceServiceClient.getJobsPerformance(recruiterId)
      performance     <- jobsPerformance.values.toList.traverse(perf => Performance.map(perf)(referenceIdService))
    } yield performance.asRight[ErrorMessage]) recover {
      case ex: JobPerformanceError =>
        logger.error(ex.getMessage)
        Unknown(ex.getMessage).asLeft
      case ex: UnauthorizationError =>
        logger.error(ex.getMessage)
        Unauthorized(ex.message).asLeft
    }
  }
}
