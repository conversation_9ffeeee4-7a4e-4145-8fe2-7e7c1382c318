package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits.catsSyntaxOption
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.job.v1.job.SalaryPeriod

final case class SalaryPeriod private (value: String) extends AnyVal

object SalaryPeriod {
  val Hour        = new SalaryPeriod("Hour")
  val Month       = new SalaryPeriod("Month")
  val Year        = new SalaryPeriod("Year")
  val Unspecified = new SalaryPeriod("Unspecified")

  val all: Map[String, SalaryPeriod] = Map(
    "hour"        -> Hour,
    "month"       -> Month,
    "year"        -> Year,
    "unspecified" -> Unspecified
  )

  def apply(period: String): Validated[String, SalaryPeriod] = all
    .get(period.toLowerCase)
    .toValid(s"Cannot get period from '$period'")

  implicit lazy val jsonEncoder: Encoder[SalaryPeriod] = Encoder.encodeString.contramap(_.value)
  implicit lazy val jsonDecoder: Decoder[SalaryPeriod] = Decoder.decodeString.emap(SalaryPeriod.apply(_).toEither)
}
