package nl.dpes.job.poster.api.job.v1.job

import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.shared.{ContactInformation => ServiceContactInformation}

case class ContactInformation(
  contact: Contact,
  address: Option[PostalAddress] = None,
  website: Option[String] = None
)

object ContactInformation {
  implicit lazy val encoder: Encoder[ContactInformation] = deriveEncoder
  implicit lazy val decoder: Decoder[ContactInformation] = deriveDecoder

  def mapToService(contactInformation: ContactInformation): ServiceContactInformation = ServiceContactInformation(
    Contact.mapToService(contactInformation.contact),
    contactInformation.address.map(PostalAddress.mapToService),
    contactInformation.website
  )
}
