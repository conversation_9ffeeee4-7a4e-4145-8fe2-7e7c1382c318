package nl.dpes.job.poster.api.job.v1.job

import cats.syntax.option._
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import sttp.tapir.Schema.annotations.description

case class JobUpdate(
  @description("Represents the job title.")
  title: Option[String],
  @description(s"The job description in HTML. Only tags allowed: ${Html.allowedTags.mkString(", ")}.")
  description: Option[Html],
  @description("Represents the occupation.") // does this need to match jdco?
  occupation: Option[Occupation],
  @description( // this field is called "vakgebied" on nvb, which translates to dicipline
    s"The category for this job. This field must contain at least 1 job category and can accept up to 2 categories. " +
    s"Allowed values: ${JobCategory.validJobCategories.format}."
  )
  jobCategories: Option[JobCategories],
  @description(
    s"The industry for this job. This field must contain at least 1 industry category and can accept up to 2 categories. " +
    s"All possible values are: ${IndustryCategory.validIndustryCategories.format}"
  )
  industryCategories: Option[IndustryCategories],
  @description(
    s"The required education levels for this job. This field must contain at least 1 education level and can accept up to 5 levels. " +
    s"Allowed values: ${EducationLevel.validEducationLevels.format}."
  )
  educationLevels: Option[EducationLevels],
  @description(
    s"The required career level for this job. Allowed values: ${CareerLevel.validCareerLevels.format}."
  )
  careerLevel: Option[CareerLevel],
  @description(
    s"The contract types for this job. This field must contain at least 1 contract type and can accept up to 5 types. " +
    s"Allowed values: ${ContractType.validContractTypes.format}."
  )
  contractTypes: Option[ContractTypes],
  @description(s"The workplace of this job. Allowed values: ${Workplace.validWorkplaces.format}.")
  workplace: Option[Workplace],
  @description("The amount of hours per week required for this  job.")
  workingHours: Option[Range[Hour]],
  @description("Offered salary for this job.")
  salary: Option[SalaryRange],
  @description("Location where the job will be.")
  location: Option[Location],
  @description(
    "The period the job will be online. Note that even for jobs that will be online in the future credits will be consumed immediately."
  )
  publicationPeriod: Option[PublicationPeriod],
  @description(
    "The way an applicant can apply for this job. The job application can be done via the job board or via an external website."
  )
  applicationMethod: Option[ApplicationMethod],
  @description("The url for a logo. The actual image will be copied by our system.")
  logo: Option[Logo],
  @description("The url for a video. Currently only Youtube and Vimeo are supported.")
  video: Option[Video],
  @description("Information on the company.")
  company: Option[Company],
  @description("Information on how to contact the company about this job.") // redundant?
  contactInformation: Option[ContactInformation]
)

object JobUpdate {
  implicit lazy val jobDecoder: Decoder[Job] = deriveDecoder[Job]
  implicit lazy val jobEncoder: Encoder[Job] = deriveEncoder[Job]

  object SwaggerDoc {

    val example: JobUpdate = JobUpdate(
      "Scala developer gezocht".some,
      Html.SwaggerDoc.example.some,
      Occupation.SwaggerDoc.example.some,
      JobCategories.SwaggerDoc.example.some,
      IndustryCategories.SwaggerDoc.example.some,
      EducationLevels.SwaggerDoc.example.some,
      CareerLevel.SwaggerDoc.example.some,
      ContractTypes.SwaggerDoc.example.some,
      Workplace.SwaggerDoc.example.some,
      Range.SwaggerDoc.hourExample.some,
      SalaryRange.SwaggerDoc.example.some,
      Zipcode("1018LL").some,
      PublicationPeriod.SwaggerDoc.example.some,
      ApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
      Logo.SwaggerDoc.example.some,
      Video.SwaggerDoc.example.some,
      Company.SwaggerDoc.example.some,
      ContactInformation(
        Contact(
          Name("John", "Doe"),
          "**********".some,
          "<EMAIL>"
        ),
        PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
        "https://www.nationalevacaturebank.nl".some
      ).some
    )

    val jobObjectDescription = "A JSON representation for Job object"

  }
}
