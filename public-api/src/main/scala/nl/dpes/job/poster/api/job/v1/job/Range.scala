package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.shared.{Hour => AppHour, Range => AppRange, Salary => AppSalary}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}

case class Range[A: Ordering] private (lower: A, upper: A)

object Range {

  implicit def rangeEncoder[A: Encoder: Ordering]: Encoder[Range[A]] = (range: Range[A]) =>
    Json.obj(
      "lower" -> range.lower.asJson,
      "upper" -> range.upper.asJson
    )

  implicit def rangeDecoder[A: Decoder: Ordering]: Decoder[Range[A]] = (c: HCursor) =>
    for {
      lower <- c.downField("lower").as[A]
      upper <- c.downField("upper").as[A]
      range <- Range[A](lower, upper).toEither.leftMap(e => DecodingFailure(e, c.history))
    } yield range

  def apply[A: Ordering](lower: A, upper: A): Validated[String, Range[A]] =
    if (implicitly[Ordering[A]].gt(lower, upper))
      s"Lower value '$lower' cannot be greater than upper value '$upper'.".invalid
    else new Range(lower, upper).valid

  object SwaggerDoc {
    val hourExample = new Range[Hour](Hour.SwaggerDoc.minHourExample, Hour.SwaggerDoc.maxHourExample)
  }

  def mapWorkingHours(workingHours: Option[Range[Hour]])(implicit cursor: Cursor): Validated[MappingError, Option[AppRange[AppHour]]] =
    workingHours
      .traverse { range =>
        (for {
          lower <- AppHour(range.lower.hour).toEither
          upper <- AppHour(range.upper.hour).toEither
          range <- AppRange(lower, upper).toEither
        } yield range).toValidated
      }
      .leftMap(error => MappingError(cursor -> error))

  def mapSalary(salary: Option[Range[Salary]])(implicit cursor: Cursor): Validated[MappingError, Option[AppRange[AppSalary]]] =
    salary
      .traverse { range =>
        (for {
          lower <- AppSalary(range.lower.salary).toEither
          upper <- AppSalary(range.upper.salary).toEither
          range <- AppRange(lower, upper).toEither
        } yield range).toValidated
      }
      .leftMap(error => MappingError(cursor -> error))
}
