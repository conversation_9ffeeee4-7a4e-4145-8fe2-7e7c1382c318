package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import sttp.tapir.Schema.annotations.description
import nl.dpes.job.poster.api.service.shared.{
  Company => AppCompany,
  CompanyType => AppCompanyType,
  DomesticAddress => AppDomesticAddress,
  ForeignAddress => AppForeignAddress,
  ISOAlpha2 => AppISOAlpha2,
  PostalAddress => AppPostalAddress,
  Website => AppWebsite
}

case class Company(
  @description("The name of the company") // does it need to be restricted for direct employers?
  name: String,
  @description("The location of the company itself, does not need to be the same as the location of the job.")
  address: Option[Address] = None,
  @description(s"The type of company, allowed values: ${CompanyType.validCompanyTypes.mkString(", ")}")
  companyType: CompanyType,
  @description("Url for more information")
  website: Option[Website] = None
)

object Company {

  implicit lazy val encoder: Encoder[Company] = deriveEncoder[Company]
  implicit lazy val decoder: Decoder[Company] = deriveDecoder[Company]

  def map(company: Option[Company])(implicit cursor: Cursor): Validated[MappingError, Option[AppCompany]] = company
    .traverse { company =>
      val name = company.name
      val address = company.address.map {
        case PostalAddress(streetNameAndHouseNumber, city, zipCode) => AppPostalAddress(streetNameAndHouseNumber, city, zipCode)
        case DomesticAddress(street, houseNumber, houseNumberAddition, zipCode, city) =>
          AppDomesticAddress(street, houseNumber, houseNumberAddition, zipCode, city)
      }
      (AppCompanyType(company.companyType.companyType), company.website.traverse(site => AppWebsite(site.url)))
        .mapN((companyType, website) => AppCompany(name, address, companyType, website))
    }
    .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {

    val example = new Company(
      "ACME corp",
      PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
      CompanyType.SwaggerDoc.example,
      Website.SwaggerDoc.example.some
    )
  }
}
