package nl.dpes.job.poster.api.service.prediction

import cats.Functor
import cats.syntax.functor._
import org.slf4j.{<PERSON><PERSON>, <PERSON>ggerFactory}

trait VacancyEnricher[F[_]] {
  def predict(title: JobTitle, text: JobDescription): F[Prediction]
}

object VacancyEnricher {

  lazy val logger: Logger = LoggerFactory.getLogger(getClass.getName)

  def impl[F[_]: Functor](client: VacancyEnricherClient[F]): VacancyEnricher[F] = new VacancyEnricher[F] {

    override def predict(title: JobTitle, text: JobDescription): F[Prediction] = {
      logger.info(s"[VacancyEnricher] Calling VacancyEnricher with  title: $title and text: $text")
      client
        .predict(VacancyEnricherClient.Request(title, text))
        .map { response =>
          val prediction = Prediction(
            response.`DCO`.headOption,
            response.`vakgebied`,
            response.`education-lvl`,
            response.`career-lvl`,
            response.`contract_type`
          )
          logger.info(s"[VacancyEnricher] Got result: $prediction for title: $title and text: $text")
          prediction
        }
    }
  }
}
