package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.syntax.validated._
import io.circe.{Decoder, Encoder}

case class Workplace private (workplace: String) extends AnyVal

object Workplace {

  implicit lazy val encoder: Encoder[Workplace] = Encoder.encodeString.contramap(_.workplace)
  implicit lazy val decoder: Decoder[Workplace] = Decoder.decodeString.emap(Workplace(_).toEither)

  val validWorkplaces: Set[String] = Set(
    "Remote",
    "On-site",
    "Hybride"
  )

  def apply(name: String): Validated[String, Workplace] =
    if (validWorkplaces contains name) new Workplace(name).valid
    else s"Workplace '$name' is not valid.".invalid

  object SwaggerDoc {
    val example = new Workplace("Remote")
  }
}
