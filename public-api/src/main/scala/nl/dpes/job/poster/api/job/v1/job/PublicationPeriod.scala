package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.shared.{Date => AppDate, PublicationPeriod => AppPublicationPeriod}
import nl.dpes.job.poster.api.service.{shared, Cursor, MappingError}
import sttp.tapir.Schema.annotations.description

import scala.concurrent.duration.Duration

case class PublicationPeriod private (
  @description("Represents the publication start date.")
  start: Date,
  @description("Represents the publication end date.")
  end: Option[Date]
) {
  def endForDuration(daysOnline: Duration): Date = end.getOrElse(start.plus(daysOnline))
}

object PublicationPeriod {

  def apply(start: Date, end: Option[Date] = None): Validated[String, PublicationPeriod] = end.map { end =>
    if (end.value < start.value) s"End date '$end' cannot be before start date '$start'.".invalid
    else new PublicationPeriod(start, end.some).valid
  } getOrElse new PublicationPeriod(start, end).valid[String]

  implicit lazy val encoder: Encoder[PublicationPeriod] = (period: PublicationPeriod) =>
    Json.obj(
      "start" -> period.start.asJson,
      "end"   -> period.end.asJson
    )

  implicit lazy val decoder: Decoder[PublicationPeriod] = (c: HCursor) =>
    for {
      start  <- c.downField("start").as[Date]
      end    <- c.downField("end").as[Option[Date]]
      period <- PublicationPeriod(start, end).toEither.leftMap(e => DecodingFailure(e, c.history))
    } yield period

  def fromService(period: shared.PublicationPeriod): PublicationPeriod = new PublicationPeriod(
    Date.fromService(period.start),
    period.end.map(Date.fromService)
  )

  object SwaggerDoc {
    val example = new PublicationPeriod(Date.SwaggerDoc.start, Date.SwaggerDoc.end.some)
  }

  def map(publicationPeriod: Option[PublicationPeriod])(implicit cursor: Cursor): Validated[MappingError, Option[AppPublicationPeriod]] =
    publicationPeriod
      .traverse { period =>
        (for {
          start  <- AppDate(period.start.value).toEither
          end    <- period.end.traverse(end => AppDate(end.value)).toEither
          period <- AppPublicationPeriod(start, end).toEither
        } yield period).toValidated
      }
      .leftMap(error => MappingError(cursor -> error))
}
