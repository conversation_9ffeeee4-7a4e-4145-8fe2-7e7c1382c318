package nl.dpes.job.poster.api.service.shared

import io.circe.{Decoder, Encoder}
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}

case class ContactInformation(
  contact: Contact,
  address: Option[PostalAddress] = None,
  website: Option[String] = None
)

object ContactInformation {
  implicit lazy val encoder: Encoder[ContactInformation] = deriveEncoder
  implicit lazy val decoder: Decoder[ContactInformation] = deriveDecoder
}
