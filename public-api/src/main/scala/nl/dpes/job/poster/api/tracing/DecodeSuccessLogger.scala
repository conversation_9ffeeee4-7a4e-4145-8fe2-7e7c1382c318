package nl.dpes.job.poster.api.tracing

import cats.syntax.applicative._
import cats.syntax.applicativeError._
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.parallel._
import cats.syntax.traverse._
import cats.{MonadThrow, <PERSON>llel}
import io.circe.Json
import io.circe.generic.auto._
import io.circe.syntax._
import nl.dpes.job.poster.api.job.v1.job.{Job, JobCreated, JobId, JobStatus, JobSummary, JobUpdate}
import nl.dpes.job.poster.api.ErrorMessage
import org.typelevel.log4cats.Logger
import sttp.model.StatusCode
import sttp.tapir.model.ServerRequest
import sttp.tapir.server.interceptor._
import sttp.tapir.server.interpreter.BodyListener
import sttp.tapir.server.model.ServerResponse

case class DecodeSuccessLogger[F[_]: MonadThrow: Parallel](logger: Logger[F]) extends EndpointInterceptor[F] {
  import DecodeSuccessLogger._

  override def apply[B](responder: <PERSON><PERSON><PERSON><PERSON>[F, B], next: EndpointHandler[F, B]): EndpointHandler[F, B] =
    new EndpointHandler[F, B] {

      import sttp.monad.MonadError

      override def onDecodeSuccess[A, U, I](
        ctx: DecodeSuccessContext[F, A, U, I]
      )(implicit monad: MonadError[F], bodyListener: BodyListener[F, B]): F[ServerResponse[B]] = {
        implicit val implicitLogger: Logger[F] = logger
        next.onDecodeSuccess(ctx).flatTap(logResult(ctx, _))
      }

      override def onSecurityFailure[A](
        ctx: SecurityFailureContext[F, A]
      )(implicit monad: MonadError[F], bodyListener: BodyListener[F, B]): F[ServerResponse[B]] =
        next.onSecurityFailure(ctx)

      override def onDecodeFailure(
        ctx: DecodeFailureContext
      )(implicit F: MonadError[F], bodyListener: BodyListener[F, B]): F[Option[ServerResponse[B]]] =
        next.onDecodeFailure(ctx)
    }

}

object DecodeSuccessLogger {
  final case class RequestIdWithJob(requestId: RequestId, job: Job)
  final case class RequestIdWithJobUpdate(requestId: RequestId, jobId: JobId, job: JobUpdate)
  final case class RequestIdWithJobStatusUpdate(requestId: RequestId, jobId: JobId, job: JobStatus)
  final case class RequestIdWithJobId(requestId: RequestId, jobId: JobId)
  final case class JobSummaryWithRequestId(job: JobSummary, requestId: RequestId)
  final case class JobCreatedWithRequestId(location: String, jobCreated: JobCreated, requestId: RequestId)
  final case class LocationWithRequestId(location: String, requestId: RequestId)
  final case class UnknownPrincipalType[U](body: U) extends Throwable(s"No json parsing for principal ${body.toString}")
  final case class UnknownInputType[I](body: I)     extends Throwable(s"No json parsing for input ${body.toString}")
  final case class UnknownOutputType[T](output: T)  extends Throwable(s"No json parsing for output ${output.getClass} - ${output.toString}")

  def extractPrincipal[F[_]: MonadThrow, U](principal: U)(implicit logger: Logger[F]): F[Json] = for {
    result <- (principal match {
      case recruiterId: RecruiterId => Json.fromString(recruiterId.idWithChecksum).pure
      case _                        => UnknownPrincipalType(principal).raiseError
    }).widen
  } yield result

  def extractInput[F[_]: MonadThrow, I](input: I)(implicit logger: Logger[F]): F[Json] = for {
    result <- (input match {
      case (requestId: RequestId, _: CorrelationId, job: Job)      => RequestIdWithJob(requestId, job).asJson.pure
      case (requestId: RequestId, _: CorrelationId, jobId: String) => RequestIdWithJobId(requestId, JobId(jobId)).asJson.pure
      case (requestId: RequestId, _: CorrelationId, jobId: JobId)  => RequestIdWithJobId(requestId, jobId).asJson.pure
      case (requestId: RequestId, _: CorrelationId, jobId: String, job: JobUpdate) =>
        logger.warn("Deprecated input type (jobId as String)") >>
          RequestIdWithJobUpdate(requestId, JobId(jobId), job).asJson.pure // is this still in use?
      case (requestId: RequestId, _: CorrelationId, jobId: JobId, job: JobUpdate) =>
        RequestIdWithJobUpdate(requestId, jobId, job).asJson.pure
      case (requestId: RequestId, _: CorrelationId, jobId: String, state: JobStatus) =>
        RequestIdWithJobStatusUpdate(requestId, JobId(jobId), state).asJson.pure
      case (requestId: RequestId, _: CorrelationId, jobId: JobId, state: JobStatus) =>
        RequestIdWithJobStatusUpdate(requestId, jobId, state).asJson.pure
      case _ => UnknownInputType(input).raiseError
    }).widen
  } yield result

  def outputToJson[F[_]: MonadThrow, T](output: T): F[Json] = output match {
    case (location: String, requestId: RequestId)    => LocationWithRequestId(location, requestId).asJson.pure
    case (summary: JobSummary, requestId: RequestId) => JobSummaryWithRequestId(summary, requestId).asJson.pure
    case (location: String, requestId: RequestId, jobCreated: JobCreated) =>
      JobCreatedWithRequestId(location, jobCreated, requestId).asJson.pure
    case ()                               => ().asJson.pure
    case (x: StatusCode, e: ErrorMessage) => e.asJson.pure
    case e: ErrorMessage                  => e.asJson.pure
    case _                                => UnknownOutputType(output).raiseError
  }

  def extractOutput[F[_]: MonadThrow, T](output: ServerResponse[T])(implicit logger: Logger[F]): F[Json] =
    output.source
      .traverse(s => outputToJson(s.value))
      .map(_.getOrElse(Json.obj()))

  def combineJson(request: ServerRequest, principal: Json, input: Json, outcome: Json): Json =
    Json
      .obj(
        "method"      -> Method(request.method.method).asJson,
        "path"        -> Path(request.pathSegments).asJson,
        "recruiterId" -> principal,
        "input"       -> input,
        "outcome"     -> outcome
      )

  def logResult[F[_]: MonadThrow: Parallel, A, U, I, B](ctx: DecodeSuccessContext[F, A, U, I], response: ServerResponse[B])(implicit
    logger: Logger[F]
  ): F[Unit] = (for {
    json <- (extractPrincipal(ctx.principal), extractInput(ctx.input), extractOutput(response)).parMapN(combineJson(ctx.request, _, _, _))
    _ <-
      if (response.isClientError) logger.warn(json)
      else if (response.isServerError) logger.error(json)
      else logger.info(json)
  } yield ()).recoverWith(e => logger.error(e.getMessage))
}
