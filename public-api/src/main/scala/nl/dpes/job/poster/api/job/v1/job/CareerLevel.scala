package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{CareerLevel => AppCareerLevel}

case class CareerLevel private (level: String) extends AnyVal

object CareerLevel {

  implicit val encoder: Encoder[CareerLevel] = Encoder.encodeString.contramap[CareerLevel](_.level)
  implicit val decoder: Decoder[CareerLevel] = Decoder.decodeString.emap(CareerLevel(_).toEither)

  val validCareerLevels: Set[String] = Set("Geen ervaring", "Starter", "E<PERSON>ren", "Leidinggevend", "Senior management", "Directie")

  def apply(level: String): Validated[String, CareerLevel] =
    if (validCareerLevels.containsCaseInsensitive(level)) new CareerLevel(level).valid
    else s"Career level '$level' is not valid.".invalid

  def map(level: Option[CareerLevel])(implicit cursor: Cursor): Validated[MappingError, Option[AppCareerLevel]] =
    level
      .traverse(level => AppCareerLevel(level.level))
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val example = new CareerLevel("Starter")
  }
}
