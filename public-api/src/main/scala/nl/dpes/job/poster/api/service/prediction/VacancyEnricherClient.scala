package nl.dpes.job.poster.api.service.prediction

import cats.effect.kernel.MonadCancelThrow
import cats.effect.{Resource, Temporal}
import cats.syntax.all._
import io.circe.Decoder._
import io.circe.Encoder._
import io.circe.generic.auto._
import nl.dpes.job.poster.api.tracing.RequestTimer
import nl.dpes.job.poster.api.tracing.RequestTimer.ClientName.vacancyEnricher
import org.typelevel.log4cats.{Logger, LoggerFactory}
import sttp.client3._
import sttp.client3.circe._

trait VacancyEnricherClient[F[_]] {
  import VacancyEnricherClient._
  def predict(predictionRequest: Request): F[Response]
}

object VacancyEnricherClient {
  final case class Host(value: String)   extends AnyVal
  final case class ApiKey(value: String) extends AnyVal
  final case class Request(`title`: JobTitle, `text`: JobDescription, `n`: Int = 1)

  final case class Response(
    `DCO`: List[OccupationClassification],
    `branche`: List[IndustryCategory],
    `vakgebied`: List[JobCategory],
    `education-lvl`: EducationLevel,
    `career-lvl`: CareerLevel,
    `contract_type`: ContractType
  )

  def impl[F[_]: MonadCancelThrow: LoggerFactory: Temporal](
    host: Host,
    apiKey: ApiKey,
    predictionBackend: Resource[F, SttpBackend[F, Any]]
  ): VacancyEnricherClient[F] = new VacancyEnricherClient[F] {

    val logger: Logger[F] = LoggerFactory[F].getLogger

    override def predict(request: Request): F[Response] = predictionBackend.use { backend =>
      RequestTimer[F](vacancyEnricher).log {
        val predictionRequest = basicRequest
          .header("x-api-key", apiKey.value)
          .body(Request(request.`title`, request.`text`))
          .post(uri"https://${host.value}/predict")
          .response(asJson[Response])

        predictionRequest
          .send(backend)
          .attempt
          .flatMap {
            case Right(response) =>
              response.body.fold(
                err =>
                  logger.warn(s"[VacancyEnricher] Failed to parse response body: $err") *>
                  createEmptyResponse.pure[F],
                resp =>
                  logger.info(s"[VacancyEnricher] Successfully received and parsed response: $resp") *>
                  resp.pure[F]
              )
            case Left(err) =>
              logger.warn(
                s"[VacancyEnricher] Error making prediction request for title=${request.`title`} and text=${request.`text`} with error: $err"
              ) *>
                createEmptyResponse.pure[F]
          }
      }
    }

    private def createEmptyResponse: Response = Response(
      `DCO` = List.empty,
      `branche` = List.empty,
      `vakgebied` = List.empty,
      `education-lvl` = EducationLevel(""),
      `career-lvl` = CareerLevel(""),
      `contract_type` = ContractType("")
    )
  }
}
