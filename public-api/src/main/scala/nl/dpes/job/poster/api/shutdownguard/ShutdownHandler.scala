package nl.dpes.job.poster.api.shutdownguard

import cats.effect.Async

trait ShutdownHandler[F[_], R] {
  def registerShutdownHook(runtime: R, f: => Unit): F[Unit]
}

object ShutdownHandler {

  def apply[F[_], R](implicit handler: ShutdownHandler[F, R]): ShutdownHandler[F, R] = handler

  implicit def runtimeShutdownHandler[F[_]: Async]: ShutdownHandler[F, Runtime] = new ShutdownHandler[F, Runtime] {

    def registerShutdownHook(runtime: Runtime, f: => Unit): F[Unit] =
      Async[F].delay(runtime.addShutdownHook(new Thread(() => f)))
  }

  object syntax {

    implicit class ShutdownHandlerOps[F[_], R](runtime: R)(implicit handler: ShutdownHandler[F, R]) {
      def registerShutdownHook(f: => Unit): F[Unit] = handler.registerShutdownHook(runtime, f)
    }
  }
}
