package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import sttp.tapir.Schema.annotations.description
import nl.dpes.job.poster.api.service.shared.{
  Configuration => AppConfiguration,
  JobPosting => AppJobPosting,
  PerformanceBased => AppPerformanceBased,
  Feature => AppFeature,
  Budget => AppBudget,
  Site => AppSite
}

import scala.concurrent.duration.{Duration, DurationLong}

@description("Defines the job type that you want to publish: Job Posting or Performance Based.")
sealed trait Configuration

object Configuration {

  implicit lazy val encoder: Encoder[Configuration] = deriveEncoder
  implicit lazy val decoder: Decoder[Configuration] = deriveDecoder

  def map(configuration: Option[Configuration])(implicit cursor: Cursor): Validated[MappingError, Option[AppConfiguration]] =
    configuration
      .traverse {
        case JobPosting(publishOn, publicationDuration, features) =>
          (for {
            publishOn <- publishOn.map(site => AppSite(site.name)).toList.sequence.map(_.toSet).toEither
            features  <- features.map(feature => AppFeature(feature.name)).toList.sequence.map(_.toSet).toEither
          } yield AppJobPosting(publishOn, publicationDuration, features)).toValidated
        case PerformanceBased(budget) => AppBudget(budget.budget).map(AppPerformanceBased(_))
      }
      .leftMap(error => MappingError(cursor -> error))
}

@description(
  "Configuration on how the job will be represented on the job board. This configuration will determine what kind of credits need to be available to post the job."
)
case class JobPosting(
  @description(s"Represents the website on which the job will be published. All possible values are: ${Site.allowedSites.format}.")
  publishOn: Set[Site],
  @description(s"Represents the publication duration (in days).")
  publicationDuration: Duration,
  @description(s"Represents the job features. All possible values are: ${Feature.allowedFeatures.format}.")
  features: Set[Feature] = Set()
) extends Configuration

object JobPosting {
  implicit lazy val durationEncoder: Encoder[Duration] = Encoder.encodeLong.contramap(_.toDays)
  implicit lazy val durationDecoder: Decoder[Duration] = Decoder.decodeLong.map(_.days)

  implicit lazy val encoder: Encoder[JobPosting] = deriveEncoder[JobPosting]
  implicit lazy val decoder: Decoder[JobPosting] = deriveDecoder[JobPosting]
}

@description("Configuration of a Performance Based job.")
case class PerformanceBased(@description("Represents the maximum budget to be spent in Euro for the PBP job.") budget: Budget)
    extends Configuration

object PerformanceBased {
  implicit lazy val performanceBasedEncoder: Encoder[PerformanceBased] = deriveEncoder[PerformanceBased]
  implicit lazy val performanceBasedDecoder: Decoder[PerformanceBased] = deriveDecoder[PerformanceBased]
}
