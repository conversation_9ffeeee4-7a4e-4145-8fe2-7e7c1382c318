package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.syntax.validated._
import io.circe.{Decoder, Encoder}

import scala.util.matching.Regex

case class Feature private (name: String) extends AnyVal

object Feature {

  implicit lazy val encoder: Encoder[Feature] = Encoder.encodeString.contramap(_.name)
  implicit lazy val decoder: Decoder[Feature] = Decoder.decodeString.emap(Feature(_).toEither)

  val allowedFeatures: Set[String] = Set(
    "logo",
    "spotlight",
    "highlight",
    "scraped"
  )

  val boosterPattern: Regex = "booster_(\\d+)".r

  def apply(name: String): Validated[String, Feature] =
    if (allowedFeatures contains name) new Feature(name).valid
    else if (boosterPattern matches name) new Feature(name).valid
    else s"Feature '$name' is not valid.".invalid

  object SwaggerDoc {
    val logo = new Feature("logo")
  }
}
