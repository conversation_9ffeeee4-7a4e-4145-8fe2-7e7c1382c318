package nl.dpes.job.poster.api.service.shared

import io.circe.{Decoder, Encoder}
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import sttp.tapir.Schema.annotations.description

sealed trait ApplicationMethod

object ApplicationMethod {
  implicit lazy val encoder: Encoder[ApplicationMethod] = deriveEncoder
  implicit lazy val decoder: Decoder[ApplicationMethod] = deriveDecoder
}

@description("Represents the job board application method.")
final case class ApplyViaJobBoard(
  @description("Represents the recruiter first name.")
  firstName: String,
  @description("Represents the recruiter last name.")
  lastName: String,
  @description("Represents the recruiter email.")
  emailAddress: String
) extends ApplicationMethod

object ApplyViaJobBoard {
  implicit lazy val encoder: Encoder[ApplyViaJobBoard] = deriveEncoder
  implicit lazy val decoder: Decoder[ApplyViaJobBoard] = deriveDecoder
}

@description("Represents the external website application method.")
final case class ApplyViaExternalWebsite(@description("Represents the job url.") url: Website) extends ApplicationMethod

object ApplyViaExternalWebsite {
  implicit lazy val encoder: Encoder[ApplyViaExternalWebsite] = deriveEncoder[ApplyViaExternalWebsite]
  implicit lazy val decoder: Decoder[ApplyViaExternalWebsite] = deriveDecoder[ApplyViaExternalWebsite]
}

@description("Represents the easy_apply application method.")
final case object EasyApply extends ApplicationMethod {
  implicit lazy val encoder: Encoder[EasyApply.type] = deriveEncoder[EasyApply.type]
  implicit lazy val decoder: Decoder[EasyApply.type] = deriveDecoder[EasyApply.type]
}
