package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

case class Budget private (budget: Int) extends AnyVal

object Budget {
  implicit lazy val encoder: Encoder[Budget] = Encoder.encodeInt.contramap[Budget](_.budget)
  implicit lazy val decoder: Decoder[Budget] = Decoder.decodeInt.emap(Budget(_).toEither)

  def apply(budget: Int): Validated[String, Budget] =
    if (budget >= 0) new Budget(budget).valid
    else s"Budget '$budget' must not be negative.".invalid

  object SwaggerDoc {
    val example = new Budget(500)
  }
}
