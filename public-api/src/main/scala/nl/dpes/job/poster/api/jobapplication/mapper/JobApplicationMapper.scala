package nl.dpes.job.poster.api.jobapplication.mapper

import cats.effect.Async
import cats.implicits._
import nl.dpes.job.poster.api.jobapplication.v1.jobapplication.{Application, ISO_INSTANT_PATTERN, ZONE_ID}
import nl.dpes.job.poster.api.reference_id.ReferenceIdService
import nl.dpes.job.poster.api.job.v1.job.{JobId => ApiJobId}
import nl.dpes.job.poster.api.service.job.ReferenceId

import java.time.format.DateTimeFormatter
import java.time.{Instant, ZoneId, ZonedDateTime}
import scala.util.Try

object JobApplicationMapper {

  case class JobApplicationMappingError(cause: String) extends Throwable(s"Error when mapping job application, cause by: $cause.")

  def map[F[_]: Async](application: Application)(referenceIdService: ReferenceIdService[F]): F[JobApplication] = for {
    applicationDate <- Async[F].fromEither(mapTimestamp(application.createdAt))
    referenceId     <- referenceIdService.getReferenceId(ApiJobId(application.jobId))
  } yield JobApplication(
    applicationId = ApplicationId(application.id),
    recruiterId = application.recruiterId.map(RecruiterId.apply),
    jobId = JobId(application.jobId),
    referenceId = referenceId.map(id => ReferenceId(id.value)),
    applicationDate = applicationDate,
    jobSeeker = JobSeeker(
      id = application.jobSeekerId.map(JobSeekerId.apply),
      name = JobSeekerName(firstName = Name(application.jobSeekerFirstName), lastName = Name(application.jobSeekerLastName)),
      email = Email(application.jobSeekerEmail),
      phone = application.jobSeekerPhone.map(Phone.apply)
    ),
    motivation = application.motivation.map(Motivation.apply),
    cv = s"/api/v1/application/${application.id}/cv",
    site = Site(application.site)
  )

  private def mapTimestamp(createdAt: Long): Either[JobApplicationMappingError, Timestamp] =
    Try {
      new Timestamp(
        ZonedDateTime
          .ofInstant(Instant.ofEpochSecond(createdAt), ZoneId.of(ZONE_ID))
          .format(DateTimeFormatter.ofPattern(ISO_INSTANT_PATTERN))
      )
    }.toEither
      .leftMap(thr => JobApplicationMappingError(thr.getMessage))
}
