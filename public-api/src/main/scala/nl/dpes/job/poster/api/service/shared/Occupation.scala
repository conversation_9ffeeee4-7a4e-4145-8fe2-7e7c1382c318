package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

case class Occupation private (name: String) extends AnyVal

object Occupation {
  implicit lazy val encoder: Encoder[Occupation] = Encoder.encodeString.contramap[Occupation](_.name)
  implicit lazy val decoder: Decoder[Occupation] = Decoder.decodeString.emap(Occupation(_).toEither)

  def apply(name: String): Validated[String, Occupation] =
    if (name.trim.nonEmpty) new Occupation(name).valid
    else s"The 'occupation' field should not be empty.".invalid

  object SwaggerDoc {
    val example = new Occupation("Developer")
  }
}
