package nl.dpes.job.poster.api.service.shared

import io.circe.Encoder
import nl.dpes.b2b.jobmanager.domain

sealed trait JobStatus extends Product

object JobStatus {
  case object ValidDraft     extends JobStatus
  case object Expired        extends JobStatus
  case object MultiStepDraft extends JobStatus
  case object Draft          extends JobStatus
  case object Published      extends JobStatus
  case object Suspended      extends JobStatus
  case object Rejected       extends JobStatus
  case object Approved       extends JobStatus
  case object Submitted      extends JobStatus
  case object Deleted        extends JobStatus

  def fromString(status: String): JobStatus = status.toLowerCase match {
    case "validdraft"     => ValidDraft
    case "expired"        => Expired
    case "multistepdraft" => MultiStepDraft
    case "draft"          => Draft
    case "published"      => Published
    case "suspended"      => Suspended
    case "rejected"       => Rejected
    case "approved"       => Approved
    case "submitted"      => Submitted
    case "deleted"        => Deleted
  }
  def fromJobManager(status: domain.Status): JobStatus = fromString(status.toString)

  implicit val jobStatusEncoder: Encoder[JobStatus] = Encoder.encodeString.contramap(_.toString)
}
