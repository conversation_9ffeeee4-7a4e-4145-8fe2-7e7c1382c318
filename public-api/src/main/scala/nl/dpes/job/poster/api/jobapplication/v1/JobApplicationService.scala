package nl.dpes.job.poster.api.jobapplication.v1

import cats.MonadThrow
import cats.data.Validated
import cats.effect.Async
import cats.implicits.{catsSyntaxApplicativeId, catsSyntaxEitherId, catsSyntaxTuple2Semigroupal, toFunctorOps}
import cats.syntax.applicativeError._
import cats.syntax.flatMap._
import cats.syntax.traverse._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.jobapplication.mapper.{JobApplication, JobApplicationMapper}
import nl.dpes.job.poster.api.jobapplication.v1.JobApplicationClient._
import nl.dpes.job.poster.api.jobapplication.v1.cv.ApplicationId
import nl.dpes.job.poster.api.jobapplication.v1.jobapplication._
import nl.dpes.job.poster.api.reference_id.ReferenceIdService
import nl.dpes.job.poster.api.{BadRequest, Conflict, ErrorMessage, NotFound, ServiceUnavailable, Unknown}
import nl.dpes.job.poster.api.tracing.CorrelationId
import org.slf4j.{<PERSON><PERSON>, LoggerFactory}
import sttp.model.{<PERSON><PERSON>, HeaderNames}
import sttp.tapir.header

import java.io.{ByteArrayInputStream, InputStream}
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatter.ISO_ZONED_DATE_TIME
import java.time.{ZoneId, ZonedDateTime}
import java.util.{Date => JavaDate}
import scala.util.Try

trait JobApplicationService[F[_]] {

  def getJobApplications(credentials: SalesForceId)(
    data: (CorrelationId, RawDate, Option[RawDate])
  ): F[Either[ErrorMessage, Seq[JobApplication]]]

  def downloadCV(credentials: SalesForceId)(
    data: (CorrelationId, ApplicationId)
  ): F[Either[ErrorMessage, (List[Header], InputStream)]]
}
case class SearchDateTimeRangeError(cause: String) extends Throwable(s"Error when computing the search datetime range, cause by: $cause")

case class RecruiterNotFound(applicationId: String)
    extends Throwable(s"Job Application Recruiter is missing for application '$applicationId'.")

case class RecruiterNotOwnerOfApplication(recruiterId: String, applicationId: String)
    extends Throwable(s"Application '$applicationId' does not belong to recruiter '$recruiterId'.")

object JobApplicationService {

  lazy val logger: Logger = LoggerFactory.getLogger("JobApplicationService")

  def apply[F[_]: Async](client: JobApplicationClient[F], referenceIdService: ReferenceIdService[F]): JobApplicationService[F] =
    new JobApplicationService[F] {

      override def getJobApplications(credentials: SalesForceId)(
        data: (CorrelationId, RawDate, Option[RawDate])
      ): F[Either[ErrorMessage, Seq[JobApplication]]] =
        (for {
          searchRange           <- createSearchDateTimeRange(data._2, data._3)
          rawJobApplications    <- client.getJobApplications(credentials, searchRange)
          mappedJobApplications <- rawJobApplications.traverse(app => JobApplicationMapper.map(app)(referenceIdService))
        } yield mappedJobApplications.asRight[ErrorMessage]).recover { case SearchDateTimeRangeError(thr) =>
          BadRequest(thr).asLeft
        }

      override def downloadCV(
        credentials: SalesForceId
      )(data: (CorrelationId, ApplicationId)): F[Either[ErrorMessage, (List[Header], InputStream)]] =
        (for {
          application <- client.getJobApplication(data._2)
          file        <- downloadFile(credentials, application)
        } yield file.asRight[ErrorMessage]) recover {
          case ex: CVNotFound =>
            logger.warn(ex.getMessage)
            NotFound(ex.getMessage).asLeft
          case ex: RecruiterNotFound =>
            logger.warn(ex.getMessage)
            NotFound(ex.getMessage).asLeft
          case ex: ApplicationNotFound =>
            logger.warn(ex.getMessage)
            NotFound(ex.getMessage).asLeft
          case ex: RecruiterNotOwnerOfApplication =>
            logger.error(ex.getMessage)
            Conflict(ex.getMessage).asLeft
          case ex: JobApplicationClientError =>
            logger.error(ex.getMessage)
            Unknown(ex.getMessage).asLeft
          case ex: CVDownloadError =>
            logger.error(ex.getMessage)
            Unknown(ex.getMessage).asLeft
          case ex: ServiceNotAvailable =>
            ServiceUnavailable(ex.getMessage).asLeft
        }

      private def downloadFile(recruiterId: SalesForceId, application: Application): F[(List[Header], InputStream)] = for {
        recruiter <- MonadThrow[F].fromOption(application.recruiterId, RecruiterNotFound(application.id))
        file      <- streamFile(recruiterId, application, recruiter)
      } yield file

      private def streamFile(
        recruiterId: SalesForceId,
        application: Application,
        recruiter: String
      ): F[(List[Header], ByteArrayInputStream)] =
        if (recruiter == recruiterId.idWithChecksum) {
          for {
            content <- client.downloadFile(application.url)
            file    <- MonadThrow[F].fromTry(Try(new ByteArrayInputStream(content)))
            header  <- List(header(HeaderNames.ContentDisposition, s"attachment; filename=${application.filename}").h).pure[F]
          } yield (header, file)
        } else throw RecruiterNotOwnerOfApplication(recruiterId.idWithChecksum, application.id)
    }

  private def createSearchDateTimeRange[F[_]: MonadThrow](startDate: RawDate, endDate: Option[RawDate]): F[SearchDateTimeRange] =
    MonadThrow[F].fromValidated(
      (StartDate(startDate), computeEndDate(endDate)).tupled
        .andThen(input => SearchDateTimeRange.apply(input._1, input._2))
        .leftMap { thr =>
          val error = SearchDateTimeRangeError(thr)
          logger.error(error.getMessage)
          error
        }
    )

  private def computeEndDate[F[_]: MonadThrow](endDate: Option[RawDate]): Validated[String, EndDate] =
    endDate.map(EndDate(_)) getOrElse currentDate

  private def currentDate: Validated[String, EndDate] = Validated
    .fromTry(Try {
      val now = ZonedDateTime
        .ofInstant(new JavaDate().toInstant, ZoneId.of(ZONE_ID))
        .format(DateTimeFormatter.ofPattern(ISO_INSTANT_PATTERN))
      ZonedDateTime.parse(now, ISO_ZONED_DATE_TIME)
      EndDate.apply(now)
    })
    .leftMap(_.getMessage)
}
