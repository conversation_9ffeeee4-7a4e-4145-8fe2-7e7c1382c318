package nl.dpes.job.poster.api.jobapplication.v1

import cats.effect.Resource
import cats.effect.kernel.Async
import cats.implicits.{catsSyntaxApplicativeErrorId, catsSyntaxApplicativeId, toFlatMapOps}
import nl.dpes.job.poster.api.jobapplication.v1.jobapplication.{Application, SearchDateTimeRange}
import sttp.client3._
import sttp.client3.circe._
import io.circe.generic.auto._
import cats.syntax.functor._
import cats.syntax.monadError._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.jobapplication.v1.cv.ApplicationId
import org.slf4j.{Logger, LoggerFactory}
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend

import java.net.{HttpURLConnection, URL, UnknownHostException}
import scala.util.{Failure, Success, Try}

trait JobApplicationClient[F[_]] {
  def getJobApplications(recruiterId: SalesForceId, searchDateTimeRange: SearchDateTimeRange): F[Seq[Application]]
  def getJobApplication(applicationId: ApplicationId): F[Application]
  def downloadFile(url: String): F[Array[Byte]]
}

object JobApplicationClient {

  lazy val logger: Logger = LoggerFactory.getLogger("JobApplicationClient")

  case class JobApplicationClientError(cause: Throwable)
      extends Throwable(s"Error occurred when invoking the job application client, caused by: ${cause.getMessage}")
  case class CVDownloadError(cause: String) extends Throwable(s"Error occurred when downloading CV, caused by: $cause")
  case class CVNotFound(url: String)        extends Throwable(s"The CV '$url' was not found.")

  case class ApplicationNotFound(applicationId: ApplicationId)
      extends Throwable(s"Application '${applicationId.applicationId}' was not found.")

  case class ServiceNotAvailable(url: String) extends Throwable(s"The application service is not available.")

  def impl[F[_]: Async](host: String, apiKey: String, jobApplicationBackend: Resource[F, SttpBackend[F, Any]]): JobApplicationClient[F] =
    new JobApplicationClient[F] {

      override def getJobApplications(recruiterId: SalesForceId, searchDateTimeRange: SearchDateTimeRange): F[Seq[Application]] =
        jobApplicationBackend.use { backend =>
          basicRequest
            .get(
              uri"https://$host/apply/applications?recruiter-id=${recruiterId.idWithChecksum}&from=${searchDateTimeRange.start.value}&to=${searchDateTimeRange.end.value}"
            )
            .response(asJson[Seq[Application]])
            .send(backend)
            .map(_.body)
            .adaptError { case ex =>
              JobApplicationClientError(ex)
            }
            .rethrow
        }

      override def getJobApplication(applicationId: ApplicationId): F[Application] = {
        val applyServiceUrl = s"https://$host/apply/application/${applicationId.applicationId}"
        for {
          _ <- isAvailableUrl(apiKey, applyServiceUrl)
          application <-
            jobApplicationBackend.use { backend =>
              basicRequest
                .get(uri"$applyServiceUrl")
                .response(asJson[Application])
                .send(backend)
                .map(_.body)
                .flatMap {
                  case Right(value) => value.pure
                  case Left(thr) =>
                    thr match {
                      case HttpError(_, statusCode) if statusCode.code == 404 => throw ApplicationNotFound(applicationId)
                      case ex @ _                                             => throw JobApplicationClientError(ex)
                    }
                }
            }
        } yield application
      }

      override def downloadFile(url: String): F[Array[Byte]] =
        for {
          availability <- isAvailableUrl(apiKey, url)
          res <-
            if (availability) getCVAsBytes(url, jobApplicationBackend)
            else throw CVNotFound(url)
        } yield res

      private def getCVAsBytes(url: String, jobApplicationBackend: Resource[F, SttpBackend[F, Any]]): F[Array[Byte]] =
        jobApplicationBackend.use { backend =>
          basicRequest
            .get(uri"$url")
            .header("x-api-key", apiKey)
            .response(asByteArray)
            .send(backend)
            .map(_.body)
            .flatMap {
              case Right(value) => value.pure
              case Left(thr)    => CVDownloadError(thr).raiseError
            }
        }

      private def isAvailableUrl(apiKey: String, url: String): F[Boolean] =
        Try {
          val cxn = new URL(url).openConnection()
          cxn.setRequestProperty("x-api-key", apiKey)
          val httpCode = cxn.asInstanceOf[HttpURLConnection].getResponseCode
          httpCode >= 200 && httpCode <= 206
        } match {
          case Success(value) => value.pure
          case Failure(exception) if exception.isInstanceOf[UnknownHostException] =>
            logger.error(s"The application service is not available. We cannot reach '$url'.")
            ServiceNotAvailable(url).raiseError
          case Failure(exception) => exception.raiseError
        }
    }

  def jobApplicationBackend[F[_]: Async]: Resource[F, SttpBackend[F, Any]] =
    AsyncHttpClientFs2Backend.resource[F]()
}
