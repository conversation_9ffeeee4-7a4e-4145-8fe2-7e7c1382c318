package nl.dpes.job.poster.api

import cats.Parallel
import cats.effect._
import cats.syntax.functor._
import cats.syntax.semigroupk._
import io.getquill.{SnakeCase, SqliteJdbcContext}
import nl.dpes.job.poster.api.config.ConfigLoader.ConfigSourceOps
import nl.dpes.job.poster.api.config.{AppConfig, Configuration}
import nl.dpes.job.poster.api.database.{Database, TableName}
import nl.dpes.job.poster.api.reference_id.{ReferenceIdRepository, ReferenceIdService}
import nl.dpes.job.poster.api.service.apikey.{ApiKeyRepository, Salt}
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import nl.dpes.job.poster.api.shutdownguard.ShutdownGuard
import nl.dpes.job.poster.api.status.StatusController
import org.http4s.HttpRoutes
import org.http4s.Method._
import org.http4s.blaze.server.BlazeServerBuilder
import org.http4s.server.Router
import org.http4s.server.middleware._
import org.typelevel.log4cats.slf4j.loggerFactoryforSync
import org.typelevel.log4cats.{Logger, LoggerFactory}
import pureconfig.ConfigSource
import sttp.tapir.server.ServerEndpoint
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter

import scala.concurrent.duration.DurationInt

object Main extends IOApp with Configuration {
  implicit lazy val context: SqliteJdbcContext[SnakeCase.type] = new SqliteJdbcContext(SnakeCase, "quillconfig")

  val referenceIdRepo: Resource[IO, ReferenceIdRepository[IO]] = for {
    appConfig       <- Resource.eval(ConfigSource.default.loadConfig[IO, AppConfig])
    transactor      <- Database.asResource[IO](appConfig.database)
    referenceIdRepo <- ReferenceIdRepository[IO](TableName("reference_id"), transactor)
  } yield referenceIdRepo

  val corsConfig: CORSConfig = CORSConfig.default
    .withAnyOrigin(false)
    .withAllowedOrigins(
      Set(
        "https://www.persgroepemploymentsolutions.nl",  // production
        "https://job-poster-api.persgroep.digital",     // production
        "https://b2b-acc.persgroep.digital",            // acceptance
        "https://job-poster-api-acc.persgroep.digital", // acceptance
        "https://b2b-dev.persgroep.digital",            // development
        "http://127.0.0.1:11360",                       // development2
        "http://localhost:11360"                        // development2
      )
    )
    .withAllowCredentials(false)
    .withAnyMethod(false)
    .withAllowedMethods(Some(Set(GET, PUT, POST, DELETE)))

  def run(args: List[String]): IO[ExitCode] = IO.println("----START----") >> referenceIdRepo.use(repo =>
    ShutdownGuard[IO, Runtime](Runtime.getRuntime).use(guard =>
      for {
        authService <- AuthenticationService.impl(
          ApiKeyRepository.impl[IO](service.apikey.ApiKeyHasher(Salt(ApiKeyHasher.salt)))
        )
        jobHelper                 <- job.v1.initializer[IO](authService, repo, guard)
        jobRoutes                 <- jobHelper.routes
        jobEndpoints              <- jobHelper.endpoints
        cpcInitializer            <- cpc_registrations.initializer[IO](authService, ReferenceIdService.impl(repo))
        cpcRoutes                 <- cpcInitializer.routes
        cpcEndpoints              <- cpcInitializer.endpoints
        jobApplicationInitializer <- jobapplication.v1.initializer[IO](authService, repo)
        jobApplicationEndpoints   <- jobApplicationInitializer.endpoints
        jobApplicationRoutes      <- jobApplicationInitializer.routes
        jobPerformanceInitializer <- jobperformance.v1.initializer[IO](authService, repo)
        jobPerformanceEndpoints   <- jobPerformanceInitializer.endpoints
        jobPerformanceRoutes      <- jobPerformanceInitializer.routes
        combinedDocs              <- routes[IO](jobEndpoints ::: jobApplicationEndpoints ::: jobPerformanceEndpoints ::: cpcEndpoints)
        serviceRoutes             <- service.routes[IO]

        statusRoute <- StatusController.impl[IO].routes
        httpApp <- IO(
          Router(
            Http.basePath -> (RequestId.httpRoutes(jobRoutes <+> jobApplicationRoutes <+> jobPerformanceRoutes <+> cpcRoutes) <+> CORS(
              serviceRoutes,
              corsConfig
            ) <+> statusRoute <+> combinedDocs)
          ).orNotFound
        )
        _ <- IO.println("=====Starting API=====")
        _ <- BlazeServerBuilder[IO]
          .bindHttp(Http.port, Http.host)
          .withHttpApp(httpApp)
          .withResponseHeaderTimeout(65.seconds)
          .withIdleTimeout(70.seconds)
          .withoutBanner
          .resource
          .use(_ => guard.awaitCompletion)
      } yield ExitCode.Success
    )
  )

  def routes[F[_]: Async: Parallel](endpoints: List[ServerEndpoint[Any, F]]): F[HttpRoutes[F]] = {
    val v = "v1"
    for {
      swaggerRoutes <- Sync[F].delay(
        Http4sServerInterpreter[F](serverOptions)
          .toRoutes(
            SwaggerInterpreter(swaggerUIOptions =
              SwaggerUIOptions(
                List("docs", v),
                "docs.yaml",
                Nil,
                useRelativePaths = false,
                showExtensions = false,
                initializerOptions = None,
                oAuthInitOptions = None
              )
            )
              .fromServerEndpoints[F](endpoints, "Job Poster", v)
          )
      )
    } yield swaggerRoutes
  }
}
