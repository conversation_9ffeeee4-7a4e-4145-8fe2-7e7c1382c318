package nl.dpes.job.poster.api
package job.v1

import cats.Parallel
import cats.effect.Sync
import cats.effect.kernel.Async
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.semigroupk._
import cats.syntax.traverse._
import io.circe.{Decoder, Encoder}
import io.circe.generic.auto._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.job.v1.job.Job.SwaggerDoc.{jobExample, jobObjectDescription}
import nl.dpes.job.poster.api.job.v1.job.JobStatus.SwaggerDoc
import nl.dpes.job.poster.api.job.v1.job.{Job, JobCreated, JobId, JobStatus, JobSummary, JobUpdate}
import nl.dpes.job.poster.api._
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import nl.dpes.job.poster.api.tracing.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DecodeSuccessLogger, RequestId}
import org.http4s.HttpRoutes
import org.typelevel.log4cats.slf4j.Slf4jFactory
import sttp.model.{<PERSON>er<PERSON><PERSON>s, StatusCode}
import sttp.tapir._
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe.jsonBody
import sttp.tapir.server.ServerEndpoint
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter

trait Controller[F[_]] {
  def create: F[ServerEndpoint[Any, F]]
  def read: F[ServerEndpoint[Any, F]]
  def update: F[ServerEndpoint[Any, F]]
  def delete: F[ServerEndpoint[Any, F]]
  def updateJobStatus: F[ServerEndpoint[Any, F]]
  def routes: F[HttpRoutes[F]]
  def endpoints: F[List[ServerEndpoint[Any, F]]]
}

object Controller {

  implicit lazy val encoder: Encoder[SalesForceId] = Encoder.encodeString.contramap[SalesForceId](_.idWithChecksum)
  implicit lazy val decoder: Decoder[SalesForceId] = Decoder.decodeString.emap(SalesForceId(_).left.map(_.toString))

  private val jobIdPathSegment: EndpointInput.PathCapture[JobId] = path[JobId]("id").description("This id can be a job id or reference id.")

  def impl[F[_]: Async: Parallel](authenticationService: AuthenticationService[F], service: ControllerService[F]): Controller[F] =
    new Controller[F] {

      val requestIdHeader: EndpointIO.Header[RequestId] =
        header[RequestId]("X-Request-ID").description("Header containing unique id for this request")

      override def create: F[ServerEndpoint[Any, F]] =
        Sync[F].delay {
          jobEndpoint
            .baseEndpoint(
              authenticationService.authenticate,
              _.errorOut(
                oneOf(
                  oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest])),
                  oneOfVariant[StatusConflict](statusCode(StatusCode.Conflict).and(jsonBody[StatusConflict])),
                  oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
                  oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden])),
                  oneOfVariant[Timeout](statusCode(StatusCode.GatewayTimeout).and(jsonBody[Timeout])),
                  oneOfVariant[Unknown](statusCode(StatusCode.InternalServerError).and(jsonBody[Unknown])),
                  oneOfVariant[Teapot](statusCode(StatusCode.unsafeApply(418)).and(jsonBody[Teapot]))
                )
              )
            )
            .post
            .description(
              "Creates a job. Note that credits will be consumed immediately and the job will be posted on job boards."
            )
            .in("jobs")
            .in(jsonBody[Job].description(jobObjectDescription).example(jobExample))
            .out(statusCode(StatusCode.Created))
            .out(header[String](HeaderNames.Location))
            .out(requestIdHeader)
            .out(jsonBody[JobCreated])
            .serverLogic(service.create)
        }

      override def read: F[ServerEndpoint[Any, F]] = Sync[F].delay {
        jobEndpoint
          .baseEndpoint(
            authenticationService.authenticate,
            _.errorOut(
              oneOf(
                oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest])),
                oneOfVariant[NotFound](statusCode(StatusCode.NotFound).and(jsonBody[NotFound])),
                oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
                oneOfVariant[Unknown](statusCode(StatusCode.InternalServerError).and(jsonBody[Unknown])),
                oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden]))
              )
            )
          )
          .get
          .description("Returns a summary of the requested job")
          .in("job" / jobIdPathSegment / "summary")
          .out(statusCode(StatusCode.Ok))
          .out(jsonBody[JobSummary])
          .out(requestIdHeader)
          .serverLogic(service.read)
      }

      override def update: F[ServerEndpoint[Any, F]] = Sync[F].delay {
        jobEndpoint
          .baseEndpoint(
            authenticationService.authenticate,
            _.errorOut(
              oneOf(
                oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest])),
                oneOfVariant[NotFound](statusCode(StatusCode.NotFound).and(jsonBody[NotFound])),
                oneOfVariant[StatusConflict](statusCode(StatusCode.Conflict).and(jsonBody[StatusConflict])),
                oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
                oneOfVariant[Unknown](statusCode(StatusCode.InternalServerError).and(jsonBody[Unknown])),
                oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden]))
              )
            )
          )
          .put
          .description("Updates a job")
          .in("job" / jobIdPathSegment)
          .in(jsonBody[JobUpdate].description(jobObjectDescription).example(JobUpdate.SwaggerDoc.example))
          .out(statusCode(StatusCode.Accepted))
          .serverLogic(service.update)
      }

      override def delete: F[ServerEndpoint[Any, F]] = Sync[F].delay {
        jobEndpoint
          .baseEndpoint(
            authenticationService.authenticate,
            _.errorOut(
              oneOf(
                oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
                oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden])),
                oneOfVariant[NotFound](statusCode(StatusCode.NotFound).and(jsonBody[NotFound])),
                oneOfVariant[Unknown](statusCode(StatusCode.InternalServerError).and(jsonBody[Unknown])),
                oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
              )
            )
          )
          .delete
          .description("Deletes a job. The job will not be available any more.")
          .in("job" / jobIdPathSegment)
          .out(statusCode(StatusCode.Accepted))
          .serverLogic(service.delete)
      }

      override def updateJobStatus: F[ServerEndpoint[Any, F]] = Sync[F].delay {
        jobEndpoint
          .baseEndpoint(
            authenticationService.authenticate,
            _.errorOut(
              oneOf(
                oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest])),
                oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
                oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden])),
                oneOfVariant[NotFound](statusCode(StatusCode.NotFound).and(jsonBody[NotFound])),
                oneOfVariant[Unknown](statusCode(StatusCode.InternalServerError).and(jsonBody[Unknown])),
                oneOfVariant[StatusConflict](statusCode(StatusCode.Conflict).and(jsonBody[StatusConflict]))
              )
            )
          )
          .put
          .description("Updates the status of a job: Suspended or Resumed.")
          .in("job" / jobIdPathSegment / "status")
          .in(jsonBody[JobStatus].description(SwaggerDoc.jobStatusDescription).example(SwaggerDoc.example))
          .out(statusCode(StatusCode.Accepted))
          .serverLogic(service.updateJobStatus)
      }

      override def routes: F[HttpRoutes[F]] = for {
        usageLogger <- Slf4jFactory[F].fromName("JobController UsageLogger")
        endpoints   <- endpoints
        swaggerRoutes <- Sync[F].delay(
          Http4sServerInterpreter[F]()
            .toRoutes(
              SwaggerInterpreter(swaggerUIOptions =
                SwaggerUIOptions(
                  List("docs", "jobs", jobEndpoint.version),
                  "docs.yaml",
                  Nil,
                  useRelativePaths = false,
                  showExtensions = false,
                  initializerOptions = None,
                  oAuthInitOptions = None
                )
              )
                .fromServerEndpoints[F](endpoints, "Job poster API", jobEndpoint.version)
            )
        )
        endpointRoutes <- Sync[F].delay(
          Http4sServerInterpreter[F](
            serverOptions
              .prependInterceptor(DecodeFailureLogger(authenticationService, usageLogger))
              .prependInterceptor(DecodeSuccessLogger(usageLogger))
          )
            .toRoutes(endpoints)
        )
      } yield swaggerRoutes <+> endpointRoutes

      override def endpoints: F[List[ServerEndpoint[Any, F]]] = List(create, read, update, delete, updateJobStatus).sequence
    }
}
