package nl.dpes.job.poster.api.jobapplication.mapper

import cats.implicits.catsSyntaxEitherId
import io.circe.generic.semiauto.deriveEncoder
import io.circe.{Decoder, Encoder, HCursor}

case class JobSeeker(id: Option[JobSeekerId], name: Job<PERSON>eeker<PERSON><PERSON>, email: Email, phone: Option[Phone])

object JobSeeker {
  implicit lazy val encoder: Encoder[JobSeeker] = deriveEncoder

  implicit lazy val decoder: Decoder[JobSeeker] = (c: HCursor) =>
    for {
      id        <- c.downField("id").as[Option[JobSeekerId]]
      name      <- c.downField("name").as[JobSeekerName]
      email     <- c.downField("email").as[Email]
      phone     <- c.downField("phone").as[Option[Phone]]
      jobSeeker <- JobSeeker(id, name, email, phone).asRight
    } yield jobSeeker

}
