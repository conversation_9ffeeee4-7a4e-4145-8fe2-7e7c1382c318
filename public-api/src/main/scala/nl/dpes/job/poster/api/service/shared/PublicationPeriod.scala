package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.syntax._
import io.circe._
import nl.dpes.b2b.jobmanager.domain

import java.time.LocalDate
import scala.concurrent.duration._

case class PublicationPeriod private (
  start: Date,
  end: Option[Date]
) {
  def endForDuration(daysOnline: Duration): Date = end.getOrElse(start.plus(daysOnline + 1.day))
}

object PublicationPeriod {

  implicit lazy val encoder: Encoder[PublicationPeriod] = (period: PublicationPeriod) =>
    Json.obj(
      "start" -> period.start.asJson,
      "end"   -> period.end.asJson
    )

  implicit lazy val decoder: Decoder[PublicationPeriod] = (c: HCursor) =>
    for {
      start  <- c.downField("start").as[Date]
      end    <- c.downField("end").as[Option[Date]]
      period <- PublicationPeriod(start, end).toEither.leftMap(e => DecodingFailure(e, c.history))
    } yield period

  def apply(start: Date, end: Option[Date] = None): Validated[String, PublicationPeriod] = end.map { end =>
    if (end.value < start.value) s"End date '$end' cannot be before start date '$start'.".invalid
    else new PublicationPeriod(start, Some(end)).valid
  } getOrElse new PublicationPeriod(start, end).valid[String]

  def fromJobManager(period: domain.Range[LocalDate]): Validated[String, PublicationPeriod] =
    (Date(period.lower.toString), Date(period.upper.toString)).tupled.andThen { case (from, to) =>
      PublicationPeriod.apply(from, Some(to))
    }
}
