package nl.dpes.job.poster.api.service.shared

import cats.implicits._
import io.circe.{Decoder, Encoder}
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import sttp.tapir.Schema.annotations.description

case class Company(
  @description("The name of the company")
  name: String,
  @description("The location of the company itself, does not need to be the same as the location of the job.")
  address: Option[Address] = None,
  @description(s"The type of company, allowed values: ${CompanyType.validCompanyTypes.mkString(", ")}")
  companyType: CompanyType,
  @description("Url for more information")
  website: Option[Website] = None
)

object Company {

  implicit lazy val encoder: Encoder[Company] = deriveEncoder[Company]
  implicit lazy val decoder: Decoder[Company] = deriveDecoder[Company]

  object SwaggerDoc {

    val example = new Company(
      "ACME corp",
      PostalAddress("mt Lincolnweg 40", "Amsterdam", "1033SN").some,
      CompanyType.SwaggerDoc.example,
      Website.SwaggerDoc.example.some
    )
  }
}
