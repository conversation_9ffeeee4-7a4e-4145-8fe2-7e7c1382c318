package nl.dpes.job.poster.api.service.cache

import cats.effect.{Concurrent, Temporal}
import cats.syntax.functor._

trait CacheFactory[F[_]] {
  def create[A, B](f: A => F[B]): F[A => F[B]]
}

object CacheFactory {

  implicit def apply[F[_]: Concurrent: Temporal]: CacheFactory[F] = new CacheFactory[F] {

    override def create[A, B](f: A => F[B]): F[A => F[B]] = Cache[A, B, F](f).map { cache =>
      cache.apply
    }
  }
}
