package nl.dpes.job.poster.api.jobapplication.mapper

import cats.implicits.catsSyntaxEitherId
import io.circe.{Decoder, Encoder}

case class Motivation(motivation: String) extends AnyVal

object Motivation {
  implicit lazy val encoder: Encoder[Motivation] = Encoder.encodeString.contramap(_.motivation)
  implicit lazy val decoder: Decoder[Motivation] = Decoder.decodeString.emap(Motivation(_).asRight)
}
