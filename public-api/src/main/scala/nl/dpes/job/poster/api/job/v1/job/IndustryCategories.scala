package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{IndustryCategories => AppIndustryCategories, IndustryCategory => AppIndustryCategory}

case class IndustryCategories private (industryCategories: Set[IndustryCategory]) extends AnyVal

object IndustryCategories {

  implicit lazy val encoder: Encoder[IndustryCategories] = Encoder.encodeSet[IndustryCategory].contramap(_.industryCategories)
  implicit lazy val decoder: Decoder[IndustryCategories] = Decoder.decodeSet[IndustryCategory].emap(IndustryCategories(_).toEither)

  val minimumAmountOfCategories = 1
  val maximumAmountOfCategories = 2

  def checkMinimumAmount(categories: IndustryCategories): Validated[String, IndustryCategories] =
    Validated.cond(
      categories.industryCategories.size >= minimumAmountOfCategories,
      categories,
      s"At least $minimumAmountOfCategories categories should be chosen"
    )

  def checkMaximumAmount(categories: IndustryCategories): Validated[String, IndustryCategories] =
    Validated.cond(
      categories.industryCategories.size <= maximumAmountOfCategories,
      categories,
      s"At most $maximumAmountOfCategories categories should be chosen"
    )

  def apply(items: Set[IndustryCategory]): Validated[String, IndustryCategories] =
    new IndustryCategories(items).valid andThen checkMinimumAmount andThen checkMaximumAmount

  def map(
    industryCategories: Option[IndustryCategories]
  )(implicit cursor: Cursor): Validated[MappingError, Option[AppIndustryCategories]] =
    (for {
      categories    <- industryCategories.map(industries => industries.industryCategories).map(_.toList)
      appCategories <- categories.traverse(category => AppIndustryCategory(category.category).toOption).map(_.toSet)
    } yield AppIndustryCategories(appCategories)).sequence
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val example = new IndustryCategories(Set(IndustryCategory.SwaggerDoc.telecom, IndustryCategory.SwaggerDoc.techniek))
  }
}
