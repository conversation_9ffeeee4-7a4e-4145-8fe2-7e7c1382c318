package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import org.jsoup.Jsoup
import org.jsoup.safety.Safelist
import sttp.tapir.Schema.annotations.description
import nl.dpes.job.poster.api.service.shared.{Html => AppHtml}
import org.jsoup.nodes.Element

import scala.util.Try

@description(s"HTML document, which will be stripped of all tags, except for ${Html.allowedTags.mkString(", ")}.")
case class Html private (document: String) extends AnyVal

object Html {

  implicit lazy val encoder: Encoder[Html] = Encoder.encodeString.contramap(_.document)
  implicit lazy val decoder: Decoder[Html] = Decoder.decodeString.emap(Html(_).toEither)

  val allowedTags: Seq[String]   = Seq("p", "b", "i", "u", "ol", "ul", "li")
  private val safeList: Safelist = Safelist.none().addTags(allowedTags: _*)

  def apply(document: String): Validated[String, Html] = Validated
    .fromTry(Try {
      val invisibleRemoved = document.replaceAll("[\uFEFF]", "<p>")
      val brReplaced       = invisibleRemoved.replaceAll("<br[^>]*>", "<p>")
      val h2Replaced       = brReplaced.replaceAll("<h2[^>]*>", "<p><b>").replaceAll("</h2>", "</b></p><p>")
      val wrappedDocument  = s"<p>$h2Replaced</p>"
      val cleanedDocument  = Jsoup.clean(wrappedDocument, safeList)

      val parsedDocument = Jsoup.parse(cleanedDocument)
      parsedDocument.getAllElements.forEach(element => if (!element.hasText) element.remove())
      new Html(Option(parsedDocument.body().html()) getOrElse "")
    })
    .leftMap(_.getMessage)

  def map(description: Option[Html])(implicit cursor: Cursor): Validated[MappingError, Option[AppHtml]] =
    description
      .traverse(html => AppHtml(html.document))
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val example = new Html("<p>The description</p><p>To describe the duties of the job.</p>")
  }
}
