package nl.dpes.job.poster.api
package job.v1.job

import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import sttp.tapir.Schema.annotations.description

sealed trait Address

object Address {
  implicit lazy val encoder: Encoder[Address] = deriveEncoder[Address]
  implicit lazy val decoder: Decoder[Address] = deriveDecoder[Address]
}

final case class PostalAddress(
  streetNameAndHouseNumber: String,
  city: String,
  zipCode: String
) extends Address

object PostalAddress {

  def mapToService(address: PostalAddress): service.shared.PostalAddress =
    service.shared.PostalAddress(address.streetNameAndHouseNumber, address.city, address.zipCode)

  implicit lazy val encoder: Encoder[PostalAddress] = deriveEncoder[PostalAddress]
  implicit lazy val decoder: Decoder[PostalAddress] = deriveDecoder[PostalAddress]
}

@description("Deprecated, only used for compability with legacy system. Use PostalAddress instead")
final case class DomesticAddress(
  street: String,
  houseNumber: String,
  houseNumberAddition: Option[String] = None,
  zipCode: String,
  city: String
) extends Address

object DomesticAddress {

  implicit lazy val encoder: Encoder[DomesticAddress] = deriveEncoder[DomesticAddress]
  implicit lazy val decoder: Decoder[DomesticAddress] = deriveDecoder[DomesticAddress]
}
