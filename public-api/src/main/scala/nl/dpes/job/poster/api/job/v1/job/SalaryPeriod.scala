package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.syntax.validated._
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.shared.{SalaryPeriod => AppSalaryPeriod}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import sttp.tapir.Schema.annotations.description

@description(s"Period for the salary, can be ${SalaryPeriod.all.values.map(_.value.toLowerCase).mkString(", ")}.")
final case class SalaryPeriod private (value: String) extends AnyVal

object SalaryPeriod {
  val Hour  = new SalaryPeriod("Hour")
  val Month = new SalaryPeriod("Month")
  val Year  = new SalaryPeriod("Year")

  val all: Map[String, SalaryPeriod] = Map(
    "hour"  -> Hour,
    "month" -> Month,
    "year"  -> Year
  )

  def apply(period: String): Validated[String, SalaryPeriod] =
    Validated.fromOption(all.get(period.toLowerCase), s"Cannot get period from '$period'")

  implicit lazy val jsonEncoder: Encoder[SalaryPeriod] = Encoder.encodeString.contramap(_.value)
  implicit lazy val jsonDecoder: Decoder[SalaryPeriod] = Decoder.decodeString.emap(SalaryPeriod.apply(_).toEither)

  def map(salaryPeriod: Option[SalaryPeriod])(implicit cursor: Cursor): Validated[MappingError, AppSalaryPeriod] =
    salaryPeriod.map(map).getOrElse(AppSalaryPeriod.Unspecified.valid)

  def map(salaryPeriod: SalaryPeriod)(implicit cursor: Cursor): Validated[MappingError, AppSalaryPeriod] =
    AppSalaryPeriod(salaryPeriod.value).leftMap(error => MappingError(cursor -> error))
}
