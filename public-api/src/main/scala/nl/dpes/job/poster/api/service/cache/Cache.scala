package nl.dpes.job.poster.api.service.cache

import cats.effect.syntax.spawn._
import cats.effect.{Concurrent, Deferred, Ref, Temporal}
import cats.syntax.applicative._
import cats.syntax.applicativeError._
import cats.syntax.flatMap._
import cats.syntax.functor._

import scala.concurrent.duration._

trait Cache[A, B, F[_]] {
  def apply(a: A): F[B]
}

object Cache {
  case object NotHandled extends Throwable

  val cacheTtl: FiniteDuration = 10.minutes

  sealed trait CacheResult[B]

  object CacheResult {
    def apply[B](value: B): CacheResult[B] = Cached(value)
  }

  case class Cached[B](value: B) extends CacheResult[B]

  case class NotFound[B](reason: Throwable) extends CacheResult[B]

  def apply[A, B, F[_]: Concurrent: Temporal](f: A => F[B]): F[Cache[A, B, F]] = for {
    mutex    <- Mutex[F]
    cacheRef <- Ref.of(Map[A, Deferred[F, CacheResult[B]]]())
  } yield new Cache[A, B, F] {

    def getCachedResult(a: A): F[CacheResult[B]] =
      f(a).map(CacheResult.apply[B]).recover { case e => NotFound[B](e) }

    def startProcess(a: A, deferredResult: Deferred[F, CacheResult[B]]): F[Unit] =
      for {
        result <- getCachedResult(a)
        _      <- deferredResult.complete(result) >> Temporal[F].sleep(cacheTtl) >> cacheRef.update(_ - a)
      } yield () // todo: do not cache other errors than notfound?

    def startCaching(a: A): F[Deferred[F, CacheResult[B]]] = for {
      deferredResult <- Deferred[F, CacheResult[B]]
      _              <- cacheRef.update(_ + (a -> deferredResult))
      _              <- startProcess(a, deferredResult).start
    } yield deferredResult

    def readCache(a: A): F[Deferred[F, CacheResult[B]]] = for {
      cache          <- cacheRef.get
      deferredResult <- Concurrent[F].fromOption(cache.get(a), NotHandled) orElse startCaching(a)
    } yield deferredResult

    override def apply(a: A): F[B] = for {
      deferredValue <- mutex(readCache(a))
      result        <- deferredValue.get
      value <- result match {
        case Cached(value)    => value.pure[F]
        case NotFound(reason) => reason.raiseError[F, B]
      }
    } yield value
  }
}
