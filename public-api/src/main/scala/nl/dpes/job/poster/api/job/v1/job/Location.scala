package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.data.Validated.Valid
import cats.syntax.either._
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, DecodingFailure, Encoder}
import nl.dpes.job.poster.api.service.shared.{Zipcode => AppLocation}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}

sealed trait Location

object Location {

  implicit lazy val locationEncoder: Encoder[Location] = deriveEncoder

  implicit lazy val locationDecoder: Decoder[Location] =
    Decoder.instance { c =>
      c.downField("Geolocation")
        .as[Geolocation]
        .orElse(c.downField("Zipcode").as[Zipcode])
        .orElse(c.as[String].map(Zipcode(_))) // for backwards compatibility we will still accept a string at this location
        .orElse(c.downField("City").as[City])
        .orElse(DecodingFailure(s"Cannot decode location from '${c.value.noSpaces}'", List()).asLeft[Location])
    }
}

case class Zipcode(zipcode: String) extends Location

object Zipcode {

  implicit lazy val encoder: Encoder[Zipcode] = Encoder.encodeString.contramap[Zipcode](_.zipcode)
  implicit lazy val decoder: Decoder[Zipcode] = Decoder.decodeString.emap(Zipcode(_).asRight[String])

  def map(location: Option[Zipcode])(implicit cursor: Cursor): Validated[MappingError, Option[AppLocation]] =
    Valid(location.map(location => AppLocation(location.zipcode)))
      .leftMap(error => MappingError(cursor -> error))
}

case class Geolocation(longitude: Longitude, latitude: Latitude) extends Location

object Geolocation {
  implicit lazy val geolocationEncoder: Encoder[Geolocation] = deriveEncoder
  implicit lazy val geolocationDecoder: Decoder[Geolocation] = deriveDecoder
}

case class City(city: String) extends Location

object City {
  implicit lazy val cityEncoder: Encoder[City] = Encoder.encodeString.contramap[City](_.city)
  implicit lazy val cityDecoder: Decoder[City] = Decoder.decodeString.map(City(_))
}
