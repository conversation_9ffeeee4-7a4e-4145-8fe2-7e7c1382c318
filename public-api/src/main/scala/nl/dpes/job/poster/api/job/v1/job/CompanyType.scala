package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.syntax.validated._
import io.circe.{Decoder, Encoder}

case class CompanyType private (companyType: String) extends AnyVal

object CompanyType {

  val validCompanyTypes: Set[String] = Set(
    "Direct employer",
    "Human resource manager",
    "Mediabureau"
  )

  def apply(companyType: String): Validated[String, CompanyType] =
    if (validCompanyTypes contains companyType) new CompanyType(companyType).valid
    else s"Company type '$companyType' is not valid.".invalid

  implicit lazy val encoder: Encoder[CompanyType] = Encoder.encodeString.contramap[CompanyType](_.companyType)
  implicit lazy val decoder: Decoder[CompanyType] = Decoder.decodeString.emap(CompanyType(_).toEither)

  object SwaggerDoc {
    val example: CompanyType = new CompanyType("Direct employer")
  }
}
