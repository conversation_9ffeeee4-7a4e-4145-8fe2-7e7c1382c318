package nl.dpes.job.poster.api.jobapplication.v1.jobapplication

import cats.data.Validated
import cats.implicits.toBifunctorOps

import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter.ISO_ZONED_DATE_TIME
import scala.util.Try

case class SearchDateTimeRange private (start: StartDate, end: EndDate)

object SearchDateTimeRange {

  def apply(startDate: StartDate, endDate: EndDate): Validated[String, SearchDateTimeRange] =
    Validated.fromEither(for {
      startLocalDateTime <- Try(ZonedDateTime.parse(startDate.value, ISO_ZONED_DATE_TIME)).toEither.leftMap(_.getMessage)
      endLocalDateTime   <- Try(ZonedDateTime.parse(endDate.value, ISO_ZONED_DATE_TIME)).toEither.leftMap(_.getMessage)
      result <-
        if (endLocalDateTime.isBefore(startLocalDateTime))
          Left(s"End date '${endLocalDateTime.toLocalDate}' cannot be before start date '${startLocalDateTime.toLocalDate}'.")
        else Right(new SearchDateTimeRange(startDate, endDate))
    } yield result)

  object SwaggerDoc {
    SearchDateTimeRange(StartDate.SwaggerDoc.infStartDateTime, EndDate.SwaggerDoc.supEndDateTime)
  }
}
