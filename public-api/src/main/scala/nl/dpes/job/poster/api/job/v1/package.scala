package nl.dpes.job.poster.api
package job

import cats.Parallel
import cats.effect.kernel.Async
import cats.implicits.catsSyntaxApplicativeId
import io.getquill.{SnakeCase, SqliteJdbcContext}
import nl.dpes.job.poster.api.config.Configuration
import nl.dpes.job.poster.api.reference_id.{ReferenceIdRepository, ReferenceIdService}
import nl.dpes.job.poster.api.service.JobPosterServiceFactory
import nl.dpes.job.poster.api.service.addressservice.{AddressServiceClient, JobAddressService}
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import nl.dpes.job.poster.api.defaults.DefaultsRepository
import nl.dpes.job.poster.api.service.jobmanager.{JobManager, JobManagerFactory}
import nl.dpes.job.poster.api.service.logo.LogoService
import nl.dpes.job.poster.api.service.occupationclassification.OccupationClassifier
import nl.dpes.job.poster.api.service.prediction.{V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, VacancyEnricherClient}
import nl.dpes.job.poster.api.service.recruiter.{RecruiterService, RecruiterServiceFactory}
import nl.dpes.job.poster.api.service.uuid.UuidGenerator
import nl.dpes.job.poster.api.shutdownguard.ShutdownGuard
import org.http4s.HttpRoutes
import org.typelevel.log4cats.LoggerFactory
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend
import sttp.tapir.server.ServerEndpoint

package object v1 extends Configuration {
  val jobEndpoint: JobEndpoint = JobEndpoint("v1")

  case class Initializer[F[_]](controller: Controller[F]) {
    def endpoints: F[List[ServerEndpoint[Any, F]]] = controller.endpoints
    def routes: F[HttpRoutes[F]]                   = controller.routes
  }

  def initializer[F[_]: Async: Parallel: LoggerFactory](
    authService: AuthenticationService[F],
    referenceIdRepository: ReferenceIdRepository[F],
    shutdownGuard: ShutdownGuard[F]
  )(implicit context: SqliteJdbcContext[SnakeCase.type]): F[Initializer[F]] =
    Initializer(
      Controller
        .impl[F](
          authService,
          ControllerService(
            JobPosterServiceFactory.impl(
              JobManagerFactory.impl(
                JobManager.connection(JobManagerConnection.host, JobManagerConnection.port),
                LogoService.resource(LogoService.client(FileService.host, FileService.port)),
                OccupationClassifier.apply[F](
                  VacancyEnricher.impl[F](
                    VacancyEnricherClient.impl[F](
                      VacancyEnricherConfig.host,
                      VacancyEnricherConfig.apiKey,
                      AsyncHttpClientFs2Backend.resource[F]()
                    )
                  )
                ),
                VacancyEnricher.impl[F](
                  VacancyEnricherClient.impl[F](
                    VacancyEnricherConfig.host,
                    VacancyEnricherConfig.apiKey,
                    AsyncHttpClientFs2Backend.resource[F]()
                  )
                )
              ),
              RecruiterServiceFactory.impl(RecruiterService.connection(RecruiterServiceConnection.host, RecruiterServiceConnection.port)),
              DefaultsRepository.impl
            ),
            UuidGenerator[F],
            JobAddressService.impl[F](
              AddressServiceClient.impl[F](
                AddressService.host,
                AsyncHttpClientFs2Backend.resource[F]()
              )
            ),
            ReferenceIdService.impl[F](referenceIdRepository),
            shutdownGuard
          )
        )
    ).pure
}
