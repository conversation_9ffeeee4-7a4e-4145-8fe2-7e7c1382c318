package nl.dpes.job.poster.api
package jobapplication.v1

import cats.effect.Sync
import cats.effect.kernel.Async
import cats.implicits._
import io.circe.generic.auto._
import nl.dpes.job.poster.api.jobapplication.mapper.JobApplication
import nl.dpes.job.poster.api.jobapplication.v1.cv.ApplicationId
import nl.dpes.job.poster.api.jobapplication.v1.jobapplication.RawDate
import nl.dpes.job.poster.api.{BadRequest, Conflict, Forbidden, NotFound, ServiceUnavailable, Unauthorized, Unknown}
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import nl.dpes.job.poster.api.serverOptions
import org.http4s.HttpRoutes
import sttp.model.StatusCode
import sttp.tapir._
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe.jsonBody
import sttp.tapir.server.ServerEndpoint
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter

trait JobApplicationController[F[_]] {
  def getJobApplications: F[ServerEndpoint[Any, F]]
  def downloadCV: F[ServerEndpoint[Any, F]]
  def routes: F[HttpRoutes[F]]
  def endpoints: F[List[ServerEndpoint[Any, F]]]
}

object JobApplicationController {

  def impl[F[_]: Async](
    authenticationService: AuthenticationService[F],
    jobApplicationsService: JobApplicationService[F]
  ): JobApplicationController[F] =
    new JobApplicationController[F] {

      override def getJobApplications: F[ServerEndpoint[Any, F]] = Sync[F].delay {
        jobApplicationClient
          .baseEndpoint(
            authenticationService.authenticate,
            _.errorOut(
              oneOf(
                oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
                oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest])),
                oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden]))
              )
            )
          )
          .get
          .description("Returns all job applications for a specific period of time.")
          .in("application")
          .in(query[RawDate]("startDate").example(RawDate.SwaggerDoc.infRawDateTime))
          .in(query[Option[RawDate]]("endDate").example(RawDate.SwaggerDoc.supRawDateTime.some))
          .out(jsonBody[Seq[JobApplication]])
          .serverLogic(jobApplicationsService.getJobApplications)
      }

      override def downloadCV: F[ServerEndpoint[Any, F]] = Sync[F].delay {
        jobApplicationClient
          .baseEndpoint(
            authenticationService.authenticate,
            _.errorOut(
              oneOf(
                oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
                oneOfVariant[Conflict](statusCode(StatusCode.Conflict).and(jsonBody[Conflict])),
                oneOfVariant[NotFound](statusCode(StatusCode.NotFound).and(jsonBody[NotFound])),
                oneOfVariant[Unknown](statusCode(StatusCode.InternalServerError).and(jsonBody[Unknown])),
                oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest])),
                oneOfVariant[ServiceUnavailable](statusCode(StatusCode.ServiceUnavailable).and(jsonBody[ServiceUnavailable])),
                oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden]))
              )
            )
          )
          .get
          .description("Downloads the CV related to a specific job application. Click on '*Download file*' once the request is successful.")
          .in("application" / path[ApplicationId]("applicationId") / "cv")
          .out(headers)
          .out(inputStreamBody)
          .serverLogic(jobApplicationsService.downloadCV)
      }

      override def routes: F[HttpRoutes[F]] = for {
        endpoint <- List(getJobApplications, downloadCV).sequence
        swaggerRoutes <- Sync[F].delay(
          Http4sServerInterpreter[F]()
            .toRoutes(
              SwaggerInterpreter(swaggerUIOptions =
                SwaggerUIOptions(
                  List("docs", "job-applications", jobApplicationClient.version),
                  "docs.yaml",
                  Nil,
                  useRelativePaths = false,
                  showExtensions = false,
                  initializerOptions = None,
                  oAuthInitOptions = None
                )
              )
                .fromServerEndpoints[F](endpoint, "Job Applications", jobApplicationClient.version)
            )
        )
        endpointRoutes <- Sync[F].delay(Http4sServerInterpreter[F](serverOptions).toRoutes(endpoint))
      } yield swaggerRoutes <+> endpointRoutes

      override def endpoints: F[List[ServerEndpoint[Any, F]]] = List(getJobApplications, downloadCV).sequence
    }
}
