package nl.dpes.job.poster.api.service.addressservice

import cats.effect.{Async, Resource}
import cats.implicits.{catsSyntaxApplicativeError, toBifunctorOps, toFunctorOps}
import cats.syntax.monadError._
import io.circe.generic.auto._
import nl.dpes.job.poster.api.job.v1.job.{City, Geolocation}
import nl.dpes.job.poster.api.tracing.RequestTimer
import nl.dpes.job.poster.api.tracing.RequestTimer.ClientName.addressService
import org.typelevel.log4cats._
import sttp.client3.circe.asJson
import sttp.client3.{basicRequest, HttpError, SttpBackend, UriContext}
import sttp.model.StatusCode

import java.util.UUID

trait AddressServiceClient[F[_]] {

  def getZipcodeByGeolocation(geolocation: Geolocation): F[AddressInfo]
  def getZipcodeByCity(city: City): F[AddressInfo]
}

object AddressServiceClient {

  case class InvalidData(message: String) extends Throwable(message)

  case class ZipCodeNotFound(geolocation: Geolocation)
      extends Throwable(s"No ZipCode has been found for coordinates: ${geolocation.longitude}, ${geolocation.latitude}")
  case class ZipCodeNotFoundByCityName(city: City) extends Throwable(s"No ZipCode has been found for city: ${city.city}")
  case class UnsupportedLocale(message: String)    extends Throwable(s"Unsupported locale: $message")
  case class AddressServiceError(message: String)  extends Throwable(message)

  def impl[F[_]: Async: LoggerFactory](
    host: String,
    addressServiceBackend: Resource[F, SttpBackend[F, Any]]
  ): AddressServiceClient[F] =
    new AddressServiceClient[F] {
      val logger: Logger[F] = LoggerFactory[F].getLogger

      override def getZipcodeByGeolocation(geolocation: Geolocation): F[AddressInfo] = addressServiceBackend.use { backend =>
        RequestTimer(addressService).log(
          basicRequest
            .get(uri"$host/api/v1/geolocations/location/${geolocation.longitude.value}/${geolocation.latitude.value}?countryCode=nl")
            .header("X-Request-Id", UUID.randomUUID().toString)
            .response(asJson[AddressInfo])
            .send(backend)
            .map(
              _.body
                .leftMap {
                  case ex @ HttpError(_, StatusCode.BadRequest)    => InvalidData(ex.getMessage)
                  case HttpError(_, StatusCode.NotFound)           => ZipCodeNotFound(geolocation)
                  case ex @ HttpError(_, StatusCode.NotAcceptable) => UnsupportedLocale(ex.getMessage)
                  case ex @ _                                      => AddressServiceError(s"Invalid '$geolocation': ${ex.getMessage}")
                }
            )
            .rethrow
            .onError { case e => logger.warn(e.getMessage) }
        )
      }

      override def getZipcodeByCity(city: City): F[AddressInfo] = addressServiceBackend.use { backend =>
        RequestTimer(addressService).log(
          basicRequest
            .get(uri"$host/api/v1/geolocations/nl/${city.city.trim}")
            .header("X-Request-Id", UUID.randomUUID().toString)
            .response(asJson[AddressInfo])
            .send(backend)
            .map(
              _.body
                .leftMap {
                  case ex @ HttpError(_, StatusCode.BadRequest)    => InvalidData(ex.getMessage)
                  case HttpError(_, StatusCode.NotFound)           => ZipCodeNotFoundByCityName(city)
                  case ex @ HttpError(_, StatusCode.NotAcceptable) => UnsupportedLocale(ex.getMessage)
                  case ex @ _                                      => AddressServiceError(s"Invalid '$city': ${ex.getMessage}")
                }
            )
            .rethrow
            .onError { case e => logger.warn(e.getMessage) }
        )
      }
    }
}
