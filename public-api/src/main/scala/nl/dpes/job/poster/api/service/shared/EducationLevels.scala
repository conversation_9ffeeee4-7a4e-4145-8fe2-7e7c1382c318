package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

case class EducationLevels private (levels: Set[EducationLevel]) extends AnyVal

object EducationLevels {

  implicit lazy val encoder: Encoder[EducationLevels] = Encoder.encodeSet[EducationLevel].contramap(_.levels)
  implicit lazy val decoder: Decoder[EducationLevels] = Decoder.decodeSet[EducationLevel].emap(EducationLevels(_).toEither)

  val minEducationLevels: Int = 1
  val maxEducationLevels: Int = 5

  def checkMinimumAmount(educationLevels: EducationLevels): Validated[String, EducationLevels] =
    Validated.cond(
      educationLevels.levels.size >= minEducationLevels,
      educationLevels,
      s"At least $minEducationLevels level(s) should be chosen"
    )

  def checkMaximumAmount(educationLevels: EducationLevels): Validated[String, EducationLevels] =
    Validated.cond(
      educationLevels.levels.size <= maxEducationLevels,
      educationLevels,
      s"At most $maxEducationLevels levels should be chosen"
    )

  def apply(items: Set[EducationLevel]): Validated[String, EducationLevels] =
    new EducationLevels(items).valid andThen checkMinimumAmount andThen checkMaximumAmount

  object SwaggerDoc {
    val example = new EducationLevels(Set(EducationLevel.SwaggerDoc.hbo, EducationLevel.SwaggerDoc.wo, EducationLevel.SwaggerDoc.havo))
  }
}
