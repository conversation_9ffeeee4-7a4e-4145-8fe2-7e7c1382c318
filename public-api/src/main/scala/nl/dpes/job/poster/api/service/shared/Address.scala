package nl.dpes.job.poster.api.service.shared

import io.circe.{Decoder, Encoder}
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}

sealed trait Address

object Address {
  implicit lazy val encoder: Encoder[Address] = deriveEncoder[Address]
  implicit lazy val decoder: Decoder[Address] = deriveDecoder[Address]
}

final case class PostalAddress(
  streetNameAndHouseNumber: String,
  city: String,
  zipCode: String
) extends Address

object PostalAddress {
  implicit lazy val encoder: Encoder[PostalAddress] = deriveEncoder[PostalAddress]
  implicit lazy val decoder: Decoder[PostalAddress] = deriveDecoder[PostalAddress]
}

final case class DomesticAddress(
  street: String,
  houseNumber: String,
  houseNumberAddition: Option[String] = None,
  zipCode: String,
  city: String
) extends Address

object DomesticAddress {
  implicit lazy val encoder: Encoder[DomesticAddress] = deriveEncoder[DomesticAddress]
  implicit lazy val decoder: Decoder[DomesticAddress] = deriveDecoder[DomesticAddress]
}

final case class ForeignAddress(address: String, country: ISOAlpha2) extends Address

object ForeignAddress {
  implicit lazy val encoder: Encoder[ForeignAddress] = deriveEncoder[ForeignAddress]
  implicit lazy val decoder: Decoder[ForeignAddress] = deriveDecoder[ForeignAddress]
}
