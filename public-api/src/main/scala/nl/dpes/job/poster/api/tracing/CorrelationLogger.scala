package nl.dpes.job.poster.api.tracing

import cats.Functor
import cats.syntax.functor._
import org.typelevel.log4cats.{LoggerFactory, SelfAwareStructuredLogger}

case class CorrelationLoggerFactory[F[_]: Functor: LoggerFactory](correlationId: CorrelationId, requestId: RequestId)
    extends LoggerFactory[F] {

  private def addContext(logger: SelfAwareStructuredLogger[F]): SelfAwareStructuredLogger[F] = logger.addContext(
    "correlation-id" -> correlationId.value,
    "request-id"     -> requestId.value
  )

  override def getLoggerFromName(name: String): SelfAwareStructuredLogger[F] = addContext(LoggerFactory[F].getLoggerFromName(name))

  override def fromName(name: String): F[SelfAwareStructuredLogger[F]] = LoggerFactory[F].fromName(name).map(addContext)
}
