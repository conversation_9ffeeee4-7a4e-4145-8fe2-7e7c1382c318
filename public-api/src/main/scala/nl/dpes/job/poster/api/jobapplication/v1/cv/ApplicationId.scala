package nl.dpes.job.poster.api.jobapplication.v1.cv

import sttp.tapir.{Codec, DecodeResult}
import sttp.tapir.CodecFormat.TextPlain

case class ApplicationId private (applicationId: String) extends AnyVal

object ApplicationId {

  implicit val applicationIdCodec: Codec[String, ApplicationId, TextPlain] = Codec.string.mapDecode(decode)(encode)

  def decode(applicationId: String): DecodeResult[ApplicationId] = DecodeResult.Value(new ApplicationId(applicationId))

  def encode(applicationId: ApplicationId): String = applicationId.applicationId
}
