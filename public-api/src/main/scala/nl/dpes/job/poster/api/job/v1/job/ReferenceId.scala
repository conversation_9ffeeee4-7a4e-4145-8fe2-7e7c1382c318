package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

import java.net.{URLDecoder, URLEncoder}

case class ReferenceId(value: String) extends AnyVal

object ReferenceId {
  sealed abstract class ReferenceIdError(message: String) extends Throwable(message)

  case object EmptyReferenceId extends ReferenceIdError("ReferenceId cannot be empty.")

  case class InvalidReferenceIdLength(referenceId: String)
      extends ReferenceIdError(
        s"ReferenceId length should be between 1 and 100 characters. The provided id length is '${referenceId.length}' characters."
      )

  implicit val encoder: Encoder[ReferenceId] = Encoder.encodeString.contramap[ReferenceId](_.value)
  implicit val decoder: Decoder[ReferenceId] = Decoder.decodeString.emap(ReferenceId(_).toEither.leftMap(_.getMessage))

  implicit class UrlEncoder(val referenceId: ReferenceId) {
    def asUrlEncoded: Either[Throwable, String] = Either.catchNonFatal(URLEncoder.encode(referenceId.value, "UTF-8"))
  }

  implicit class UrlDecoder(val referenceId: String) {
    def asReferenceId: Either[Throwable, ReferenceId] = ReferenceId(URLDecoder.decode(referenceId, "UTF-8")).toEither
  }

  // This constructor should not be used to read data from the database, because the validation rules might change
  def apply(referenceId: String): Validated[Throwable, ReferenceId] = {
    val trimmedReferenceId = referenceId.trim
    if (trimmedReferenceId.nonEmpty)
      if (trimmedReferenceId.length > 100) InvalidReferenceIdLength(trimmedReferenceId).invalid
      else new ReferenceId(trimmedReferenceId).valid
    else EmptyReferenceId.invalid
  }

  object SwaggerDoc {
    val example = new ReferenceId("1234-5698")
  }
}
