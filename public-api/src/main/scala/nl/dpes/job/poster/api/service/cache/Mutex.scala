package nl.dpes.job.poster.api.service.cache

import cats.effect.Concurrent
import cats.effect.std.Semaphore
import cats.implicits._

trait Mutex[F[_]] {
  def apply[A](thunk: => F[A]): F[A]
}

object Mutex {

  def apply[F[_]: Concurrent]: F[Mutex[F]] = for {
    semaphore <- Semaphore[F](1)
  } yield new Mutex[F] {

    override def apply[A](thunk: => F[A]): F[A] = Concurrent[F].uncancelable { poll =>
      for {
        _      <- semaphore.acquire
        result <- poll(thunk)
        _      <- semaphore.release
      } yield result
    }
  }
}
