package nl.dpes.job.poster.api.service.job

import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.defaults.Defaults
import nl.dpes.job.poster.api.service.shared.{
  ApplicationMethod,
  CareerLevel,
  Company,
  ContactInformation,
  ContractTypes,
  EducationLevels,
  Hour,
  Html,
  IndustryCategories,
  JobCategories,
  Logo,
  Occupation,
  Prediction,
  PublicationPeriod,
  Range,
  SalaryRange,
  Video,
  Workplace,
  Zipcode
}

case class JobUpdate(
  title: Option[String],
  description: Option[Html],
  occupation: Option[Occupation],
  jobCategories: Option[JobCategories],
  industryCategories: Option[IndustryCategories],
  educationLevels: Option[EducationLevels],
  careerLevel: Option[CareerLevel],
  contractTypes: Option[ContractTypes],
  workplace: Option[Workplace],
  workingHours: Option[Range[Hour]],
  salary: Option[SalaryRange],
  location: Option[Zipcode],
  publicationPeriod: Option[PublicationPeriod],
  applicationMethod: Option[ApplicationMethod],
  logo: Option[Logo],
  video: Option[Video],
  company: Option[Company],
  contactInformation: Option[ContactInformation]
)

object JobUpdate {
  implicit lazy val encoder: Encoder[JobUpdate] = deriveEncoder[JobUpdate]
  implicit lazy val decoder: Decoder[JobUpdate] = deriveDecoder[JobUpdate]

  implicit class JobMerger(job: JobUpdate) {

    def mergeDefaults(defaults: Defaults): JobUpdate = JobUpdate(
      job.title orElse defaults.title,
      job.description orElse defaults.description,
      job.occupation orElse defaults.occupation,
      job.jobCategories orElse defaults.jobCategories,
      job.industryCategories orElse defaults.industryCategories,
      job.educationLevels orElse defaults.educationLevels,
      job.careerLevel orElse defaults.careerLevel,
      job.contractTypes orElse defaults.contractTypes,
      job.workplace orElse defaults.workplace,
      job.workingHours orElse defaults.workingHours,
      job.salary orElse defaults.salary,
      job.location orElse defaults.location,
      job.publicationPeriod,
      job.applicationMethod orElse defaults.applicationMethod,
      job.logo orElse defaults.logo,
      job.video orElse defaults.video,
      job.company orElse defaults.company,
      job.contactInformation
    )

    def mergePrediction(prediction: Option[Prediction]): JobUpdate =
      job.copy(
        jobCategories = job.jobCategories orElse prediction.map(_.jobCategories),
        educationLevels = job.educationLevels orElse prediction.map(_.educationLevels),
        careerLevel = job.careerLevel orElse prediction.map(_.careerLevel),
        contractTypes = job.contractTypes orElse prediction.map(_.contractTypes)
      )
  }
}
