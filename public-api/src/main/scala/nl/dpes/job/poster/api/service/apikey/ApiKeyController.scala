package nl.dpes.job.poster.api.service.apikey

import cats.effect.Sync
import cats.effect.kernel.Async
import cats.implicits._
import io.circe.generic.auto._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.{Forbidden, NotFound, Unauthorized}
import nl.dpes.job.poster.api.service.baseEndpoint
import nl.dpes.job.poster.api.serverOptions
import org.http4s.HttpRoutes
import sttp.model.StatusCode
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe.jsonBody
import sttp.tapir.server.ServerEndpoint
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir._

trait ApiKeyController[F[_]] {
  def generateKey: F[ServerEndpoint[Any, F]]
  def revokeKey: F[ServerEndpoint[Any, F]]
  def generateKeyForIntegration: F[ServerEndpoint[Any, F]]
  def recruiterKeys: F[ServerEndpoint[Any, F]]
  def recruiterId: F[ServerEndpoint[Any, F]]
  def endpoints: F[List[ServerEndpoint[Any, F]]]
  def routes: F[HttpRoutes[F]]
}

object ApiKeyController {

  def impl[F[_]: Async](apiKeyService: ApiKeyTapirService[F]): ApiKeyController[F] = new ApiKeyController[F] {

    override def generateKey: F[ServerEndpoint[Any, F]] = Sync[F].delay {
      baseEndpoint(
        _.errorOut(
          oneOf(
            oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
            oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden]))
          )
        )
      ).put
        .in("key")
        .out(jsonBody[api.ApiKey])
        .serverLogic(apiKeyService.generateKey)
    }

    override def revokeKey: F[ServerEndpoint[Any, F]] = Sync[F].delay {
      import api.ApiKeyId
      baseEndpoint(
        _.errorOut(
          oneOf(
            oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
            oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden]))
          )
        )
      ).delete
        .in("key" / path[ApiKeyId]("id"))
        .out(statusCode(StatusCode.Ok))
        .serverLogic(apiKeyService.revokeKey)
    }

    override def generateKeyForIntegration: F[ServerEndpoint[Any, F]] = Sync[F].delay {
      baseEndpoint(
        _.errorOut(
          oneOf(
            oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
            oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden]))
          )
        )
      ).post
        .in("keys")
        .in(jsonBody[api.IntegrationInput])
        .out(jsonBody[api.ApiKey])
        .serverLogic(apiKeyService.generateKeyForIntegration)
    }

    override def recruiterKeys: F[ServerEndpoint[Any, F]] = Sync[F].delay(
      baseEndpoint(
        _.errorOut(
          oneOf(
            oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
            oneOfVariant[NotFound](statusCode(StatusCode.NotFound).and(jsonBody[NotFound]))
          )
        )
      ).get
        .in("keys")
        .out(jsonBody[List[api.ApiKeyInformation]])
        .serverLogic(apiKeyService.getRecruiterKeys)
    )

    override def recruiterId: F[ServerEndpoint[Any, F]] = Sync[F].delay(
      baseEndpoint(
        _.errorOut(
          oneOf(
            oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
            oneOfVariant[NotFound](statusCode(StatusCode.NotFound).and(jsonBody[NotFound]))
          )
        )
      ).post
        .in("recruiterId")
        .in(jsonBody[api.ApiKey])
        .out(jsonBody[SalesForceId])
        .serverLogic(apiKeyService.getRecruiterId)
    )

    override def endpoints: F[List[ServerEndpoint[Any, F]]] =
      List(generateKey, generateKeyForIntegration, revokeKey, recruiterId, recruiterKeys).sequence

    override def routes: F[HttpRoutes[F]] = for {
      endpoints      <- endpoints
      endpointRoutes <- Sync[F].delay(Http4sServerInterpreter[F](serverOptions).toRoutes(endpoints))
    } yield endpointRoutes
  }
}
