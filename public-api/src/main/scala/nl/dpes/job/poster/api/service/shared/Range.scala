package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe._
import io.circe.syntax._

case class Range[A: Ordering] private (lower: A, upper: A)

object Range {

  implicit def rangeEncoder[A: Encoder: Ordering]: Encoder[Range[A]] = (range: Range[A]) =>
    Json.obj(
      "lower" -> range.lower.asJson,
      "upper" -> range.upper.asJson
    )

  implicit def rangeDecoder[A: Decoder: Ordering]: Decoder[Range[A]] = (c: HCursor) =>
    for {
      lower <- c.downField("lower").as[A]
      upper <- c.downField("upper").as[A]
      range <- Range[A](lower, upper).toEither.leftMap(e => DecodingFailure(e, c.history))
    } yield range

  def apply[A: Ordering](lower: A, upper: A): Validated[String, Range[A]] =
    if (implicitly[Ordering[A]].gt(lower, upper))
      s"Lower value '$lower' cannot be greater than upper value '$upper'.".invalid
    else new Range(lower, upper).valid

  object SwaggerDoc {
    val salaryExample = new Range[Salary](Salary.SwaggerDoc.minSalaryExample, Salary.SwaggerDoc.maxSalaryExample)
    val hourExample   = new Range[Hour](Hour.SwaggerDoc.minHourExample, Hour.SwaggerDoc.maxHourExample)
  }
}
