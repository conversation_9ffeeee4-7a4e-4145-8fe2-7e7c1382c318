package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import io.circe.{Decoder, Encoder}
import org.jsoup.Jsoup
import org.jsoup.safety.Safelist
import sttp.tapir.Schema.annotations.description

import scala.util.Try

@description(s"HTML document, which will be stripped of all tags, except for ${Html.allowedTags.mkString(", ")}.")
case class Html private (document: String) extends AnyVal {
  def text: String = Jsoup.parse(document).text()
}

object Html {

  implicit lazy val encoder: Encoder[Html] = Encoder.encodeString.contramap(_.document)
  implicit lazy val decoder: Decoder[Html] = Decoder.decodeString.emap(Html(_).toEither)

  val allowedTags: Seq[String]   = Seq("p", "b", "i", "u", "ol", "ul", "li")
  private val safeList: Safelist = Safelist.none().addTags(allowedTags: _*)

  def apply(document: String): Validated[String, Html] = Validated
    .fromTry(Try {
      val cleanedDocument = Jsoup.clean(document, safeList)
      val parsedDocument  = Jsoup.parse(cleanedDocument)
      parsedDocument.getAllElements.forEach(element => if (!element.hasText) element.remove())
      new Html(Option(parsedDocument.body().html()) getOrElse "")
    })
    .leftMap(_.getMessage)

  object SwaggerDoc {
    val example = new Html("<p>The description</p><p>To describe the duties of the defaults.</p>")
  }
}
