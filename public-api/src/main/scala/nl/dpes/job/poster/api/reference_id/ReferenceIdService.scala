package nl.dpes.job.poster.api.reference_id

import cats.effect.Async
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.job.v1.job.{JobId => ApiJobId, ReferenceId => ApiReferenceId}

trait ReferenceIdService[F[_]] {
  def store(recruiterId: SalesForceId, referenceId: ApiReferenceId, jobId: ApiJobId): F[Unit]
  def getReferenceId(jobId: ApiJobId): F[Option[ReferenceId]]
  def getLatestJobId(recruiterId: SalesForceId, referenceId: ApiReferenceId): F[Option[JobId]]
  def delete(recruiterId: SalesForceId, referenceId: ApiReferenceId, jobId: ApiJobId): F[Unit]
}

object ReferenceIdService {

  def impl[F[_]: Async](referenceIdRepository: ReferenceIdRepository[F]): ReferenceIdService[F] = new ReferenceIdService[F] {

    override def store(recruiterId: SalesForceId, referenceId: ApiReferenceId, jobId: ApiJobId): F[Unit] =
      referenceIdRepository
        .store(RecruiterId(recruiterId.idWithChecksum), ReferenceId(referenceId.value), JobId(jobId.value))

    override def getReferenceId(jobId: ApiJobId): F[Option[ReferenceId]] =
      referenceIdRepository.getReferenceId(JobId(jobId.value))

    override def getLatestJobId(recruiterId: SalesForceId, referenceId: ApiReferenceId): F[Option[JobId]] =
      referenceIdRepository.getLatestJobId(ReferenceId(referenceId.value), RecruiterId(recruiterId.idWithChecksum))

    override def delete(recruiterId: SalesForceId, referenceId: ApiReferenceId, jobId: ApiJobId): F[Unit] =
      referenceIdRepository
        .delete(RecruiterId(recruiterId.idWithChecksum), ReferenceId(referenceId.value), JobId(jobId.value))
  }
}
