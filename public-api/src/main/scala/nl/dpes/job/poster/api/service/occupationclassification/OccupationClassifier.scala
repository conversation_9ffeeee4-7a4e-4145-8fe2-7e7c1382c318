package nl.dpes.job.poster.api.service.occupationclassification

import cats.data.OptionT
import cats.effect.Async
import cats.syntax.applicative._
import cats.syntax.flatMap._
import cats.syntax.functor._
import nl.dpes.job.poster.api.service.job.{Job, JobUpdate}
import nl.dpes.job.poster.api.service.prediction.{JobDescription, JobTitle, OccupationClassification, VacancyEnricher}
import nl.dpes.job.poster.api.service.shared.{Configuration, Html, JobPosting, Occupation, PerformanceBased}

trait OccupationClassifier[F[_]] {
  def classify(job: Job, configuration: Option[Configuration]): F[Option[Classification]]
  def classify(job: JobUpdate, configuration: Option[Configuration]): F[Option[Classification]]
  def classify(classificationInfo: OccupationClassifier.ClassifcationInfo): F[Option[Classification]]
}

object OccupationClassifier {

  case class ClassifcationInfo(
    title: Option[String],
    description: Option[Html],
    occupation: Option[Occupation],
    configuration: Option[Configuration]
  )

  case object Unrecognized<PERSON>ob    extends Throwable("Could not classify job, since 'occupation' is missing and no classification was computed")
  case object UnclassifiedPBPJob extends Throwable("Performance Based Price job cannot be unclassified")

  def apply[F[_]: Async](vacancyEnricherService: VacancyEnricher[F]): OccupationClassifier[F] =
    new OccupationClassifier[F] {

      override def classify(job: Job, configuration: Option[Configuration]): F[Option[Classification]] =
        classify(ClassifcationInfo(job.title, job.description, job.occupation, configuration))

      override def classify(job: JobUpdate, configuration: Option[Configuration]): F[Option[Classification]] =
        classify(ClassifcationInfo(job.title, job.description, job.occupation, configuration))

      override def classify(classifcationInfo: ClassifcationInfo): F[Option[Classification]] =
        for {
          givenOccupation     <- Async[F].delay(classifcationInfo.occupation)
          predictedOccupation <- predictOccupation(classifcationInfo.title, classifcationInfo.description)
          isPerformanceBased  <- isPerformanceBasedProduct(classifcationInfo.configuration)
          result              <- validateClassification(givenOccupation.isDefined, predictedOccupation, isPerformanceBased)
        } yield result

      private def validateClassification(
        hasOccupation: Boolean,
        predictedOccupation: Option[OccupationClassification],
        isPerformanceBased: Boolean
      ): F[Option[Classification]] = Async[F].delay {
        (hasOccupation, predictedOccupation.isDefined, isPerformanceBased) match {
          // Performance-based product with occupation but no prediction
          case (true, false, true) => throw UnclassifiedPBPJob
          // No occupation and no prediction
          case (false, false, _) => throw UnrecognizedJob
          // All other cases: return the prediction
          case (_, _, _) => predictedOccupation
        }
      }

      private def predictOccupation(jobTitle: Option[String], jobDescription: Option[Html]): F[Option[OccupationClassification]] = (for {
        title       <- OptionT.fromOption[F](jobTitle)
        description <- OptionT.fromOption[F](jobDescription)
        prediction  <- OptionT(vacancyEnricherService.predict(JobTitle(title), JobDescription(description.text)).map(_.ISCO))
      } yield prediction).value

      private def isPerformanceBasedProduct(configuration: Option[Configuration]): F[Boolean] =
        configuration.map {
          case _: JobPosting       => false.pure
          case _: PerformanceBased => true.pure
        } getOrElse false.pure
    }
}
