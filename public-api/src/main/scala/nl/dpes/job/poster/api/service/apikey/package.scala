package nl.dpes.job.poster.api.service

import cats.syntax.bifunctor._
import io.circe._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import sttp.tapir.Schema

package object apikey {

  implicit lazy val salesForceIdSchema: Schema[SalesForceId] =
    Schema.string.map((id: String) => SalesForceId.apply(id).toOption)(_.idWithChecksum)
  implicit lazy val salesforceIdEncoder: Encoder[SalesForceId] = Encoder.encodeString.contramap(_.idWithChecksum)
  implicit lazy val salesforceIdDecoder: Decoder[SalesForceId] = Decoder.decodeString.emap(SalesForceId.apply(_).leftMap(_.getMessage))
}
