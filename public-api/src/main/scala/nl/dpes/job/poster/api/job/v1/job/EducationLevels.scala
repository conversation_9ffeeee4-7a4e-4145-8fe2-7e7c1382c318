package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits.{catsSyntaxValidatedId, toTraverseOps}
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{EducationLevels => AppEducationLevels, EducationLevel => AppEducationLevel}

case class EducationLevels(levels: Set[EducationLevel])

object EducationLevels {

  implicit lazy val encoder: Encoder[EducationLevels] = Encoder.encodeSet[EducationLevel].contramap(_.levels)
  implicit lazy val decoder: Decoder[EducationLevels] = Decoder.decodeSet[EducationLevel].emap(EducationLevels(_).toEither)

  private val minEducationLevels: Int = 1
  private val maxEducationLevels: Int = 5

  def checkMinimumAmount(educationLevels: EducationLevels): Validated[String, EducationLevels] =
    Validated.cond(
      educationLevels.levels.size >= minEducationLevels,
      educationLevels,
      s"At least $minEducationLevels level(s) should be chosen"
    )

  def checkMaximumAmount(educationLevels: EducationLevels): Validated[String, EducationLevels] =
    Validated.cond(
      educationLevels.levels.size <= maxEducationLevels,
      educationLevels,
      s"At most $maxEducationLevels levels should be chosen"
    )

  def apply(levels: Set[EducationLevel]): Validated[String, EducationLevels] =
    new EducationLevels(levels).valid andThen checkMinimumAmount andThen checkMaximumAmount

  def map(
    educationLevels: Option[EducationLevels]
  )(implicit cursor: Cursor): Validated[MappingError, Option[AppEducationLevels]] =
    (for {
      levels             <- educationLevels.map(levels => levels.levels).map(_.toList)
      appEducationLevels <- levels.traverse(level => AppEducationLevel(level.level).toOption).map(_.toSet)
    } yield AppEducationLevels(appEducationLevels)).sequence
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val example = new EducationLevels(Set(EducationLevel.SwaggerDoc.hbo, EducationLevel.SwaggerDoc.wo, EducationLevel.SwaggerDoc.havo))
  }
}
