package nl.dpes.job.poster.api.status

import cats.effect.Async
import cats.syntax.applicative._
import cats.syntax.flatMap._
import cats.syntax.functor._
import org.http4s.HttpRoutes
import sttp.model.StatusCode
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir.{endpoint, statusCode, PublicEndpoint}

trait StatusController[F[_]] {
  def showStatus: F[PublicEndpoint[Unit, Unit, Unit, Any]]
  def routes: F[HttpRoutes[F]]
}

object StatusController {

  def impl[F[_]: Async]: StatusController[F] = new StatusController[F] {

    override def showStatus: F[PublicEndpoint[Unit, Unit, Unit, Any]] =
      endpoint
        .in("status")
        .out(statusCode(StatusCode.Ok))
        .pure

    override def routes: F[HttpRoutes[F]] = for {
      status <- showStatus
      routes <- Http4sServerInterpreter[F]().toRoutes(status.serverLogicSuccess(_ => ().pure)).pure
    } yield routes

  }
}
