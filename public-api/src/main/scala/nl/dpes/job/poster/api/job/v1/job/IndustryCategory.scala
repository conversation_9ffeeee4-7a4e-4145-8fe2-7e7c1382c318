package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.syntax.validated._
import io.circe.{Decoder, Encoder}

case class IndustryCategory private (category: String) extends AnyVal

object IndustryCategory {

  val validIndustryCategories: Set[String] = Set(
    "Accountancy",
    "Advies/Consultancy",
    "Afval en milieu",
    "Automotive",
    "Banken/Financiële dienstverlening",
    "Beveiliging/Bewaking",
    "Bouw/Installatie",
    "Chemie/Petrochemie",
    "Detailhandel",
    "Elektronica",
    "Energie/Gas/Water",
    "Facilitaire dienstverlening",
    "Farmacie",
    "FMCG",
    "Gezondheidszorg/Welzijn",
    "Handel/Groothandel",
    "Horeca",
    "ICT",
    "Industrie",
    "Internet",
    "Juridische dienstverlening",
    "Kunst/Cultuur/Entertainment",
    "Landbouw/Bosbouw/Visserij",
    "Makelaardij/Vastgoed",
    "Maritiem",
    "Media/Uitgeverijen/TV",
    "Mode/Textiel/Cosmetica",
    "Onderwijs/Opleiding",
    "Overheid/Non-profit",
    "Overig",
    "Personen vervoer",
    "Reclame/PR/Communicatie",
    "Sport/Recreatie/Toerisme",
    "Techniek",
    "Telecom",
    "Transport/Opslag/Distributie",
    "Uitzend/Detachering/W&S",
    "Verzekeringen/Assurantiën",
    "Zakelijke dienstverlening"
  )

  def apply(category: String): Validated[String, IndustryCategory] = if (validIndustryCategories contains category)
    new IndustryCategory(category).valid
  else s"Industry category '$category' is not valid.".invalid

  implicit lazy val encoder: Encoder[IndustryCategory] = Encoder.encodeString.contramap[IndustryCategory](_.category)
  implicit lazy val decoder: Decoder[IndustryCategory] = Decoder.decodeString.emap(IndustryCategory(_).toEither)

  object SwaggerDoc {
    val techniek = new IndustryCategory("Techniek")
    val telecom  = new IndustryCategory("Telecom")
  }
}
