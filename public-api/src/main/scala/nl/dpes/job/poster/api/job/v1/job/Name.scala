package nl.dpes.job.poster.api
package job.v1.job

import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}

case class Name(firstName: String, lastName: String)

object Name {
  def mapToService(name: Name): service.shared.Name = service.shared.Name(name.firstName, name.lastName)

  implicit lazy val encoder: Encoder[Name] = deriveEncoder
  implicit lazy val decoder: Decoder[Name] = deriveDecoder
}
