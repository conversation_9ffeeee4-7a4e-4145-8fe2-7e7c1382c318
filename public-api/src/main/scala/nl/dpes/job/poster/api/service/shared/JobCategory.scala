package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.syntax.validated._
import io.circe.{Decoder, Encoder}

case class JobCategory private (category: String) extends AnyVal

object JobCategory {

  implicit lazy val encoder: Encoder[JobCategory] = Encoder.encodeString.contramap[JobCategory](_.category)
  implicit lazy val decoder: Decoder[JobCategory] = Decoder.decodeString.emap(JobCategory(_).toEither)

  val validJobCategories: Set[String] = Set(
    "Administratief/Secretarieel",
    "Automatisering/Internet",
    "Beleid/Bestuur/Staf",
    "Beveiliging/Defensie/Politie",
    "Commercieel/Verkoop",
    "Consultancy/Advies",
    "Design/Creatie/Journalistiek",
    "Directie/Management algemeen",
    "Financieel/Accounting",
    "Financiele dienstverlening",
    "Horeca/Detailhandel",
    "HR/Training/Opleiding",
    "Inkoop/Logistiek/Transport",
    "Juridisch",
    "Klantenservice/Callcenter/Receptie",
    "Marketing/PR/Communicatie",
    "Medisch/Zorg",
    "Onderwijs/Onderzoek/Wetenschap",
    "Overig",
    "Productie/Uitvoerend",
    "Techniek"
  )

  def apply(category: String): Validated[String, JobCategory] =
    if (validJobCategories.contains(category)) new JobCategory(category).valid
    else s"Job category '$category' is not valid.".invalid

  object SwaggerDoc {
    val administratief = new JobCategory("Administratief/Secretarieel")
    val automatisering = new JobCategory("Automatisering/Internet")
  }
}
