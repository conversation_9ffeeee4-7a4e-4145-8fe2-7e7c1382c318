package nl.dpes.job.poster.api.jobapplication.v1.jobapplication

import cats.data.Validated

import scala.util.Failure

case class EndDate private (value: String) extends AnyVal

object EndDate {

  def apply(date: RawDate): Validated[String, EndDate] = Validated
    .fromTry(date.value match {
      case dateTimeFormat(year, month, day, hours, minutes, seconds) =>
        constructIsoInstant(year, month, day, hours, minutes, seconds)
          .map(new EndDate(_))
      case unsupportedDateFormat => Failure(UnsupportedDateFormat(unsupportedDateFormat))
    })
    .leftMap(_.getMessage)

  object SwaggerDoc {
    val infEndDateTime = new EndDate("2023-01-03T00:00:00Z")
    val supEndDateTime = new EndDate("2023-12-03T23:59:59Z")
  }
}
