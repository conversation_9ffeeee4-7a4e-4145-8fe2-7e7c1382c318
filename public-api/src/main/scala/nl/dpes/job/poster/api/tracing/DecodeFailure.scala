package nl.dpes.job.poster.api.tracing

import cats.ApplicativeThrow
import cats.syntax.applicative._
import cats.syntax.applicativeError._
import sttp.tapir.DecodeResult
import sttp.tapir.DecodeResult.Error.JsonDecodeException

final case class DecodeFailure(message: String, errors: Map[String, String], original: String)

object DecodeFailure {

  def fromDecodeResultFailure[F[_]: ApplicativeThrow](failure: DecodeResult.Failure): F[DecodeFailure] = failure match {
    case DecodeResult.Error(original, error: JsonDecodeException) =>
      DecodeFailure(
        "Failed decoding body",
        error.errors
          .map { e =>
            ("$" :: e.path.map(_.encodedName)).mkString(".") -> e.msg
          }
          .sortBy(_._1)
          .toMap,
        original
      ).pure
    case DecodeResult.Mismatch(expected, actual) =>
      DecodeFailure(
        "HTTP method mismatch",
        Map("expected" -> expected, "actual" -> actual),
        s"Expected method: $expected, but got: $actual"
      ).pure
    case e => new Throwable(s"No JSON decode exception found: ${e.getClass} - ${e.toString}").raiseError
  }
}
