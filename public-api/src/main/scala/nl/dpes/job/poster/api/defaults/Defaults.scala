package nl.dpes.job.poster.api.defaults

import cats.Show
import cats.implicits._
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.syntax.EncoderOps
import io.circe.{parser, Decoder, Encoder}
import io.getquill.MappedEncoding
import nl.dpes.job.poster.api.service.shared.{
  ApplicationMethod,
  ApplyViaJobBoard,
  CareerLevel,
  Company,
  Configuration,
  ContractType,
  ContractTypes,
  EducationLevel,
  EducationLevels,
  Feature,
  Hour,
  Html,
  IndustryCategories,
  IndustryCategory,
  JobCategories,
  JobCategory,
  JobPosting,
  Logo,
  Occupation,
  Range,
  SalaryRange,
  SetOps,
  Site,
  Video,
  Workplace,
  Zipcode
}
import sttp.tapir.Schema.annotations.description

import scala.concurrent.duration.DurationInt

case class Defaults(
  @description("Represents the job title.")
  title: Option[String],
  @description(s"The job description in HTML. Only tags allowed: ${Html.allowedTags.mkString(", ")}.")
  description: Option[Html],
  @description("Represents the occupation.")
  occupation: Option[Occupation],
  @description(
    s"The category for this job. This field must contain at least 1 job category and can accept up to 2 categories. " +
    s"Allowed values: ${JobCategory.validJobCategories.format}."
  )
  jobCategories: Option[JobCategories],
  @description(
    s"The industry for this job. This field must contain at least 1 industry category and can accept up to 2 categories. " +
    s"All possible values are: ${IndustryCategory.validIndustryCategories.format}"
  )
  industryCategories: Option[IndustryCategories],
  @description(
    s"The required education levels for this job. This field must contain at least 1 education level and can accept up to 5 levels. " +
    s"Allowed values: ${EducationLevel.validEducationLevels.format}."
  )
  educationLevels: Option[EducationLevels],
  @description(
    s"The required career level for this job. Allowed values: ${CareerLevel.validCareerLevels.format}."
  )
  careerLevel: Option[CareerLevel],
  @description(
    s"The contract types for this job. This field must contain at least 1 contract type and can accept up to 5 types. " +
    s"Allowed values: ${ContractType.validContractTypes.format}."
  )
  contractTypes: Option[ContractTypes],
  @description(s"The workplace of this job. Allowed values: ${Workplace.validWorkplaces.format}.")
  workplace: Option[Workplace],
  @description("The amount of hours per week required for this  job.")
  workingHours: Option[Range[Hour]],
  @description("Offered salary for this job.")
  salary: Option[SalaryRange],
  @description("Location where the job will be.")
  location: Option[Zipcode],
  @description(
    "The way an applicant can apply for this job. The job application can be done via the job board or via an external website."
  )
  applicationMethod: Option[ApplicationMethod],
  @description("The url for a logo. The actual image will be copied by our system.")
  logo: Option[Logo],
  @description("The url for a video. Currently only Youtube and Vimeo are supported.")
  video: Option[Video],
  @description("Information on the company.")
  company: Option[Company],
  @description("The configuration of the job.")
  configuration: Option[Configuration]
)

object Defaults {
  val version: String = "0.4"

  implicit lazy val encoder: Encoder[Defaults] = deriveEncoder[Defaults]
  implicit lazy val decoder: Decoder[Defaults] = deriveDecoder[Defaults]

  val Empty: Defaults = defaultsDecoder("{}")

  def defaultsDecoder(input: String): Defaults = parser.decode[Defaults](input) match {
    // During decoding we must add logic to upcast older versions
    // to json: String -> Json; upcast: Json -> Json; to defaults: Json -> Defaults
    case Left(error)     => throw error
    case Right(defaults) => defaults
  }

  implicit lazy val encodeDefaults: MappedEncoding[Defaults, String] =
    MappedEncoding[Defaults, String](_.asJson.deepDropNullValues.noSpaces)
  implicit lazy val decodeDefaults: MappedEncoding[String, Defaults] = MappedEncoding[String, Defaults](defaultsDecoder)

  object SwaggerDoc {

    val defaultsObjectDescription = "A JSON representation for Defaults object"

    val defaultsExample: Defaults = Defaults(
      "Scala developer gezocht".some,
      Html.SwaggerDoc.example.some,
      Occupation.SwaggerDoc.example.some,
      JobCategories.SwaggerDoc.example.some,
      IndustryCategories.SwaggerDoc.example.some,
      EducationLevels.SwaggerDoc.example.some,
      CareerLevel.SwaggerDoc.example.some,
      ContractTypes.SwaggerDoc.example.some,
      Workplace.SwaggerDoc.example.some,
      Range.SwaggerDoc.hourExample.some,
      SalaryRange.SwaggerDoc.example.some,
      Zipcode("1018LL").some,
      ApplyViaJobBoard("John", "Doe", "<EMAIL>").some,
      Logo.SwaggerDoc.example.some,
      Video.SwaggerDoc.example.some,
      Company.SwaggerDoc.example.some,
      JobPosting(
        Set(Site.SwaggerDoc.nvb, Site.SwaggerDoc.iol),
        60.days,
        Set(Feature.SwaggerDoc.logo)
      ).some
    )
  }

  implicit lazy val showDefaults: Show[Defaults] = Show.show(d => d.asJson.show)
}
