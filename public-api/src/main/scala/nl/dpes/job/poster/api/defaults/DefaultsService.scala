package nl.dpes.job.poster.api.defaults

import cats.MonadThrow
import cats.implicits._
import nl.dpes.job.poster.api.{ErrorMessage, Forbidden, Unauthorized}
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.recruiter.RecruiterService.{RecruiterForbidden, RecruiterUnauthorized}
import nl.dpes.job.poster.api.service.recruiter.{AccessToken, RecruiterService}

trait DefaultsService[F[_]] {
  def getDefaults(bearerToken: BearerToken)(x: Unit): F[Either[ErrorMessage, Defaults]]
  def saveDefaults(bearerToken: BearerToken)(defaults: Defaults): F[Either[ErrorMessage, Unit]]
}

object DefaultsService {

  def apply[F[_]: MonadThrow](repository: DefaultsRepository[F], recruiterService: RecruiterService[F]): DefaultsService[F] =
    new DefaultsService[F] {

      override def getDefaults(bearerToken: BearerToken)(x: Unit): F[Either[ErrorMessage, Defaults]] =
        (for {
          recruiter <- recruiterService.getRecruiter(AccessToken(bearerToken.token))
          defaults  <- repository.readDefaults(recruiter.salesforceId)
        } yield defaults.asRight[ErrorMessage])
          .recover {
            case RecruiterUnauthorized(message) =>
              Unauthorized(s"Error retrieving recruiter '$message'.").asLeft[Defaults]
            case RecruiterForbidden(message) =>
              Forbidden(s"Error retrieving recruiter '$message'.").asLeft[Defaults]
          }

      override def saveDefaults(bearerToken: BearerToken)(defaults: Defaults): F[Either[ErrorMessage, Unit]] =
        (for {
          recruiter <- recruiterService.getRecruiter(AccessToken(bearerToken.token))
          _         <- repository.storeDefaults(recruiter.salesforceId, defaults)
        } yield ().asRight[ErrorMessage]).recover {
          case RecruiterUnauthorized(message) =>
            Unauthorized(s"Error retrieving recruiter '$message'.").asLeft[Unit]
          case RecruiterForbidden(message) =>
            Forbidden(s"Error retrieving recruiter '$message'.").asLeft[Unit]
        }
    }
}
