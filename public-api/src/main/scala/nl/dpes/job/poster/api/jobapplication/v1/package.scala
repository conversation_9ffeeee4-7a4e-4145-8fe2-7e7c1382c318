package nl.dpes.job.poster.api.jobapplication

import cats.Parallel
import cats.effect.kernel.Async
import cats.implicits.catsSyntaxApplicativeId
import io.getquill.{SnakeCase, SqliteJdbcContext}
import nl.dpes.job.poster.api.config.Configuration
import nl.dpes.job.poster.api.reference_id.{ReferenceIdRepository, ReferenceIdService}
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import org.http4s.HttpRoutes
import org.typelevel.log4cats.LoggerFactory
import sttp.tapir.server.ServerEndpoint

package object v1 extends Configuration {
  val jobApplicationClient: JobApplicationEndpoint = JobApplicationEndpoint("v1")

  case class Initializer[F[_]](jobApplicationController: JobApplicationController[F]) {
    def endpoints: F[List[ServerEndpoint[Any, F]]] = jobApplicationController.endpoints

    def routes: F[HttpRoutes[F]] = jobApplicationController.routes
  }

  def initializer[F[_]: Async: Parallel: LoggerFactory](
    authenticationService: AuthenticationService[F],
    referenceIdRepository: ReferenceIdRepository[F]
  )(implicit context: SqliteJdbcContext[SnakeCase.type]): F[Initializer[F]] =
    Initializer(
      JobApplicationController
        .impl[F](
          authenticationService,
          JobApplicationService(
            JobApplicationClient.impl(
              ApplicationService.host,
              ApplicationService.apiKey,
              JobApplicationClient.jobApplicationBackend
            ),
            ReferenceIdService.impl[F](referenceIdRepository)
          )
        )
    ).pure
}
