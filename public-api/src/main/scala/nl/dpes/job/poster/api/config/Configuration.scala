package nl.dpes.job.poster.api.config

import com.typesafe.config.ConfigFactory
import nl.dpes.job.poster.api.service.prediction.VacancyEnricherClient

trait Configuration {
  private val config = ConfigFactory.load()

  object Http {
    lazy val host: String     = config.getString("http.host")
    lazy val port: Int        = config.getInt("http.port")
    lazy val basePath: String = config.getString("http.basePath")
  }

  object RecruiterServiceConnection {
    lazy val host: String = config.getString("recruiterService.host")
    lazy val port: Int    = config.getInt("recruiterService.port")
  }

  object JobManagerConnection {
    lazy val host: String = config.getString("jobManager.host")
    lazy val port: Int    = config.getInt("jobManager.port")
  }

  object FileService {
    lazy val host: String = config.getString("fileService.host")
    lazy val port: Int    = config.getInt("fileService.port")
  }

  object VacancyEnricherConfig {
    lazy val host: VacancyEnricherClient.Host     = VacancyEnricherClient.Host(config.getString("vacancyEnricherService.host"))
    lazy val apiKey: VacancyEnricherClient.ApiKey = VacancyEnricherClient.ApiKey(config.getString("vacancyEnricherService.apiKey"))
  }

  object ApplicationService {
    lazy val host: String   = config.getString("applicationService.host")
    lazy val apiKey: String = config.getString("applicationService.apiKey")
  }

  object ApiKeyHasher {
    lazy val salt: String = config.getString("apiKeyHasher.salt")
  }

  object Dashboard {
    lazy val url: String = config.getString("dashboard.url")
  }

  object ReportingService {
    lazy val host: String = config.getString("reportingService.host")
  }

  object AddressService {
    lazy val host: String = config.getString("addressService.host")
  }
}
