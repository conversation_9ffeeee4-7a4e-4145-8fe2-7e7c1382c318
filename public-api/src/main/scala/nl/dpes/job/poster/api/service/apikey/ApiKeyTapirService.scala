package nl.dpes.job.poster.api.service.apikey

import cats.effect.Sync
import cats.syntax.applicativeError._
import cats.syntax.either._
import cats.syntax.flatMap._
import cats.syntax.functor._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.converter.Converter.{ConvertorOps, InvertorOps}
import nl.dpes.job.poster.api.{ErrorMessage, Forbidden, Unauthorized}
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.recruiter.RecruiterService.{RecruiterForbidden, RecruiterUnauthorized}
import nl.dpes.job.poster.api.service.recruiter.{AccessToken, RecruiterService}

trait ApiKeyTapirService[F[_]] {
  def generateKeyForIntegration(bearerToken: BearerToken)(input: api.IntegrationInput): F[Either[ErrorMessage, api.ApiKey]]
  def generateKey(bearerToken: BearerToken)(x: Unit): F[Either[ErrorMessage, api.ApiKey]]
  def getRecruiterKeys(bearerToken: BearerToken)(x: Unit): F[Either[ErrorMessage, List[api.ApiKeyInformation]]]
  def revokeKey(bearerToken: BearerToken)(id: api.ApiKeyId): F[Either[ErrorMessage, Unit]]
  def getRecruiterId(bearerToken: BearerToken)(apiKey: api.ApiKey): F[Either[ErrorMessage, SalesForceId]]
}

object ApiKeyTapirService {

  def apply[F[_]: Sync](recruiterService: RecruiterService[F], repository: ApiKeyRepository[F]): ApiKeyTapirService[F] =
    new ApiKeyTapirService[F] {

      override def generateKeyForIntegration(
        bearerToken: BearerToken
      )(input: api.IntegrationInput): F[Either[ErrorMessage, api.ApiKey]] =
        (for {
          recruiter <- recruiterService.getRecruiter(AccessToken(bearerToken.token))
          key       <- ApiKey.generate
          _         <- repository.storeKey(recruiter.salesforceId, key, input.integration.toDomain[Integration])
        } yield key.toApi[api.ApiKey].asRight[ErrorMessage]).recover {
          case RecruiterUnauthorized(message) => Unauthorized(message).asLeft
          case RecruiterForbidden(message)    => Forbidden(message).asLeft
        }

      override def getRecruiterId(bearerToken: BearerToken)(apiKey: api.ApiKey): F[Either[ErrorMessage, SalesForceId]] =
        repository.readRecruiterId(apiKey.toDomain[ApiKey]).map(_.asRight[ErrorMessage])

      override def getRecruiterKeys(bearerToken: BearerToken)(x: Unit): F[Either[ErrorMessage, List[api.ApiKeyInformation]]] =
        for {
          recruiter <- recruiterService.getRecruiter(AccessToken(bearerToken.token))
          keys      <- repository.readRecruiterKeyInformation(recruiter.salesforceId)
        } yield keys
          .map(_.toApi[api.ApiKeyInformation])
          .asRight[ErrorMessage]

      override def generateKey(bearerToken: BearerToken)(x: Unit): F[Either[ErrorMessage, api.ApiKey]] =
        generateKeyForIntegration(bearerToken)(api.IntegrationInput(api.Integration("Not specified")))

      override def revokeKey(bearerToken: BearerToken)(id: api.ApiKeyId): F[Either[ErrorMessage, Unit]] = for {
        recruiter <- recruiterService.getRecruiter(AccessToken(bearerToken.token))
        _         <- repository.removeKey(recruiter.salesforceId, id.toDomain[ApiKeyId])
      } yield ().asRight
    }
}
