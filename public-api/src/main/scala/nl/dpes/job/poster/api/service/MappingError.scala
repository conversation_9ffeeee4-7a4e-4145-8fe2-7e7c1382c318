package nl.dpes.job.poster.api.service

import cats.kernel.Semigroup
import nl.dpes.job.poster.api.service.MappingError._

case class MappingError(errors: List[(Cursor, String)]) extends Throwable {
  def combine(that: MappingError): MappingError = MappingError(this.errors ++ that.errors)

  override def toString: String = errors
    .map { case (cursor, message) =>
      cursor match {
        case _: Cursor.Field => RootError(cursor.root, s"$message at $cursor")
        case _: Cursor.Root  => RootError(cursor.root, message)
      }
    }
    .groupBy(_.root)
    .map(rootErrors => s"Invalid value for: ${rootErrors.root} (${rootErrors.errors.map(_.error).mkString(", ")})")
    .mkString("\n")

  override def getMessage: String = toString
}

object MappingError {
  def apply(keyValue: (Cursor, String)): MappingError = MappingError(List(keyValue))

  case class RootError(root: String, error: String)

  implicit class RootErrors(val rootErrors: (String, List[RootError])) extends AnyVal {
    def root: String           = rootErrors._1
    def errors: Seq[RootError] = rootErrors._2
  }

  implicit lazy val semigroup: Semigroup[MappingError] = Semigroup.instance(_ combine _)
}
