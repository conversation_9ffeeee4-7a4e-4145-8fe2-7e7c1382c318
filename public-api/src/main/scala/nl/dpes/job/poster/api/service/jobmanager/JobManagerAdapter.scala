package nl.dpes.job.poster.api.service
package jobmanager

import cats.data.Validated
import cats.implicits._
import nl.dpes.b2b.common.{CompanyType => JobManagerCompanyType}
import nl.dpes.b2b.domain.{
  Budget,
  IOL,
  ITB,
  JobConfiguration,
  NVB,
  PostingDefinition,
  PredefinedKey,
  Feature => JobManagerFeature,
  Site => JobManagerSite
}
import nl.dpes.b2b.jobmanager.domain.Occupation.{Occupations => JobManagerOccupations}
import nl.dpes.b2b.jobmanager.domain.salarytype.{ClassifiedSalary, Hourly, Monthly, Unspecified, Yearly}
import nl.dpes.b2b.jobmanager.domain.{
  ApplyVia,
  Content,
  Criteria,
  Huisstijl,
  InitialContract,
  InitialJobContent,
  JobContentLogo,
  JobContentVideo,
  PersonName,
  Address => JobManagerAddress,
  ApplyViaExternalWebsite => JobManagerViaWebsite,
  ApplyViaJobBoard => JobManagerViaJobBoard,
  Company => JobManagerCompany,
  Contact => JobManagerContact,
  ContactInformation => JobManagerContactInformation,
  ContractType => JobManagerContractType,
  ContractTypes => JobManagerContractTypes,
  DomesticAddress => JobManagerDomesticAddress,
  EasyApply => JobManagerViaEasyApply,
  EducationLevel => JobManagerEducationLevel,
  EducationLevels => JobManagerEducationLevels,
  ForeignAddress => JobManagerForeignAddress,
  Location => JobManagerLocation,
  Occupation => JobManagerOccupation,
  PostalAddress => JobManagerPostalAddress,
  Range => JobManagerRange
}
import nl.dpes.job.poster.api.service.job.{Job, JobUpdate}
import nl.dpes.job.poster.api.service.logo.LogoKey

import nl.dpes.job.poster.api.service.occupationclassification.Classification
import nl.dpes.job.poster.api.service.prediction.OccupationClassification
import nl.dpes.job.poster.api.service.shared.{
  Address,
  ApplicationMethod,
  ApplyViaExternalWebsite,
  ApplyViaJobBoard,
  CareerLevel,
  Company,
  CompanyType,
  Configuration,
  Contact,
  ContactInformation,
  ContractTypes,
  Date,
  DomesticAddress,
  EasyApply,
  EducationLevels,
  Feature,
  ForeignAddress,
  Hour,
  Html,
  IndustryCategories,
  JobCategories,
  Logo,
  Occupation,
  PostalAddress,
  PublicationPeriod,
  Range,
  SalaryPeriod,
  SalaryRange,
  Site,
  Video,
  Workplace,
  Zipcode
}

import java.time.LocalDate
import scala.concurrent.duration.Duration
import scala.util.{Failure, Success, Try}

object JobManagerAdapter {

  def mapToInitialJobContent(
    job: Job,
    maximumProductDuration: Duration,
    logoKey: Option[LogoKey],
    classification: Option[Classification]
  )(implicit
    cursor: Cursor
  ): Validated[MappingError, InitialJobContent] = (
    mapToContent(
      job.title,
      job.description,
      job.occupation,
      job.jobCategories,
      job.industryCategories,
      classification
    ),
    mapToCriteria(job.educationLevels, job.careerLevel),
    mapToInitialContract(job.contractTypes, job.workingHours, job.salary, job.workplace),
    mapToLocation(job.location),
    mapToPublicationPeriod(job.publicationPeriod, maximumProductDuration, createIfMissing = true)(cursor("publicationPeriod")),
    mapToApplicationMethod(job.applicationMethod)(cursor("applicationMethod")),
    mapToLogo(job.logo, logoKey),
    mapToVideo(job.video),
    None.valid, // tracking url will not be used by this application
    mapToCompany(job.company)(cursor("company")),
    mapToContactInformation(job.contactInformation),
    List().valid // custom fields will not be used by this application
  ).mapN(InitialJobContent.apply(_, _, _, _, _, _, _, _, _, _, _, _, None))
    .andThen { content =>
      Validated
        .cond(
          content.applyVia.getClass != classOf[
            JobManagerViaWebsite
          ] || content.contactInformation != JobManagerContactInformation.Unspecified,
          content,
          MappingError(cursor -> "When apply via external website is chosen the contact information is mandatory")
        )
    }

  def mapToInitialJobContent(
    job: JobUpdate,
    maximumProductDuration: Duration,
    logoKey: Option[LogoKey],
    classification: Option[Classification]
  )(implicit
    cursor: Cursor
  ): Validated[MappingError, InitialJobContent] = (
    mapToContent(
      job.title,
      job.description,
      job.occupation,
      job.jobCategories,
      job.industryCategories,
      classification
    ),
    mapToCriteria(job.educationLevels, job.careerLevel),
    mapToInitialContract(job.contractTypes, job.workingHours, job.salary, job.workplace),
    mapToLocation(job.location),
    mapToPublicationPeriod(job.publicationPeriod, maximumProductDuration, createIfMissing = false)(cursor("publicationPeriod")),
    mapToApplicationMethod(job.applicationMethod)(cursor("applicationMethod")),
    mapToLogo(job.logo, logoKey),
    mapToVideo(job.video),
    None.valid, // tracking url will not be used by this application
    mapToCompany(job.company)(cursor("company")),
    mapToContactInformation(job.contactInformation),
    List().valid // custom fields will not be used by this application
  ).mapN(InitialJobContent.apply(_, _, _, _, _, _, _, _, _, _, _, _, None))
    .andThen { content =>
      Validated
        .cond(
          content.applyVia.getClass != classOf[
            JobManagerViaWebsite
          ] || content.contactInformation != JobManagerContactInformation.Unspecified,
          content,
          MappingError(cursor -> "When apply via external website is chosen the contact information is mandatory")
        )
    }

  def mapToContent(
    title: Option[String],
    description: Option[Html],
    occupation: Option[Occupation],
    jobCategories: Option[JobCategories],
    industryCategories: Option[IndustryCategories],
    classification: Option[Classification]
  )(implicit cursor: Cursor): Validated[MappingError, Content] =
    (
      Validated.fromOption(title, MappingError(cursor("title") -> "missing field")),
      Validated.fromOption(description, MappingError(cursor("description") -> "missing field")).map(_.document),
      mapToOccupation(occupation, classification, cursor),
      Validated.fromOption(jobCategories, MappingError(cursor("jobCategories") -> "are required")).map(_.jobCategories.map(_.category)),
      Validated
        .valid(industryCategories.getOrElse(IndustryCategories.empty))
        .map(_.industryCategories.map(_.category))
    ).mapN(Content.apply)

  def mapToOccupation(
    occupation: Option[Occupation],
    classification: Option[Classification],
    cursor: Cursor
  ): Validated[MappingError, JobManagerOccupations] = {
    val givenOccupation = occupation.map(occ => JobManagerOccupation.Unclassified(occ.name)).valid
    val dpgco           = constructDpgco(classification, cursor)
    chooseValidClassification(givenOccupation, dpgco, cursor)
  }

  private def constructDpgco(
    classification: Option[Classification],
    cursor: Cursor
  ): Validated[MappingError, Option[JobManagerOccupation.Dpgco]] =
    classification.traverse { case OccupationClassification(label, description) =>
      val isco = JobManagerOccupation.Isco[Try](label)
      isco.flatMap(JobManagerOccupation.Dpgco[Try](description, _))
    } match {
      case Success(validOccupation) => validOccupation.valid
      case Failure(exception)       => MappingError(cursor("occupation") -> exception.getMessage).invalid
    }

  private def chooseValidClassification(
    givenOccupation: Validated[MappingError, Option[JobManagerOccupation]],
    computedOccupation: Validated[MappingError, Option[JobManagerOccupation]],
    cursor: Cursor
  ): Validated[MappingError, JobManagerOccupations] =
    (computedOccupation, givenOccupation)
      .traverseN((potentialComputedOcc, potentialGivenOcc) => potentialComputedOcc orElse potentialGivenOcc)
      .getOrElse(MappingError(cursor("occupation") -> "missing field").invalid)
      .andThen(occ =>
        JobManagerOccupations[Try](Set(occ)) match {
          case Success(value)     => value.valid
          case Failure(exception) => MappingError(cursor("occupation") -> exception.getMessage).invalid
        }
      )

  def mapToCriteria(educationLevels: Option[EducationLevels], careerLevel: Option[CareerLevel])(implicit
    cursor: Cursor
  ): Validated[MappingError, Criteria] = (
    Validated
      .fromOption(educationLevels, MappingError(cursor("educationLevels") -> "missing field"))
      .map(educationLevels =>
        JobManagerEducationLevels(educationLevels.levels.map(educationLevel => JobManagerEducationLevel(educationLevel.level)))
      ),
    Validated.fromOption(careerLevel, MappingError(cursor("careerLevel") -> "missing field")).map(level => Set(level.level)),
    None.valid
  ).mapN(Criteria.apply)

  def mapToInitialContract(
    contractTypes: Option[ContractTypes],
    workingHours: Option[Range[Hour]],
    salary: Option[SalaryRange],
    workplace: Option[Workplace]
  )(implicit
    cursor: Cursor
  ): Validated[MappingError, InitialContract] =
    (
      Validated
        .fromOption(contractTypes, MappingError(cursor("contractTypes") -> "missing field"))
        .map(types => JobManagerContractTypes(types.contractTypes.map(contractType => JobManagerContractType(contractType.contractType)))),
      workingHours.traverse(hours => mapToRange[Int](hours.lower.hour, hours.upper.hour, cursor("workingHours"))),
      workplace.map(_.workplace).valid,
      salary.traverse(mapToSalary)
    ).mapN(InitialContract.apply)

  def mapToSalary(salaryRange: SalaryRange)(implicit
    cursor: Cursor
  ): Validated[MappingError, ClassifiedSalary] = Validated
    .fromTry(salaryRange.period match {
      case SalaryPeriod.Unspecified => Unspecified[Try](salaryRange.lower.salary, salaryRange.upper.salary)
      case SalaryPeriod.Hour        => Hourly[Try](salaryRange.lower.salary, salaryRange.upper.salary)
      case SalaryPeriod.Month       => Monthly[Try](salaryRange.lower.salary, salaryRange.upper.salary)
      case SalaryPeriod.Year        => Yearly[Try](salaryRange.lower.salary, salaryRange.upper.salary)
    })
    .leftMap(e => MappingError(cursor("salary") -> e.getMessage))

  def mapToLocation(location: Option[Zipcode])(implicit cursor: Cursor): Validated[MappingError, JobManagerLocation] =
    Validated
      .fromOption(location, MappingError(cursor("location") -> "missing field"))
      .map(l => JobManagerLocation("NL", l.zipcode))

  def mapToLogo(logo: Option[Logo], logoKey: Option[LogoKey]): Validated[MappingError, Option[JobContentLogo]] =
    logoKey match {
      case Some(key) => Some(JobContentLogo(key.key, logo.map(_.url))).valid[MappingError]
      case None      => None.valid[MappingError]
    }

  def mapToRange[A: Ordering](lower: A, upper: A, cursor: Cursor): Validated[MappingError, JobManagerRange[A]] = Validated.fromEither(
    JobManagerRange(lower, upper).left.map(e => MappingError(cursor -> e))
  )

  def mapToPublicationPeriod(
    period: Option[PublicationPeriod],
    maximumDuration: Duration,
    createIfMissing: Boolean,
    startingToday: Date = Date.today
  )(implicit
    cursor: Cursor
  ): Validated[MappingError, Option[JobManagerRange[LocalDate]]] = {
    val validInput: Option[Validated[MappingError, PublicationPeriod]] = period.map(_.valid[MappingError])
    lazy val backupInput: Option[Validated[MappingError, PublicationPeriod]] =
      PublicationPeriod(startingToday).leftMap(MappingError(cursor, _)).some

    val periodToUse: Option[Validated[MappingError, PublicationPeriod]] =
      if (createIfMissing) validInput orElse backupInput
      else validInput

    periodToUse.traverse(
      _.andThen(publicationPeriod =>
        mapToRange(
          publicationPeriod.start.toLocalDate,
          publicationPeriod.endForDuration(maximumDuration).toLocalDate,
          cursor
        )
      )
    )
  }

  def mapToVideo(video: Option[Video]): Validated[MappingError, Option[JobContentVideo]] =
    video.traverse(video => JobContentVideo(video.url).valid[MappingError])

  def mapToApplicationMethod(applicationMethod: Option[ApplicationMethod])(implicit cursor: Cursor): Validated[MappingError, ApplyVia] =
    Validated.fromOption(applicationMethod, MappingError(cursor -> "missing field")).andThen {
      case ApplyViaJobBoard(firstName, lastName, emailAddress) =>
        Validated
          .fromTry(JobManagerViaJobBoard[Try](firstName, lastName, emailAddress))
          .leftMap(e => MappingError(cursor("ApplyViaJobBoard") -> e.getMessage))
      case ApplyViaExternalWebsite(website) =>
        Validated
          .fromTry(JobManagerViaWebsite[Try](website.url))
          .leftMap(e => MappingError(cursor("ApplyViaExternalWebsite") -> e.getMessage))
      case EasyApply => JobManagerViaEasyApply.valid
    }

  def mapToCompany(company: Option[Company])(implicit cursor: Cursor): Validated[MappingError, Option[JobManagerCompany]] =
    company.traverse(c =>
      (
        None.valid,
        mapToAddress(c.address)(cursor("address")),
        mapToCompanyType(c.companyType)(cursor("companyType")),
        None.valid,
        Huisstijl.CompanyDefault.valid,
        c.name.valid,
        None.valid,
        c.website.map(_.url).valid
      ).mapN(JobManagerCompany.apply)
    )

  def mapToContact(contact: Contact): Validated[MappingError, JobManagerContact] =
    JobManagerContact(PersonName(contact.name.firstName, contact.name.lastName), contact.phoneNumber, contact.emailAddress).valid

  def mapToContactInformation(
    contactInformation: Option[ContactInformation]
  )(implicit cursor: Cursor): Validated[MappingError, JobManagerContactInformation] =
    contactInformation
      .map(info =>
        (
          mapToContact(info.contact),
          info.address.traverse(mapToPostalAddress),
          info.website.valid
        ).mapN(JobManagerContactInformation.Specified)
      )
      .getOrElse(JobManagerContactInformation.Unspecified.valid)

  def mapToAddress(address: Option[Address])(implicit cursor: Cursor): Validated[MappingError, Option[JobManagerAddress]] =
    address.traverse {
      case address: PostalAddress => mapToPostalAddress(address)
      case DomesticAddress(street, houseNumber, houseNumberAddition, zipCode, city) =>
        JobManagerDomesticAddress(street, houseNumber, houseNumberAddition, city, zipCode).valid
      case ForeignAddress(address, country) => JobManagerForeignAddress(address, country.code).valid
    }

  def mapToPostalAddress(address: PostalAddress)(implicit cursor: Cursor): Validated[MappingError, JobManagerPostalAddress] = Validated
    .fromTry(JobManagerPostalAddress[Try](address.streetNameAndHouseNumber, address.city, address.zipCode))
    .leftMap(e => MappingError(cursor("PostalAddress") -> e.getMessage))

  def mapToCompanyType(companyType: CompanyType)(implicit cursor: Cursor): Validated[MappingError, JobManagerCompanyType] =
    Validated
      .fromTry(Try(JobManagerCompanyType(companyType.companyType.replace(" ", ""))))
      .leftMap(_ => MappingError(cursor, s"Unknown company type '${companyType.companyType}"))

  def mapToFeatures(features: Set[Feature])(implicit cursor: Cursor): Validated[MappingError, Set[JobManagerFeature]] = {
    import cats.instances.try_._
    import nl.dpes.b2b.domain.ErrorList.semigroupForThrowable
    features.toList
      .traverse(feature => Validated.fromTry(JobManagerFeature[Try](feature.name)))
      .map(_.toSet)
      .leftMap(e => MappingError(cursor("Features") -> e.getMessage))
  }

  def mapToConfiguration(jobConfiguration: Option[Configuration])(implicit cursor: Cursor): Validated[MappingError, JobConfiguration] =
    Validated
      .fromOption(jobConfiguration, MappingError(cursor -> "missing field"))
      .andThen {
        case shared.JobPosting(publishOn, publicationDuration, features) =>
          (
            publishOn.toList.zipWithIndex
              .traverse { case (site, index) => mapToSite(site)(cursor(s"publishOn[$index]")) }
              .map(_.toSet),
            publicationDuration.valid,
            mapToFeatures(features),
            Map.empty[PredefinedKey, String].valid
          ).mapN(PostingDefinition.apply)
        case shared.PerformanceBased(budget) => Budget(budget.budget).valid
      }

  def mapToSite(site: Site)(implicit cursor: Cursor): Validated[MappingError, JobManagerSite] = site match {
    case Site("Nationale Vacaturebank") => NVB.valid
    case Site("Intermediair")           => IOL.valid
    case Site("Tweakers Carrière")      => ITB.valid
    case Site(unknown)                  => MappingError(cursor -> s"unknown site '$unknown'").invalid
  }

  implicit class ErrorMessageOps(val message: String) extends AnyVal {

    def cleanGrpcError: String = {
      val pattern = "GRPC server was FAILED_PRECONDITION: '(.*)'".r
      message match {
        case pattern(error) => error
        case _              => message
      }
    }
  }
}
