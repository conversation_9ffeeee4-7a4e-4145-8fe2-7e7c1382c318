package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

case class ContractType private (contractType: String) extends AnyVal

object ContractType {

  implicit lazy val encoder: Encoder[ContractType] = Encoder.encodeString.contramap[ContractType](_.contractType)
  implicit lazy val decoder: Decoder[ContractType] = Decoder.decodeString.emap(ContractType(_).toEither)

  val validContractTypes: Set[String] = Set(
    "Interim",
    "Stage",
    "Tijdelijk",
    "Vast",
    "Leer-werk overeenkomst",
    "Vrijwilliger",
    "Bijbaan",
    "Zelfstandig/Franchise",
    "Freelance",
    "ZZP",
    "Vakantiewerk",
    "Thuiswerk"
  )

  def apply(contractType: String): Validated[String, ContractType] =
    if (validContractTypes.containsCaseInsensitive(contractType)) new ContractType(contractType).valid
    else s"Contract type '$contractType' is not valid.".invalid

  object SwaggerDoc {
    val interim   = new ContractType("Interim")
    val stage     = new ContractType("Stage")
    val tijdelijk = new ContractType("Tijdelijk")
  }
}
