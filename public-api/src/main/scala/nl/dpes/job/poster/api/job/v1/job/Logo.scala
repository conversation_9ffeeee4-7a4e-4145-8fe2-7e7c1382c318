package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.shared.{Logo => AppLogo}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}

case class Logo private (url: String) extends AnyVal

object Logo {
  implicit lazy val encoder: Encoder[Logo] = Encoder.encodeString.contramap(_.url)
  implicit lazy val decoder: Decoder[Logo] = Decoder.decodeString.emap(Logo(_).toEither)

  def apply(url: String): Validated[String, Logo] =
    Website(url).map(website => new Logo(website.url))

  def map(logo: Option[Logo])(implicit cursor: Cursor): Validated[MappingError, Option[AppLogo]] =
    logo
      .traverse(logo => AppLogo(logo.url))
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val example = new Logo("https://picsum.photos/200/300")
  }
}
