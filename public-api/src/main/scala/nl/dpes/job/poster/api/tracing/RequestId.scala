package nl.dpes.job.poster.api.tracing

import io.circe.Encoder
import sttp.tapir.Codec
import sttp.tapir.Codec.PlainCodec

import java.util.UUID

case class RequestId(value: String) extends AnyVal

object RequestId {
  def generate: RequestId = RequestId(UUID.randomUUID().toString)

  implicit lazy val codec: PlainCodec[RequestId]     = Codec.string.map(RequestId(_))(_.value)
  implicit lazy val circeEncoder: Encoder[RequestId] = Encoder.encodeString.contramap(_.value)
}
