package nl.dpes.job.poster.api.service

import cats.Parallel
import cats.effect.Sync
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.parallel._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.job.v1.job.JobStatus
import nl.dpes.job.poster.api.defaults.DefaultsRepository
import nl.dpes.job.poster.api.service.job.{Job, JobSummary, JobUpdate}
import nl.dpes.job.poster.api.service.jobmanager.JobManager
import nl.dpes.job.poster.api.service.recruiter.{AccessToken, RecruiterService}
import nl.dpes.job.poster.api.service.shared.{Configuration, JobId}
import nl.dpes.job.poster.api.tracing.CorrelationId
import org.typelevel.log4cats.LoggerFactory

trait JobPosterService[F[_]] {

  def create(
    salesForceId: SalesForceId,
    jobId: JobId,
    job: Job,
    configuration: Option[Configuration],
    correlationId: CorrelationId
  ): F[Unit]

  def read(salesForceId: SalesForceId, jobId: JobId): F[JobSummary]
  def update(recruiterId: SalesForceId, jobId: JobId, job: JobUpdate): F[Unit]
  def delete(recruiterId: SalesForceId, jobId: String): F[Unit]
  def updateJobStatus(recruiterId: SalesForceId, jobId: String, status: JobStatus): F[Unit]
}

object JobPosterService {

  def apply[F[_]: Sync: Parallel](
    jobManager: JobManager[F],
    recruiterService: RecruiterService[F],
    defaultsRepository: DefaultsRepository[F],
    loggerFactory: LoggerFactory[F]
  ): JobPosterService[F] = new JobPosterService[F] {

    override def create(
      recruiterId: SalesForceId,
      jobId: JobId,
      job: Job,
      configuration: Option[Configuration],
      correlationId: CorrelationId
    ): F[Unit] =
      for {
        (recruiter, defaults) <- (recruiterService.getRecruiter(recruiterId), defaultsRepository.readDefaults(recruiterId)).parTupled
        _                     <- jobManager.postJob(AccessToken.JPA, jobId, job, configuration, defaults, recruiter, correlationId)
      } yield ()

    override def update(recruiterId: SalesForceId, jobId: JobId, job: JobUpdate): F[Unit] = for {
      recruiter <- recruiterService.getRecruiter(recruiterId)
      defaults  <- defaultsRepository.readDefaults(recruiterId)
      _         <- jobManager.updateJob(AccessToken.JPA, jobId, job, defaults, recruiter)
    } yield ()

    override def delete(recruiterId: SalesForceId, jobId: String): F[Unit] = for {
      _ <- jobManager.deleteJob(AccessToken.JPA, JobId(jobId), recruiterId)
    } yield ()

    override def updateJobStatus(recruiterId: SalesForceId, jobId: String, status: JobStatus): F[Unit] = for {
      _ <- jobManager.updateJobStatus(AccessToken.JPA, JobId(jobId), recruiterId, status)
    } yield ()

    override def read(salesForceId: SalesForceId, jobId: JobId): F[JobSummary] =
      jobManager.getJob(AccessToken.JPA, salesForceId, jobId)
  }
}
