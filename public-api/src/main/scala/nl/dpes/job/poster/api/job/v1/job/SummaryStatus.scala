package nl.dpes.job.poster.api.job.v1.job

import cats.implicits.none
import io.circe.Encoder
import nl.dpes.job.poster.api.service.shared
import nl.dpes.job.poster.api.service.shared.JobStatus
import sttp.tapir.Schema

sealed trait SummaryStatus

object SummaryStatus {
  case object ValidDraft extends SummaryStatus

  case object Expired extends SummaryStatus

  case object MultiStepDraft extends SummaryStatus

  case object Draft extends SummaryStatus

  case object Published extends SummaryStatus

  case object Suspended extends SummaryStatus

  case object Rejected extends SummaryStatus

  case object Approved extends SummaryStatus

  case object Submitted extends SummaryStatus

  case object Deleted extends SummaryStatus

  def fromJobStatus(status: shared.JobStatus): SummaryStatus = status match {
    case JobStatus.ValidDraft     => ValidDraft
    case JobStatus.Expired        => Expired
    case JobStatus.MultiStepDraft => MultiStepDraft
    case JobStatus.Draft          => Draft
    case JobStatus.Published      => Published
    case JobStatus.Suspended      => Suspended
    case JobStatus.Rejected       => Rejected
    case JobStatus.Approved       => Approved
    case JobStatus.Submitted      => Submitted
    case JobStatus.Deleted        => Deleted
  }

  implicit lazy val jsonEncoder: Encoder[SummaryStatus] = Encoder.encodeString.contramap(_.toString)

  implicit lazy val schema: Schema[SummaryStatus] = Schema.schemaForString.map(_ => none[SummaryStatus])(status => s"$status")
}
