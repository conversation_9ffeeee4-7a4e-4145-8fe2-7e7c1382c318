package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import io.circe.{Decoder, Encoder}
import cats.implicits._
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{Video => AppVideo}

case class Video private (url: String) extends AnyVal

object Video {
  implicit lazy val encoder: Encoder[Video] = Encoder.encodeString.contramap(_.url)
  implicit lazy val decoder: Decoder[Video] = Decoder.decodeString.emap(Video(_).toEither)

  def validateUrl(url: String): Either[String, String] = {
    val youtube1 = """.*youtu\.be/([^?$/#]+).*""".r
    val youtube2 = """.*\.youtube.com.*[?&]v=([^&$#]+).*""".r
    val youtube3 = """.*\.youtube.com/embed/([^&$#/]+).*""".r
    val youtube4 = """.*\.youtube.com/v/([^&$#/]+).*""".r
    val vimeo1   = """.*player.vimeo.com/video/([\d]+).*""".r
    val vimeo2   = """.*vimeo.com/([\d]+).*""".r

    url match {
      case youtube1(_) => Right(url)
      case youtube2(_) => Right(url)
      case youtube3(_) => Right(url)
      case youtube4(_) => Right(url)
      case vimeo1(_)   => Right(url)
      case vimeo2(_)   => Right(url)
      case _           => Left(s"Cannot parse url '$url'.")
    }
  }

  def apply(url: String): Validated[String, Video] = validateUrl(url).toValidated
    .andThen(url => Website(url))
    .map(website => new Video(website.url))

  def map(video: Option[Video])(implicit cursor: Cursor): Validated[MappingError, Option[AppVideo]] =
    video
      .traverse(video => AppVideo(video.url))
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val example = new Video("https://www.youtube.com/watch?time_continue=4&v=jobvideo")
  }
}
