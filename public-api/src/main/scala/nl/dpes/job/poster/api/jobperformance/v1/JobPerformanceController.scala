package nl.dpes.job.poster.api.jobperformance.v1

import cats.effect.Async
import cats.implicits._
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import sttp.tapir.server.ServerEndpoint
import sttp.tapir._
import sttp.tapir.json.circe._
import sttp.tapir.generic.auto._
import io.circe.generic.auto._
import nl.dpes.job.poster.api.{Unauthorized, Unknown}
import nl.dpes.job.poster.api.serverOptions
import org.http4s.HttpRoutes
import sttp.model.StatusCode
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter

trait JobPerformanceController[F[_]] {
  def getJobsPerformance: F[ServerEndpoint[Any, F]]

  def routes: F[HttpRoutes[F]]

  def endpoints: F[List[ServerEndpoint[Any, F]]]
}

object JobPerformanceController {

  def impl[F[_]: Async](
    authenticationService: AuthenticationService[F],
    jobPerformanceService: JobPerformanceService[F]
  ): JobPerformanceController[F] =
    new JobPerformanceController[F] {

      override def getJobsPerformance: F[ServerEndpoint[Any, F]] = Async[F].delay {
        jobPerformanceClient
          .baseEndpoint(
            authenticationService.authenticate,
            _.errorOut(
              oneOf(
                oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
                oneOfVariant[Unknown](statusCode(StatusCode.InternalServerError).and(jsonBody[Unknown]))
              )
            )
          )
          .get
          .description("Returns performance metrics for all jobs.")
          .in("performance")
          .out(jsonBody[List[JobPerformance]])
          .serverLogic(jobPerformanceService.getJobsPerformance)
      }

      override def routes: F[HttpRoutes[F]] = for {
        endpoint <- List(getJobsPerformance).sequence
        swaggerRoutes <- Async[F].delay(
          Http4sServerInterpreter[F]()
            .toRoutes(
              SwaggerInterpreter(swaggerUIOptions =
                SwaggerUIOptions(
                  List("docs", "job-performance", jobPerformanceClient.version),
                  "docs.yaml",
                  Nil,
                  useRelativePaths = false,
                  showExtensions = false,
                  initializerOptions = None,
                  oAuthInitOptions = None
                )
              )
                .fromServerEndpoints[F](endpoint, "Job Applications", jobPerformanceClient.version)
            )
        )
        endpointRoutes <- Async[F].delay(Http4sServerInterpreter[F](serverOptions).toRoutes(endpoint))
      } yield swaggerRoutes <+> endpointRoutes

      override def endpoints: F[List[ServerEndpoint[Any, F]]] = List(getJobsPerformance).sequence
    }
}
