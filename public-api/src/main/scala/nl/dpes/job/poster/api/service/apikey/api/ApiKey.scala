package nl.dpes.job.poster.api.service.apikey.api

import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.converter.Converter
import nl.dpes.job.poster.api.service.apikey.{ApiKey => Domain}

final case class ApiKey(key: String) extends AnyVal

object ApiKey {
  implicit lazy val keyEncoder: Encoder[ApiKey] = deriveEncoder[ApiKey]
  implicit lazy val keyDecoder: Decoder[ApiKey] = deriveDecoder[ApiKey]

  implicit lazy val converter: Converter[Api<PERSON><PERSON>, Domain] = new Converter[Api<PERSON>ey, Domain] {
    override def convert(from: ApiKey): Domain = Domain(from.key)

    override def invert(from: Domain): ApiKey = ApiKey(from.key)
  }

  def authenticationDescription(dashboardUrl: String): String =
    s"""
       |You can obtain the API Key to start using the Job Poster API, by following these steps:
       |
       |##### Step 1: Login to the [Persgroep Digital dashboard]($dashboardUrl).
       |
       |##### Step 2: Generate an API Key using this [tool]($dashboardUrl/job-poster/api-key) and copy it.
       |
       |##### Step 3: Paste the generated API Key below and click on **Authorize**.
       |
       |""".stripMargin
}
