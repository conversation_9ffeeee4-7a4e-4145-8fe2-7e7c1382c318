package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.shared

import java.time.{Instant, LocalDate}
import java.time.format.DateTimeFormatter
import scala.concurrent.duration.Duration
import scala.util.matching.Regex
import scala.util.{Failure, Try}

case class Date private (value: String) extends AnyVal {

  import Date.formatter

  override def toString: String = value

  def plus(duration: Duration): Date = {
    val date: LocalDate = LocalDate.parse(value, formatter)

    new Date(date.plusDays(duration.toDays).format(formatter))
  }

  def toLocalDate: LocalDate = LocalDate.parse(value, formatter)
}

object Date {

  implicit lazy val encoder: Encoder[Date] = Encoder.encodeString.contramap(_.value)
  implicit lazy val decoder: Decoder[Date] = Decoder.decodeString.emap(Date(_).toEither)

  def today: Date = new Date(formatter.format(LocalDate.now()))

  val formatter: DateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE
  val dateFormat: Regex            = "(\\d{4})-(\\d{1,2})-(\\d{1,2})".r

  def apply(date: String): Validated[String, Date] = Validated
    .fromTry(date match {
      case dateFormat(year, month, day) => Try(new Date(LocalDate.of(year.toInt, month.toInt, day.toInt).format(formatter)))
      case unknown                      => Failure(new Throwable(s"Cannot parse '$unknown'"))
    })
    .leftMap(_.getMessage)

  def fromService(date: shared.Date) = new Date(date.value)

  val startDate: LocalDate = LocalDate.now().plusDays(5)
  val endDate: LocalDate   = startDate.plusDays(30)

  object SwaggerDoc {
    val start = new Date(startDate.format(formatter))
    val end   = new Date(endDate.format(formatter))
  }
}
