package nl.dpes.job.poster.api.job.v1.job

import io.circe.Encoder
import io.circe.generic.semiauto.deriveEncoder
import nl.dpes.job.poster.api.service.job
case class JobSummary(title: JobTitle, status: SummaryStatus, publicationPeriod: PublicationPeriod)

object JobSummary {

  def fromService(summary: job.JobSummary): JobSummary = JobSummary(
    JobTitle.fromService(summary.title),
    SummaryStatus.fromJobStatus(summary.status),
    PublicationPeriod.fromService(summary.publicationPeriod)
  )

  implicit lazy val jsonEncoder: Encoder[JobSummary] = deriveEncoder
}
