package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{ContractType => AppContractType}
case class ContractType private (contractType: String) extends AnyVal

object ContractType {

  implicit lazy val encoder: Encoder[ContractType] = Encoder.encodeString.contramap[ContractType](_.contractType)
  implicit lazy val decoder: Decoder[ContractType] = Decoder.decodeString.emap(ContractType(_).toEither)

  val validContractTypes: Set[String] = Set(
    "Interim",
    "Stage",
    "Tijdelijk",
    "Vast",
    "Leer-werk overeenkomst",
    "Vrijwilliger",
    "<PERSON><PERSON>j<PERSON><PERSON>",
    "Zelfstandig/Franchise",
    "Freelance",
    "ZZ<PERSON>",
    "<PERSON><PERSON>ntiewerk",
    "Thuiswerk"
  )

  def apply(contractType: String): Validated[String, ContractType] =
    if (validContractTypes.containsCaseInsensitive(contractType)) new ContractType(contractType).valid
    else s"Contract type '$contractType' is not valid.".invalid

  def map(contractType: Option[ContractType])(implicit cursor: Cursor): Validated[MappingError, Option[AppContractType]] =
    contractType
      .traverse(contract => AppContractType(contract.contractType))
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val interim   = new ContractType("Interim")
    val stage     = new ContractType("Stage")
    val tijdelijk = new ContractType("Tijdelijk")
  }
}
