package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.syntax.either._
import cats.syntax.validated._
import io.circe._
import io.circe.syntax._

case class SalaryRange private (lower: Salary, upper: Salary, period: SalaryPeriod)

object SalaryRange {

  def apply(lower: Salary, upper: Salary, period: SalaryPeriod): Validated[String, SalaryRange] =
    if (lower > upper) s"Lower salary '${lower.salary}' cannot be more than upper salary '${upper.salary}'".invalid
    else new SalaryRange(lower, upper, period).valid

  implicit lazy val encoder: Encoder[SalaryRange] = (range: SalaryRange) =>
    Json.obj(
      "lower"  -> range.lower.asJson,
      "upper"  -> range.upper.asJson,
      "period" -> range.period.asJson
    )

  implicit lazy val decoder: Decoder[SalaryRange] = (c: HCursor) =>
    for {
      lower  <- c.downField("lower").as[Salary]
      upper  <- c.downField("upper").as[Salary]
      period <- c.downField("period").as[SalaryPeriod]
      range  <- SalaryRange(lower, upper, period).toEither.leftMap(e => DecodingFailure(e, c.history))
    } yield range

  object SwaggerDoc {
    val example = new SalaryRange(Salary.SwaggerDoc.minSalaryExample, Salary.SwaggerDoc.maxSalaryExample, SalaryPeriod.Month)
  }
}
