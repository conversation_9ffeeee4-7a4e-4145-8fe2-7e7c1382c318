package nl.dpes.job.poster.api.service.authentication

import cats.MonadThrow
import cats.syntax.applicativeError._
import cats.syntax.either._
import cats.syntax.flatMap._
import cats.syntax.functor._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.{ErrorMessage, Forbidden, Unauthorized}
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.apikey.ApiKeyRepository._
import nl.dpes.job.poster.api.service.apikey.{ApiKey, ApiKeyRepository}
import nl.dpes.job.poster.api.service.cache.CacheFactory
import nl.dpes.job.poster.api.service.recruiter.RecruiterService.{RecruiterForbidden, RecruiterUnauthorized}

trait AuthenticationService[F[_]] {
  def authenticateByApiKey(bearerToken: BearerToken): F[SalesForceId]
  def authenticate(bearerToken: BearerToken): F[Either[ErrorMessage, SalesForceId]]
}

object AuthenticationService {

  def impl[F[_]: MonadThrow](
    keyRepository: ApiKeyRepository[F]
  )(implicit cacheFactory: CacheFactory[F]): F[AuthenticationService[F]] = {

    def authProcess(bearerToken: BearerToken): F[SalesForceId] = for {
      apiKey      <- ApiKey(bearerToken)
      recruiterId <- keyRepository.readRecruiterId(apiKey)
    } yield recruiterId

    for {
      cachedProcess <- cacheFactory.create(authProcess)
    } yield new AuthenticationService[F] {

      override def authenticateByApiKey(bearerToken: BearerToken): F[SalesForceId] = cachedProcess(bearerToken)

      override def authenticate(bearerToken: BearerToken): F[Either[ErrorMessage, SalesForceId]] =
        authenticateByApiKey(bearerToken).map(_.asRight[ErrorMessage]).recover {
          case ApiKeyEntryNotFound =>
            Unauthorized(s"Unknown API key").asLeft
          case RecruiterUnauthorized(message) =>
            Unauthorized(s"Error retrieving recruiter: '$message'").asLeft
          case RecruiterForbidden(message) =>
            Forbidden(s"Error retrieving recruiter: '$message'").asLeft
        }
    }
  }
}
