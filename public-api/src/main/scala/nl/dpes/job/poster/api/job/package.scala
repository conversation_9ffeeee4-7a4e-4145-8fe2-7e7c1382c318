package nl.dpes.job.poster.api

import cats.effect.kernel.Async
import nl.dpes.job.poster.api.config.Configuration
import nl.dpes.job.poster.api.ErrorMessage
import nl.dpes.job.poster.api.service.BearerToken
import nl.dpes.job.poster.api.service.apikey.api.ApiKey.authenticationDescription
import nl.dpes.job.poster.api.tracing.{CorrelationId, RequestId}
import sttp.tapir._
import sttp.tapir.server.PartialServerEndpoint

package object job extends Configuration {

  case class JobEndpoint(version: String) {

    def baseEndpoint[F[_]: Async, Principal, E <: ErrorMessage](
      securityLogic: BearerToken => F[Either[E, Principal]],
      expectedErrors: Endpoint[Unit, Unit, Unit, Unit, Any] => Endpoint[Unit, Unit, E, Unit, Any]
    ): PartialServerEndpoint[BearerToken, Principal, (RequestId, CorrelationId), E, Unit, Any, F] =
      expectedErrors(endpoint.in("api" / version))
        .securityIn(auth.bearer[String]().mapTo[BearerToken].description(authenticationDescription(Dashboard.url)))
        .in(header[RequestId]("X-Request-ID").schema(_.hidden(true)))
        .in(
          header[Option[CorrelationId]]("X-Correlation-ID")
            .map(CorrelationId.generateIfNotProvided)
            .schema(_.hidden(true))
        )
        .serverSecurityLogic(securityLogic)
  }
}
