package nl.dpes.job.poster.api.jobperformance.v1

import cats.implicits._
import cats.effect.Async
import nl.dpes.job.poster.api.job.v1.job.JobId
import nl.dpes.job.poster.api.reference_id.ReferenceIdService
import nl.dpes.job.poster.api.service.job.ReferenceId

case class Performance(`job_id`: String, `detail_view`: Int, `apply_click`: Int, `finished_apply`: Int)

object Performance {

  def map[F[_]: Async](performance: Performance)(referenceIdService: ReferenceIdService[F]): F[JobPerformance] =
    for {
      referenceId <- referenceIdService.getReferenceId(JobId(performance.`job_id`))
    } yield JobPerformance(
      jobId = performance.`job_id`,
      referenceId = referenceId.map(ref => ReferenceId(ref.value)),
      jobViews = performance.`detail_view`,
      jobApplications = performance.`apply_click`,
      finishedJobApplications = performance.`finished_apply`
    )
}
