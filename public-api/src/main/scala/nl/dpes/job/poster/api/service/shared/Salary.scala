package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import cats.implicits._
import io.circe.{Decoder, Encoder}

case class Salary private (salary: Float) extends AnyVal

object Salary {

  implicit lazy val encoder: Encoder[Salary]            = Encoder.encodeFloat.contramap[Salary](_.salary)
  implicit lazy val decoder: Decoder[Salary]            = Decoder.decodeFloat.emap(Salary(_).toEither)
  implicit lazy val ordering: Ordering[Salary]          = Ordering.by(_.salary)
  implicit def orderedSalary: Salary => Ordered[Salary] = Ordered.orderingToOrdered[Salary]

  def apply(salary: Float): Validated[String, Salary] =
    if (salary < 1) f"Salary '$salary%1.2f' cannot be less than 1.00.".invalid
    else new Salary(salary).valid

  object SwaggerDoc {
    val minSalaryExample = new Salary(10000)
    val maxSalaryExample = new Salary(30000)
  }
}
