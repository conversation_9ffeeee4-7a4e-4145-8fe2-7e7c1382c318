package nl.dpes.job.poster.api.tracing

import cats.syntax.option._
import sttp.tapir._

import java.util.UUID
import scala.collection.{IterableOps => _}

case class CorrelationId(value: String) extends AnyVal

object CorrelationId {
  def generate: CorrelationId = CorrelationId(UUID.randomUUID().toString)

  implicit lazy val generateIfNotProvided: Mapping[Option[CorrelationId], CorrelationId] =
    Mapping.from[Option[CorrelationId], CorrelationId](_ getOrElse generate)(_.some)
}
