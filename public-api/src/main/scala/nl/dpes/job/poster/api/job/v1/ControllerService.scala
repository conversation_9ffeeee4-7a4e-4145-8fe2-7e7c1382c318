package nl.dpes.job.poster.api
package job.v1

import cats.MonadThrow
import cats.data.Validated._
import cats.data._
import cats.effect.kernel.MonadCancelThrow
import cats.implicits._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.job.v1.job.ReferenceId.ReferenceIdError
import nl.dpes.job.poster.api.job.v1.job.{
  City,
  Geolocation,
  Job,
  JobCreated,
  JobId,
  JobStatus,
  JobSummary,
  JobUpdate,
  Location,
  ReferenceId,
  ApplicationMethod => ApiApplicationMethod,
  CareerLevel => ApiCareerLevel,
  Company => ApiCompany,
  Configuration => ApiConfiguration,
  ContactInformation => ApiContactInformation,
  ContractTypes => ApiContractTypes,
  EducationLevels => ApiEducationLevels,
  Html => ApiHtml,
  IndustryCategories => ApiIndustryCategories,
  JobCategories => ApiJobCategories,
  Logo => ApiLogo,
  Occupation => ApiOccupation,
  PublicationPeriod => ApiPublicationPeriod,
  Range => ApiRange,
  SalaryRange => ApiSalaryRange,
  Video => ApiVideo,
  Workplace => ApiWorplace,
  Zipcode => ApiLocation
}
import nl.dpes.job.poster.api.reference_id.{ReferenceIdService, JobId => RefJobId}
import nl.dpes.job.poster.api.{BadRequest, Conflict, ErrorMessage, NotFound, ServiceUnavailable, Timeout}
import nl.dpes.job.poster.api.service.addressservice.JobAddressService
import nl.dpes.job.poster.api.service.job.{Job => ApplicationJob, JobUpdate => ApplicationJobUpdate}
import nl.dpes.job.poster.api.service.jobmanager.JobManager
import nl.dpes.job.poster.api.service.jobmanager.JobManager.{
  logger,
  InvalidData,
  JobCannotBeResumed,
  JobCannotBeSuspended,
  JobCannotBeUpdated,
  JobNotFound,
  NoJobOwnership,
  NoSuitableCredits,
  NonUpdatableJob,
  NotAJob
}
import nl.dpes.job.poster.api.service.logo.LogoService.LogoServiceError
import nl.dpes.job.poster.api.service.occupationclassification.OccupationClassifier.{UnclassifiedPBPJob, UnrecognizedJob}
import nl.dpes.job.poster.api.service.shared.{JobStatus => AppJobStatus}
import nl.dpes.job.poster.api.service.shared.JobStatus.{Deleted, Expired}
import nl.dpes.job.poster.api.service.shared.{Configuration, Zipcode}
import nl.dpes.job.poster.api.service.uuid.UuidGenerator
import nl.dpes.job.poster.api.service.{shared, Cursor, JobPosterServiceFactory, MappingError}
import nl.dpes.job.poster.api.shutdownguard.ShutdownGuard
import nl.dpes.job.poster.api.tracing.{CorrelationId, CorrelationLoggerFactory, RequestId}
import org.typelevel.log4cats.{Logger, LoggerFactory}

trait ControllerService[F[_]] {

  def create(recruiterId: SalesForceId)(
    data: (RequestId, CorrelationId, Job)
  ): F[Either[ErrorMessage, (String, RequestId, JobCreated)]]

  def read(recruiterId: SalesForceId)(
    data: (RequestId, CorrelationId, JobId)
  ): F[Either[ErrorMessage, (JobSummary, RequestId)]]

  def update(recruiterId: SalesForceId)(
    data: (RequestId, CorrelationId, JobId, JobUpdate)
  ): F[Either[ErrorMessage, Unit]]

  def delete(recruiterId: SalesForceId)(data: (RequestId, CorrelationId, JobId)): F[Either[ErrorMessage, Unit]]

  def updateJobStatus(recruiterId: SalesForceId)(
    data: (RequestId, CorrelationId, JobId, JobStatus)
  ): F[Either[ErrorMessage, Unit]]
}

object ControllerService {

  case class NonRepostableJob(referenceId: ReferenceId, jobId: RefJobId, status: AppJobStatus)
      extends Throwable(s"Job '$referenceId/$jobId' with '$status' status cannot be reposted because it's not expired or deleted")

  private def mapZipcode[F[_]: MonadThrow](location: Option[Location], addressService: JobAddressService[F])(implicit
    cursor: Cursor
  ): F[Validated[MappingError, Option[Zipcode]]] =
    (location flatTraverse {
      case zipcode: ApiLocation => MonadThrow[F].fromValidated(ApiLocation.map(zipcode.some)(cursor("location")))
      case geolocation: Geolocation =>
        addressService
          .getZipcodeByGeolocation(geolocation)
          .map(addressInfo => Zipcode(addressInfo.zipCode).some)
      case city: City =>
        addressService
          .getZipcodeByCity(city)
          .map(addressInfo => Zipcode(addressInfo.zipCode).some)
    }).attempt
      .map(_.leftMap(thr => MappingError(cursor -> thr.getMessage)))
      .map(_.toValidated)

  def mapJob[F[_]: MonadThrow](job: Job, addressService: JobAddressService[F])(implicit
    cursor: Cursor
  ): F[ApplicationJob] =
    mapZipcode(job.location, addressService).flatMap(zipcode =>
      (
        Valid(job.title),
        ApiHtml.map(job.description)(cursor("description")),
        ApiOccupation.map(job.occupation)(cursor("occupation")),
        ApiJobCategories.map(job.jobCategories)(cursor("jobCategories")),
        ApiIndustryCategories.map(job.industryCategories)(cursor("industryCategories")),
        ApiEducationLevels.map(job.educationLevels)(cursor("educationLevels")),
        ApiCareerLevel.map(job.careerLevel)(cursor("careerLevel")),
        ApiContractTypes.map(job.contractTypes)(cursor("contractTypes")),
        ApiWorplace.map(job.workplace)(cursor("workplace")),
        ApiRange.mapWorkingHours(job.workingHours)(cursor("workingHours")),
        ApiSalaryRange.map(job.salary)(cursor("salary")),
        zipcode,
        ApiPublicationPeriod.map(job.publicationPeriod)(cursor("publicationPeriod")),
        ApiApplicationMethod.map(job.applicationMethod)(cursor("applicationMethod")),
        ApiLogo.map(job.logo)(cursor("logo")),
        ApiVideo.map(job.video)(cursor("video")),
        ApiCompany.map(job.company)(cursor("company")),
        job.contactInformation.map(ApiContactInformation.mapToService).valid
      ).mapN(ApplicationJob.apply) match {
        case Valid(validJob) => validJob.pure[F]
        case Invalid(thr)    => thr.raiseError[F, ApplicationJob]
      }
    )

  def mapJobUpdate[F[_]: MonadThrow](job: JobUpdate, addressService: JobAddressService[F])(implicit
    cursor: Cursor
  ): F[ApplicationJobUpdate] =
    mapZipcode(job.location, addressService).flatMap(zipcode =>
      (
        Valid(job.title),
        ApiHtml.map(job.description)(cursor("description")),
        ApiOccupation.map(job.occupation)(cursor("occupation")),
        ApiJobCategories.map(job.jobCategories)(cursor("jobCategories")),
        ApiIndustryCategories.map(job.industryCategories)(cursor("industryCategories")),
        ApiEducationLevels.map(job.educationLevels)(cursor("educationLevels")),
        ApiCareerLevel.map(job.careerLevel)(cursor("careerLevel")),
        ApiContractTypes.map(job.contractTypes)(cursor("contractTypes")),
        ApiWorplace.map(job.workplace)(cursor("workplace")),
        ApiRange.mapWorkingHours(job.workingHours)(cursor("workingHours")),
        ApiSalaryRange.map(job.salary)(cursor("salary")),
        zipcode,
        ApiPublicationPeriod.map(job.publicationPeriod)(cursor("publicationPeriod")),
        ApiApplicationMethod.map(job.applicationMethod)(cursor("applicationMethod")),
        ApiLogo.map(job.logo)(cursor("logo")),
        ApiVideo.map(job.video)(cursor("video")),
        ApiCompany.map(job.company)(cursor("company")),
        job.contactInformation.map(ApiContactInformation.mapToService).valid
      ).mapN(ApplicationJobUpdate.apply) match {
        case Valid(validJob) => validJob.pure[F]
        case Invalid(thr)    => thr.raiseError[F, ApplicationJobUpdate]
      }
    )

  def apply[F[_]: MonadCancelThrow: LoggerFactory](
    jobPosterServiceFactory: JobPosterServiceFactory[F],
    uuidGenerator: UuidGenerator[F],
    addressService: JobAddressService[F],
    referenceIdService: ReferenceIdService[F],
    shutdownGuard: ShutdownGuard[F]
  ): ControllerService[F] =
    new ControllerService[F] {

      override def create(
        credentials: SalesForceId
      )(data: (RequestId, CorrelationId, Job)): F[Either[ErrorMessage, (String, RequestId, JobCreated)]] =
        shutdownGuard.run {
          implicit val cursor: Cursor         = Cursor("body")
          val (requestId, correlationId, job) = data

          val loggerFactory = CorrelationLoggerFactory.apply[F](correlationId, requestId)

          uuidGenerator.generate
            .map(id => JobId(id.toString))
            .flatMap { id =>
              val result: F[Either[ErrorMessage, (String, RequestId, JobCreated)]] =
                for {
                  id                  <- uuidGenerator.generate.map(id => JobId(id.toString))
                  logger              <- loggerFactory.fromClass(getClass)
                  _                   <- logger.info(s"Job posting request was received for job content: '$data'")
                  recruiterId         <- credentials.pure[F]
                  mappedJob           <- mapJob(job, addressService)
                  mappedConfiguration <- MonadThrow[F].fromValidated(ApiConfiguration.map(job.configuration)(cursor("configuration")))
                  jobRef              <- chooseJobReference(JobId(id.value), job.referenceId)(logger)
                  _                   <- postJob(correlationId, loggerFactory, id, job.referenceId, recruiterId, mappedJob, mappedConfiguration)
                  _                   <- logger.info(s"Job posting request was sent successfully for job '$id'")
                } yield (s"/api/${jobEndpoint.version}/job/$jobRef", RequestId(requestId.value), JobCreated(id))
                  .asRight[ErrorMessage]

              result.recoverWith {
                case InvalidData(message) =>
                  MonadThrow[F].pure(BadRequest(s"Job manager did not accept the data: '$message'").asLeft[(String, RequestId, JobCreated)])
                case e: MappingError => MonadThrow[F].pure(BadRequest(e.getMessage).asLeft[(String, RequestId, JobCreated)])
                case NoSuitableCredits(message) =>
                  MonadThrow[F].pure(
                    Forbidden(s"We couldn't post your job as you didn't have sufficient credits of the right type to post")
                      .asLeft[(String, RequestId, JobCreated)]
                  )
                case NotAJob(message) => MonadThrow[F].pure(Teapot(message).asLeft[(String, RequestId, JobCreated)])
                case ex: NonRepostableJob =>
                  MonadThrow[F].pure(StatusConflict(ex.getMessage, ex.status).asLeft[(String, RequestId, JobCreated)])
                case ex: UnclassifiedPBPJob.type => MonadThrow[F].pure(BadRequest(ex.getMessage).asLeft[(String, RequestId, JobCreated)])
                case ex: UnrecognizedJob.type    => MonadThrow[F].pure(BadRequest(ex.getMessage).asLeft[(String, RequestId, JobCreated)])
                case JobManager.Timeout(message) =>
                  MonadThrow[F].pure(Timeout(s"A timeout occurred while handing job '${id.value}'").asLeft[(String, RequestId, JobCreated)])
                case error: LogoServiceError => MonadThrow[F].pure(BadRequest(error.getMessage).asLeft[(String, RequestId, JobCreated)])
                case thr: Throwable =>
                  for {
                    logger <- loggerFactory.fromClass(getClass)
                    _ <- logger.error(
                      s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] An error occurred while posting the job: ${thr.getMessage}"
                    )
                    _ <- logger.error(
                      s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] Stacktrace: ${thr.getStackTrace.mkString("\n")}"
                    )
                    res <- MonadThrow[F].pure(Unknown("An error occurred while posting the job").asLeft[(String, RequestId, JobCreated)])
                  } yield res
              }
            }

        }

      override def update(
        recruiterId: SalesForceId
      )(data: (RequestId, CorrelationId, JobId, JobUpdate)): F[Either[ErrorMessage, Unit]] =
        shutdownGuard.run {
          implicit val cursor: Cursor                           = Cursor("body")
          val (requestId, correlationId, referenceOrJobId, job) = data
          val loggerFactory                                     = CorrelationLoggerFactory.apply[F](correlationId, requestId = RequestId.generate)
          val logger                                            = loggerFactory.getLogger
          val result = for {
            jobId     <- fetchJobId(recruiterId, JobId(referenceOrJobId.value))
            mappedJob <- mapJobUpdate(job, addressService)
            _         <- jobPosterServiceFactory.createService(correlationId, loggerFactory).update(recruiterId, shared.JobId(jobId), mappedJob)
          } yield ().asRight[ErrorMessage]

          result.recoverWith {
            case InvalidData(message)        => MonadThrow[F].pure(BadRequest(s"Job manager did not accept the data: '$message'").asLeft[Unit])
            case e: MappingError             => MonadThrow[F].pure(BadRequest(e.getMessage).asLeft[Unit])
            case error: LogoServiceError     => MonadThrow[F].pure(BadRequest(error.getMessage).asLeft[Unit])
            case JobCannotBeUpdated(message) => MonadThrow[F].pure(Forbidden(message).asLeft[Unit])
            case NonUpdatableJob(errorMessage) =>
              returnStatusConflict(errorMessage, recruiterId, requestId, correlationId, referenceOrJobId)
            case e: JobNotFound      => MonadThrow[F].pure(NotFound(s"Job '${referenceOrJobId.value}' not found.").asLeft[Unit])
            case e: ReferenceIdError => MonadThrow[F].pure(BadRequest(e.getMessage).asLeft[Unit])
            case ex: UnrecognizedJob.type =>
              for {
                logger <- loggerFactory.fromClass(getClass)
                _ <- logger.warn(
                  s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] An error occurred while updating the job: ${ex.getMessage}"
                )
                res <- MonadThrow[F].pure(BadRequest(ex.getMessage).asLeft[Unit])
              } yield res
            case ex: UnclassifiedPBPJob.type => MonadThrow[F].pure(BadRequest(ex.getMessage).asLeft[Unit])
            case thr: Throwable =>
              for {
                logger <- loggerFactory.fromClass(getClass)
                _ <- logger.error(
                  s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] An error occurred while updating the job: ${thr.getMessage}"
                )
                _ <- logger.error(
                  s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] Stacktrace: ${thr.getStackTrace.mkString("\n")}"
                )
                res <- MonadThrow[F].pure(Unknown("An error occurred while updating the job").asLeft[Unit])
              } yield res
          }

        }

      private def returnStatusConflict(
        errorMessage: String,
        recruiterId: SalesForceId,
        requestId: RequestId,
        correlationId: CorrelationId,
        referenceOrJobId: JobId
      ): F[Either[ErrorMessage, Unit]] =
        read(recruiterId)(requestId, correlationId, referenceOrJobId).map {
          case Right((summary, _)) => StatusConflict(errorMessage, AppJobStatus.fromString(summary.status.toString)).asLeft[Unit]
          case Left(error)         => error.asLeft[Unit]
        }

      override def delete(recruiterId: SalesForceId)(
        data: (RequestId, CorrelationId, JobId)
      ): F[Either[ErrorMessage, Unit]] = shutdownGuard.run {
        val (requestId, correlationId, referenceOrJobId) = data
        val loggerFactory                                = CorrelationLoggerFactory.apply[F](correlationId, RequestId.generate)
        //        logger.info(s"[$correlationId] Job deletion request was received for job id: '$jobId'")
        val result = for {
          jobId <- fetchJobId(recruiterId, JobId(referenceOrJobId.value))
          _     <- jobPosterServiceFactory.createService(correlationId, loggerFactory).delete(recruiterId, jobId)
        } yield ().asRight[ErrorMessage]

        result.recoverWith {
          case JobNotFound(jobId)      => MonadThrow[F].pure(NotFound(s"Job '${jobId.value}' not found.").asLeft)
          case e: ReferenceIdError     => MonadThrow[F].pure(BadRequest(e.getMessage).asLeft[Unit])
          case NoJobOwnership(message) => MonadThrow[F].pure(Forbidden(message).asLeft[Unit])
          case thr: Throwable =>
            for {
              logger <- loggerFactory.fromClass(getClass)
              _ <- logger.error(
                s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] An error occurred while deleting the job: ${thr.getMessage}"
              )
              _ <- logger.error(
                s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] Stacktrace: ${thr.getStackTrace.mkString("\n")}"
              )
              res <- MonadThrow[F].pure(Unknown("An error occurred while deleting the job").asLeft[Unit])
            } yield res
        }
      }

      override def updateJobStatus(
        recruiterId: SalesForceId
      )(data: (RequestId, CorrelationId, JobId, JobStatus)): F[Either[ErrorMessage, Unit]] = shutdownGuard.run {
        val (requestId, correlationId, referenceOrJobId, jobStatus) = data
        val loggerFactory                                           = CorrelationLoggerFactory.apply[F](correlationId, RequestId.generate)
        val logger                                                  = loggerFactory.getLogger
        //        logger.info(s"[$correlationId] Job $jobStatus request was received for job id: '$referenceOrJobId'")
        val result: F[Either[ErrorMessage, Unit]] = for {
          jobId <- fetchJobId(recruiterId, JobId(referenceOrJobId.value))
          _     <- jobPosterServiceFactory.createService(correlationId, loggerFactory).updateJobStatus(recruiterId, jobId, jobStatus)
        } yield ().asRight

        result.recoverWith {
          case JobCannotBeSuspended(message) => returnStatusConflict(message, recruiterId, requestId, correlationId, referenceOrJobId)
          case JobCannotBeResumed(message)   => returnStatusConflict(message, recruiterId, requestId, correlationId, referenceOrJobId)
          case JobNotFound(jobId)            => MonadThrow[F].pure(NotFound(s"Job '${jobId.value}' not found.").asLeft)
          case e: ReferenceIdError           => MonadThrow[F].pure(BadRequest(e.getMessage).asLeft[Unit])
          case thr: Throwable =>
            for {
              logger <- loggerFactory.fromClass(getClass)
              _ <- logger.error(
                s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] An error occurred while updating the status of the job to '$jobStatus': ${thr.getMessage}"
              )
              _ <- logger.error(
                s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] Stacktrace: ${thr.getStackTrace.mkString("\n")}"
              )
              res <- MonadThrow[F].pure(Unknown(s"An error occurred while updating the status of the job to '$jobStatus'").asLeft[Unit])
            } yield res
        }
      }

      override def read(
        credentials: SalesForceId
      )(data: (RequestId, CorrelationId, JobId)): F[Either[ErrorMessage, (JobSummary, RequestId)]] = shutdownGuard.run {
        val (requestId, correlationId, jobId) = data
        val loggerFactory                     = CorrelationLoggerFactory.apply[F](correlationId, requestId)
        val result = for {
          jobReference <- fetchJobId(credentials, jobId)
          recruiterId  <- credentials.pure[F]
          summary <- jobPosterServiceFactory
            .createService(correlationId, loggerFactory)
            .read(recruiterId, shared.JobId(jobReference))
        } yield (JobSummary.fromService(summary), requestId).asRight[ErrorMessage]

        result.recoverWith {
          case JobNotFound(jobId) =>
            MonadThrow[F].pure(NotFound(s"No summary found for job '${jobId.value}'").asLeft[(JobSummary, RequestId)])
          case InvalidData(message) => MonadThrow[F].pure(BadRequest(message).asLeft[(JobSummary, RequestId)])
          case e: ReferenceIdError  => MonadThrow[F].pure(BadRequest(e.getMessage).asLeft[(JobSummary, RequestId)])
          case thr: Throwable =>
            for {
              logger <- loggerFactory.fromClass(getClass)
              _ <- logger.error(
                s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] An error occurred while fetching the job summary: ${thr.getMessage}"
              )
              _ <- logger.error(
                s"[REQ-ID:${requestId.value}][CORR-ID:${correlationId.value}] Stacktrace: ${thr.getStackTrace.mkString("\n")}"
              )
              res <- MonadThrow[F].pure(Unknown(s"An error occurred while fetching the job summary").asLeft[(JobSummary, RequestId)])
            } yield res
        }
      }

      private def fetchJobId(credentials: SalesForceId, jobId: JobId): F[String] =
        for {
          validatedReferenceId <- MonadThrow[F].fromValidated(ReferenceId(jobId.value))
          potentialJobId       <- referenceIdService.getLatestJobId(credentials, validatedReferenceId)
          jobReference         <- potentialJobId.map(_.value).getOrElse(jobId.value).pure[F]
        } yield jobReference

      private def postJob(
        correlationId: CorrelationId,
        loggerFactory: CorrelationLoggerFactory[F],
        jobId: JobId,
        referenceId: Option[ReferenceId],
        recruiterId: SalesForceId,
        mappedJob: ApplicationJob,
        mappedConfiguration: Option[Configuration]
      ): F[Unit] =
        referenceId match {
          case Some(refId) =>
            for {
              storedJobId <- referenceIdService.getLatestJobId(recruiterId, refId)
              response <- storedJobId
                .map(jobRef =>
                  repostUsingReferenceId(correlationId, loggerFactory, jobId, recruiterId, mappedJob, mappedConfiguration, refId, jobRef)
                )
                .getOrElse(postUsingReferenceId(correlationId, loggerFactory, jobId, recruiterId, refId, mappedJob, mappedConfiguration))
            } yield response
          case None =>
            jobPosterServiceFactory
              .createService(correlationId, loggerFactory)
              .create(recruiterId, shared.JobId(jobId.value), mappedJob, mappedConfiguration, correlationId)
        }

      private def repostUsingReferenceId(
        correlationId: CorrelationId,
        loggerFactory: CorrelationLoggerFactory[F],
        jobId: JobId,
        recruiterId: SalesForceId,
        mappedJob: ApplicationJob,
        mappedConfiguration: Option[Configuration],
        refId: ReferenceId,
        jobRef: RefJobId
      ): F[Unit] =
        (for {
          summary <- jobPosterServiceFactory
            .createService(correlationId, loggerFactory)
            .read(recruiterId, shared.JobId(jobRef.value))
          result <-
            if (summary.status == Expired || summary.status == Deleted)
              postUsingReferenceId(correlationId, loggerFactory, jobId, recruiterId, refId, mappedJob, mappedConfiguration)
            else
              NonRepostableJob(refId, jobRef, summary.status).raiseError[F, Unit]
        } yield result).recoverWith { case JobNotFound(notFoundJobId) =>
          for {
            _   <- loggerFactory.getLogger.info(s"No job was found for '$notFoundJobId'")
            _   <- referenceIdService.delete(recruiterId, refId, JobId(notFoundJobId.value))
            _   <- loggerFactory.getLogger.info(s"Deleted '$refId' and '$notFoundJobId' for recruiter '${recruiterId.idWithChecksum}'")
            res <- postUsingReferenceId(correlationId, loggerFactory, jobId, recruiterId, refId, mappedJob, mappedConfiguration)
          } yield res
        }

      private def postUsingReferenceId(
        correlationId: CorrelationId,
        loggerFactory: CorrelationLoggerFactory[F],
        jobId: JobId,
        recruiterId: SalesForceId,
        referenceId: ReferenceId,
        mappedJob: ApplicationJob,
        mappedConfiguration: Option[Configuration]
      ): F[Unit] =
        (for {
          _ <- referenceIdService.store(recruiterId, referenceId, jobId)
          _ <- loggerFactory.getLogger.info(s"Stored '$referenceId' and '$jobId' for recruiter '${recruiterId.idWithChecksum}'")
          result <- jobPosterServiceFactory
            .createService(correlationId, loggerFactory)
            .create(recruiterId, shared.JobId(jobId.value), mappedJob, mappedConfiguration, CorrelationId(correlationId.value))
        } yield result)
          .onError {
            case _: ServiceUnavailable =>
              loggerFactory.getLogger.error(
                s"Service unavailable when posting job '$referenceId/$jobId' for '${recruiterId.idWithChecksum}' - job may have been posted successfully. NOT deleting reference ID."
              )
            case _: Timeout =>
              loggerFactory.getLogger.error(
                s"Timeout when posting job '$referenceId/$jobId' for '${recruiterId.idWithChecksum}' - job may have been posted successfully. NOT deleting reference ID."
              )
            case e =>
              loggerFactory.getLogger
                .info(
                  s"Error when trying to post a job using the referenceId '$referenceId/$jobId' for '${recruiterId.idWithChecksum}': ${e.getMessage}"
                ) *>
                referenceIdService.delete(recruiterId, referenceId, jobId) *>
                loggerFactory.getLogger.info(s"Deleted '$referenceId' and '$jobId' for recruiter '${recruiterId.idWithChecksum}'")
          }

      private def chooseJobReference(jobId: JobId, referenceId: Option[ReferenceId])(logger: Logger[F]): F[String] =
        referenceId match {
          case Some(refId) =>
            if (refId.value.trim.isEmpty) jobId.value.pure[F]
            else
              for {
                _  <- logger.info(s"'$refId' is linked to '$jobId'")
                id <- MonadThrow[F].fromEither(refId.asUrlEncoded)
              } yield id
          case None => jobId.value.pure[F]
        }
    }
}
