package nl.dpes.job.poster.api.cpc_registrations

import io.circe.Encoder
import java.time.{Instant, ZoneOffset, ZonedDateTime}
import java.time.format.DateTimeFormatter

case class Timestamp(value: String) extends AnyVal

object Timestamp {
  implicit lazy val timestampEncoder: Encoder[Timestamp] = Encoder.encodeString.contramap(_.value)

  private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")

  def now(): Timestamp = {
    val instant       = Instant.now()
    val zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneOffset.UTC)
    Timestamp(formatter.format(zonedDateTime))
  }

  def sixHoursAgo(): Timestamp = {
    val instant       = Instant.now().minusSeconds(6 * 60 * 60) // 6 hours ago
    val zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneOffset.UTC)
    Timestamp(formatter.format(zonedDateTime))
  }

  object SwaggerDoc {
    def fromExample: Timestamp = sixHoursAgo()
    def toExample: Timestamp   = now()
  }
}
