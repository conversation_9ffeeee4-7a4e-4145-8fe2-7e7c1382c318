package nl.dpes.job.poster.api.cpc_registrations

import cats.effect.{Async, Sync}
import cats.implicits._
import io.circe.generic.auto._
import nl.dpes.job.poster.api.service.authentication.AuthenticationService
import nl.dpes.job.poster.api.tracing.{DecodeFailureLogger, DecodeSuccessLogger}
import nl.dpes.job.poster.api.{serverOptions, wrapErrorResponse, Unauthorized, Unknown}
import nl.dpes.job.poster.api.cpc_registrations.CpcResponse.SwaggerDoc.example
import nl.dpes.job.poster.api.cpc_registrations.Timestamp.SwaggerDoc
import org.http4s.HttpRoutes
import sttp.model.StatusCode
import sttp.tapir._
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe._
import sttp.tapir.server.ServerEndpoint
import sttp.tapir.server.http4s.{Http4sServerInterpreter, Http4sServerOptions}
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter

trait CpcRegistrationController[F[_]] {
  def getCpcRegistrations: F[ServerEndpoint[Any, F]]
  def routes: F[HttpRoutes[F]]
  def endpoints: F[List[ServerEndpoint[Any, F]]]
}

object CpcRegistrationController {

  def impl[F[_]: Async](
    authenticationService: AuthenticationService[F],
    cpcRegistrationService: CpcService[F]
  ): CpcRegistrationController[F] =
    new CpcRegistrationController[F] {

      override def getCpcRegistrations: F[ServerEndpoint[Any, F]] = Async[F].delay {
        cpcRegistrationClient
          .baseEndpoint(
            authenticationService.authenticate,
            _.errorOut(
              oneOf(
                oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
                oneOfVariant[Unknown](statusCode(StatusCode.InternalServerError).and(jsonBody[Unknown]))
              )
            )
          )
          .get
          .description("Returns registered CPC data for applies.")
          .in("cpc-performance")
          .in(
            query[Option[Timestamp]]("from")
              .description(
                "Optional start time for filtering CPC data. " +
                s"If not provided, defaults to 6 hours before the 'to' parameter. " +
                s"Accepts full ISO 8601 format (${SwaggerDoc.fromExample.value}) or date-only format (${SwaggerDoc.fromExample.value
                  .split("T")(0)})."
              )
              .example(Some(SwaggerDoc.fromExample))
          )
          .in(
            query[Option[Timestamp]]("to")
              .description(
                "Optional end time for filtering CPC data. " +
                "If not provided, defaults to the current time. " +
                s"Accepts full ISO 8601 format (${SwaggerDoc.toExample.value}) or date-only format (${SwaggerDoc.toExample.value.split("T")(0)})."
              )
              .example(Some(SwaggerDoc.toExample))
          )
          .mapInTo[Period]
          .out(jsonBody[CpcResponse].example(example))
          .serverLogicSuccess(cpcRegistrationService.getRegisteredApplies)
      }

      override def routes: F[HttpRoutes[F]] = for {
        endpoint <- List(getCpcRegistrations).sequence
        swaggerRoutes <- Async[F].delay(
          Http4sServerInterpreter[F]()
            .toRoutes(
              SwaggerInterpreter(swaggerUIOptions =
                SwaggerUIOptions(
                  List("docs", "cpc-registrations"),
                  "docs.yaml",
                  Nil,
                  useRelativePaths = false,
                  showExtensions = false,
                  initializerOptions = None,
                  oAuthInitOptions = None
                )
              ).fromServerEndpoints[F](endpoint, "Registered apply CPCs", cpcRegistrationClient.version)
            )
        )
        endpointRoutes <- Sync[F].delay(Http4sServerInterpreter[F]().toRoutes(endpoint))
      } yield swaggerRoutes <+> endpointRoutes

      override def endpoints: F[List[ServerEndpoint[Any, F]]] = for {
        endpoint <- List(getCpcRegistrations).sequence
      } yield endpoint
    }
}
