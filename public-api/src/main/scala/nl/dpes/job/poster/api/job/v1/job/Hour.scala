package nl.dpes.job.poster.api.job.v1.job

import cats.data.Validated
import cats.implicits.catsSyntaxValidatedId
import io.circe.{Decoder, Encoder}

case class Hour private (hour: Int) extends AnyVal

object Hour {

  implicit lazy val encoder: Encoder[Hour]   = Encoder.encodeInt.contramap[Hour](_.hour)
  implicit lazy val decoder: Decoder[Hour]   = Decoder.decodeInt.emap(Hour(_).toEither)
  implicit lazy val ordering: Ordering[Hour] = Ordering.by(_.hour)

  def apply(hour: Int): Validated[String, Hour] =
    if (hour < 1) s"Hour '$hour' cannot be less than 1.".invalid
    else if (hour > 40) s"Hour '$hour' cannot be greater than 40.".invalid
    else new Hour(hour).valid

  object SwaggerDoc {
    val minHourExample = new Hour(10)
    val maxHourExample = new Hour(30)
  }
}
