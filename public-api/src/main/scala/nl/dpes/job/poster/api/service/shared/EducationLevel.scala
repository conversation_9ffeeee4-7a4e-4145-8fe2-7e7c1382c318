package nl.dpes.job.poster.api.service.shared

import cats.data.Validated
import io.circe.{Decoder, Encoder}
import cats.implicits._

case class EducationLevel private (level: String) extends AnyVal

object EducationLevel {

  implicit lazy val encoder: Encoder[EducationLevel] = Encoder.encodeString.contramap[EducationLevel](_.level)
  implicit lazy val decoder: Decoder[EducationLevel] = Decoder.decodeString.emap(EducationLevel(_).toEither)

  val validEducationLevels: Set[String] = Set("Lagere school", "LBO", "VMBO/MAVO", "HAVO", "VWO", "MBO", "HBO", "WO", "Postdoctoraal")

  def apply(level: String): Validated[String, EducationLevel] =
    if (validEducationLevels.containsCaseInsensitive(level)) new EducationLevel(level).valid
    else s"Education level '$level' is not valid.".invalid

  object SwaggerDoc {
    val hbo  = new EducationLevel("HBO")
    val wo   = new EducationLevel("WO")
    val havo = new EducationLevel("HAVO")
  }
}
