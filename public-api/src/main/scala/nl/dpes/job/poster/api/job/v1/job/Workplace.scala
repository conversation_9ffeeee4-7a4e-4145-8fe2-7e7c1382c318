package nl.dpes.job.poster.api.job.v1.job

import cats.implicits._
import cats.data.Validated
import io.circe.{Decoder, Encoder}
import nl.dpes.job.poster.api.service.{Cursor, MappingError}
import nl.dpes.job.poster.api.service.shared.{Workplace => AppWorkplace}

case class Workplace private (workplace: String) extends AnyVal

object Workplace {

  implicit lazy val encoder: Encoder[Workplace] = Encoder.encodeString.contramap(_.workplace)
  implicit lazy val decoder: Decoder[Workplace] = Decoder.decodeString.emap(Workplace(_).toEither)

  val validWorkplaces: Set[String] = Set(
    "Remote",
    "On-site",
    "Hybride"
  )

  def apply(name: String): Validated[String, Workplace] =
    if (validWorkplaces contains name) new Workplace(name).valid
    else s"Workplace '$name' is not valid.".invalid

  def map(workplace: Option[Workplace])(implicit cursor: Cursor): Validated[MappingError, Option[AppWorkplace]] =
    workplace
      .traverse(place => AppWorkplace(place.workplace))
      .leftMap(error => MappingError(cursor -> error))

  object SwaggerDoc {
    val example = new Workplace("Remote")
  }
}
