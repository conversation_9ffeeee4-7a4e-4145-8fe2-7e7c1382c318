package nl.dpes.job.poster.api.service.apikey.api

import io.circe._
import io.circe.generic.semiauto._
import nl.dpes.job.poster.api.converter.Converter
import nl.dpes.job.poster.api.converter.Converter._
import nl.dpes.job.poster.api.service.{apikey => domain}
import sttp.tapir.Schema

case class ApiKeyInformation(id: ApiKeyId, integration: Integration, createdAt: CreatedAt)

object ApiKeyInformation {

  implicit lazy val converter: Converter[ApiKeyInformation, domain.ApiKeyInformation] =
    new Converter[ApiKeyInformation, domain.ApiKeyInformation] {

      override def convert(from: ApiKeyInformation): domain.ApiKeyInformation = domain.ApiKeyInformation(
        from.id.toDomain[domain.ApiKeyId],
        from.integration.toDomain[domain.Integration],
        from.createdAt.toDomain[domain.CreatedAt]
      )

      override def invert(from: domain.ApiKeyInformation): ApiKeyInformation = ApiKeyInformation(
        from.id.toApi[ApiKeyId],
        from.integration.toApi[Integration],
        from.createdAt.toApi[CreatedAt]
      )
    }

  implicit def apiKeyInformationCodec: Codec[ApiKeyInformation]   = deriveCodec
  implicit def apiKeyInformationSchema: Schema[ApiKeyInformation] = Schema.derived
}
