package nl.dpes.job.poster.api.service.logo

import cats.MonadThrow
import cats.effect.Async
import com.google.protobuf.ByteString
import fs2._
import nl.dpes.fileservice.proto.chunk.chunk.{Chunk => JobLogoChunk}

object LogoChunk {
  case class LogoSizeExceeded(size: Int) extends Throwable(s"Logo size was greater than $size")

  def counter[F[_]: MonadThrow, A](max: Int): Pipe[F, A, Int] = _.chunks.fold(0) { case (acc, chunk) =>
    val size = acc + chunk.size
    if (size > max) throw LogoSizeExceeded(max)
    else size
  }

  def mapChunks[F[_]: Async](chunks: fs2.Stream[F, Chunk[Byte]]): fs2.Stream[F, JobLogoChunk] =
    chunks.map(c => JobLogoChunk(ByteString.copyFrom(c.toArray)))
}
