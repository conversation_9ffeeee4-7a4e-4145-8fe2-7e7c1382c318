package nl.dpes.job.poster.api.cpc_registrations

import java.util.UUID
import java.time.{Instant, ZoneOffset, ZonedDateTime}
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

case class CpcResponse(results: List[CpcResult])

object CpcResponse {

  object SwaggerDoc {
    // Generate timestamps for examples
    private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")

    private def generateTimestamp(hoursAgo: Int): String = {
      val instant       = Instant.now().minus(hoursAgo, ChronoUnit.HOURS)
      val zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneOffset.UTC)
      formatter.format(zonedDateTime)
    }

    val example: CpcResponse = CpcResponse(
      List(
        CpcResult(
          ClickId(UUID.randomUUID().toString),
          JobId(UUID.randomUUID().toString),
          Some(ReferenceId("ref-67890")),
          CpcBucket("low"),
          Timestamp(generateTimestamp(36)) // 36 hours ago
        ),
        CpcResult(
          ClickId(UUID.randomUUID().toString),
          JobId(UUID.randomUUID().toString),
          Some(ReferenceId("ref-12345")),
          CpcBucket("middle"),
          Timestamp(generateTimestamp(24)) // 24 hours ago
        ),
        CpcResult(
          ClickId(UUID.randomUUID().toString),
          JobId(UUID.randomUUID().toString),
          Some(ReferenceId("ref-54321")),
          CpcBucket("high"),
          Timestamp(generateTimestamp(12)) // 12 hours ago
        )
      )
    )
  }
}
