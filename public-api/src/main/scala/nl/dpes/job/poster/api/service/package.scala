package nl.dpes.job.poster.api

import cats.Parallel
import cats.effect.Sync
import cats.effect.kernel.Async
import cats.syntax.applicative._
import cats.syntax.either._
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.parallel._
import cats.syntax.semigroupk._
import io.getquill.{SnakeC<PERSON>, SqliteJdbcContext}
import nl.dpes.job.poster.api.config.Configuration
import nl.dpes.job.poster.api.defaults.{DefaultsController, DefaultsService}
import nl.dpes.job.poster.api.service.apikey.{ApiKeyController, ApiKeyRepository, ApiKeyTapirService, Salt}
import nl.dpes.job.poster.api.defaults.DefaultsRepository
import nl.dpes.job.poster.api.ErrorMessage
import nl.dpes.job.poster.api.service.recruiter.RecruiterService
import org.http4s.HttpRoutes
import org.typelevel.log4cats.LoggerFactory
import sttp.tapir.server.PartialServerEndpoint
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter
import sttp.tapir.{auth, endpoint, Endpoint}

package object service extends Configuration {

  val oauthDescription: String =
    """
      |This is how you can get the access token:
      |
      |##### Step 1: Get the Authorization Token:
      |
      |Run the following GET query to get your **authorization_code**:
      |```
      |GET <<your-authorization-url>>
      |
      |Headers:
      |client_id=<<your-client-id>>
      |redirect-uri=<<your-redirect-uri>>
      |response_type=code
      |```
      |As a result, you will get an authorization code that you will need for the next step.
      |
      |##### Step 2: Request an Access Token:
      |
      |Run the following POST query to get your **access_token**:
      |```
      |POST <<your-token-url>>
      |
      |Headers:
      |Content-type=application/x-www-form-urlencoded
      |grant_type=authorization_code
      |code=<<your-authorization-code-retrieved-from-step-1>>
      |client_id=<<your-client-id>>
      |client_secret=<<your-client-secret>>
      |redirect_uri=<<your-redirect-uri>>
      |Content-Length=307
      |```
      |For further information, you can check the official documentation [here](https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oauth_web_server_flow.htm&type=5).
      |""".stripMargin

  def baseEndpoint[F[_]: Async, E <: ErrorMessage](
    f: Endpoint[Unit, Unit, Unit, Unit, Any] => Endpoint[Unit, Unit, E, Unit, Any]
  ): PartialServerEndpoint[BearerToken, BearerToken, Unit, E, Unit, Any, F] =
    f(endpoint.in("api"))
      .securityIn(auth.bearer[String]().mapTo[BearerToken].description(oauthDescription))
      .serverSecurityLogic(_.asRight[E].pure)

  def createApiKeyController[F[_]: Async: LoggerFactory: Parallel](implicit
    context: SqliteJdbcContext[SnakeCase.type]
  ): F[ApiKeyController[F]] =
    Sync[F].delay(
      ApiKeyController.impl[F](
        ApiKeyTapirService(
          recruiter.RecruiterService(
            RecruiterService.connection(RecruiterServiceConnection.host, RecruiterServiceConnection.port)
          ),
          ApiKeyRepository.impl(service.apikey.ApiKeyHasher(Salt(ApiKeyHasher.salt)))
        )
      )
    )

  def createDefaultsController[F[_]: Async: LoggerFactory](implicit context: SqliteJdbcContext[SnakeCase.type]): F[DefaultsController[F]] =
    Sync[F].delay(
      DefaultsController.impl[F](
        DefaultsService(
          DefaultsRepository.impl,
          recruiter.RecruiterService(
            RecruiterService.connection(RecruiterServiceConnection.host, RecruiterServiceConnection.port)
          )
        )
      )
    )

  def services[F[_]: Async: Parallel: LoggerFactory](implicit
    context: SqliteJdbcContext[SnakeCase.type]
  ): F[(ApiKeyController[F], DefaultsController[F])] =
    (createApiKeyController, createDefaultsController).parTupled

  def routes[F[_]: Async: Parallel: LoggerFactory](implicit context: SqliteJdbcContext[SnakeCase.type]): F[HttpRoutes[F]] = for {
    (apiKeyController, defaultsConroller) <- (createApiKeyController, createDefaultsController).parTupled
    apiKeyEndpoints                       <- apiKeyController.endpoints
    defaultsEndpoints                     <- defaultsConroller.endpoints
    apiKeyRoutes                          <- apiKeyController.routes
    defaultsRoutes                        <- defaultsConroller.routes

    swaggerRoutes <- Sync[F].delay(
      Http4sServerInterpreter[F]()
        .toRoutes(
          SwaggerInterpreter(swaggerUIOptions =
            SwaggerUIOptions(
              List("docs", "service"),
              "docs.yaml",
              Nil,
              useRelativePaths = false,
              showExtensions = false,
              initializerOptions = None,
              oAuthInitOptions = None
            )
          )
            .fromServerEndpoints[F](apiKeyEndpoints ++ defaultsEndpoints, "Job poster API management", "1.0")
        )
    )
  } yield swaggerRoutes <+> apiKeyRoutes <+> defaultsRoutes
}
