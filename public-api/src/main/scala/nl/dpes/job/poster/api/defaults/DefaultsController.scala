package nl.dpes.job.poster.api.defaults

import cats.effect._
import cats.syntax.flatMap._
import cats.syntax.functor._
import cats.syntax.traverse._
import io.circe.generic.auto._
import nl.dpes.job.poster.api.service.baseEndpoint
import Defaults.SwaggerDoc.{defaultsExample, defaultsObjectDescription}
import nl.dpes.job.poster.api.{BadRequest, Forbidden, Unauthorized}
import nl.dpes.job.poster.api.serverOptions
import org.http4s.HttpRoutes
import sttp.model.StatusCode
import sttp.tapir._
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe.jsonBody
import sttp.tapir.server.ServerEndpoint
import sttp.tapir.server.http4s.Http4sServerInterpreter

trait DefaultsController[F[_]] {
  def storeDefaults: F[ServerEndpoint[Any, F]]
  def readDefaults: F[ServerEndpoint[Any, F]]
  def endpoints: F[List[ServerEndpoint[Any, F]]]
  def routes: F[HttpRoutes[F]]
}

object DefaultsController {

  def impl[F[_]: Async](defaultsService: DefaultsService[F]): DefaultsController[F] = new DefaultsController[F] {

    override def storeDefaults: F[ServerEndpoint[Any, F]] = Sync[F].delay {
      baseEndpoint(
        _.errorOut(
          oneOf(
            oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest])),
            oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
            oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden]))
          )
        )
      ).put
        .in("defaults")
        .in(jsonBody[Defaults].description(defaultsObjectDescription).example(defaultsExample))
        .out(statusCode(StatusCode.NoContent))
        .serverLogic(defaultsService.saveDefaults)
    }

    override def readDefaults: F[ServerEndpoint[Any, F]] = Sync[F].delay {
      baseEndpoint(
        _.errorOut(
          oneOf(
            oneOfVariant[Unauthorized](statusCode(StatusCode.Unauthorized).and(jsonBody[Unauthorized])),
            oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden]))
          )
        )
      ).get
        .in("defaults")
        .out(jsonBody[Defaults])
        .serverLogic(defaultsService.getDefaults)
    }

    override def endpoints: F[List[ServerEndpoint[Any, F]]] = List(readDefaults, storeDefaults).sequence

    override def routes: F[HttpRoutes[F]] = for {
      endpoints      <- endpoints
      endpointRoutes <- Sync[F].delay(Http4sServerInterpreter[F](serverOptions).toRoutes(endpoints))
    } yield endpointRoutes
  }
}
