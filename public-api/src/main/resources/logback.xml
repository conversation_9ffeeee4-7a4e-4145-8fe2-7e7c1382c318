<configuration>
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <withJansi>true</withJansi>
    <encoder>
      <pattern>[%thread] %yellow(%replace([correlation-id:%X{correlation-id} - request-id:%X{request-id}]){'\[correlation-id: - request-id:\]', ''}) %highlight(%-5level) %cyan(%logger{15}) - %msg %n</pattern>
    </encoder>
  </appender>
  <appender name="MONOCHROME" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>[%thread] %replace([correlation-id:%X{correlation-id} - request-id:%X{request-id}]){'\[correlation-id: - request-id:\]', ''} %-5level %logger{15} - %msg %n</pattern>
    </encoder>
  </appender>
  <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
      <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
        <jsonFormatter class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter"></jsonFormatter>
        <timestampFormat>yyyy-MM-dd' 'HH:mm:ss.SSS</timestampFormat>
        <appendLineSeparator>true</appendLineSeparator>
      </layout>
    </encoder>
  </appender>
  <root level="${LOGGING_LEVEL:-INFO}">
    <appender-ref ref="${LOGGING_APPENDER:-JSON}" />
  </root>
</configuration>
