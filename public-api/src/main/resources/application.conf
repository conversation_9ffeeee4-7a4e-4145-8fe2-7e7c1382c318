http {
    host = "0.0.0.0"
    port = "11360"
    basePath = "/"
}
recruiterService {
    host = ${?B2B_SALESFORCE_GATEWAY_SERVICE_HOST}
    port = 11310
}
jobManager {
    host = ${?B2B_JOB_MANAGER_SERVICE_HOST}
    port = 11320
}
fileService {
    host = ${?FILE_SERVICE_SERVICE_HOST}
    port = 11500
}
vacancyEnricherService{
    host = ${?VACANCY_ENRICHER_HOST}
    apiKey = ${?VACANCY_ENRICHER_API_KEY}
}

applicationService{
    host = ${?APPLICATION_SERVICE_HOST}
    apiKey = ${?APPLICATION_SERVICE_API_KEY}
}
apiKeyHasher {
    salt = ${?API_KEY_SALT}
}
dashboard.url = ${?DASHBOARD_URL}
quillconfig {
    dataSourceClassName = com.mysql.cj.jdbc.MysqlDataSource
    dataSource {
        url = ${?DATABASE_CONNECTION_STRING}
        user = ${?DATABASE_USER}
        password = ${?DATABASE_PASSWORD}
        cachePrepStmts = true
        prepStmtCacheSize = 250
        prepStmtCacheSqlLimit = 2048
    }
    maximumPoolSize = 1
    maximumPoolSize = ${?DB_MAX_POOL_SIZE}
}
database {
    url = ${?DATABASE_CONNECTION_STRING}
    user-name = ${?DATABASE_USER}
    user-password = ${?DATABASE_PASSWORD}
    thread-count = 1
    thread-count = ${?DB_THREAD_COUNT}
}
reportingService {
    host = ${?REPORTING_SERVICE_HOST}
}
addressService {
    host = ${?ADDRESS_SERVICE_HOST}
}

akka.grpc.client {
  deadline = 20s
  keep-alive-time = 15s
  keep-alive-timeout = 3s
  keep-alive-without-calls = true
  max-connection-age = 120s
  max-connection-age-grace = 30s
  max-inbound-message-size = 4194304
  max-outbound-message-size = 4194304

  connection-pool {
    max-connections = 4
    min-connections = 1
    max-open-requests = 8
    connection-idle-timeout = 60s
  }

  recruiter-service {
    host = ${?B2B_SALESFORCE_GATEWAY_SERVICE_HOST}
    port = 11310
    use-tls = false
    service-discovery {
      mechanism = "static"
    }
    connection-attempts = 3
    deadline = 5s
  }

}
