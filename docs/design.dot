digraph design {
    subgraph cluster_api {
        // The API can be called from the outside.
        // Breaking changes in the api must result in a new api
        // Changes in application are only possible if all active apis can handle them
        label = "api v1"
        "api.Job" [color = green]
        "api.UpdateJob" [color = orange]
    }
    subgraph cluster_application {
        // Decoupled from the actual api
        // The application can change without affecting the api
        label = application
        "app.Job" "app.Configuration"
    }
    subgraph cluster_jobManager {
        // Decoupled from application
        // Job manager can change without affecting the application much
        label = jobManager
        jobContent product
        postJob [color = green]
        updateJob [color = orange]
    }

    subgraph updating {
        edge [color = orange]
        "api.UpdateJob" -> "app.Job" -> jobContent -> updateJob
    }

    subgraph posting {
        edge [color = green]
        "api.Job" -> {"app.Configuration" "app.Job"}
        "app.Job" -> jobContent
        "app.Configuration" -> product
        {jobContent product} -> postJob
    }

}