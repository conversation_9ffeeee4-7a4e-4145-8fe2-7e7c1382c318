pipeline {
    agent {
        node {
            label 'alpakka_java21'
        }
    }
    parameters {
        string(name: 'MODULE_BUILD_NUMBER', defaultValue: '', description: 'Buildnumber to use')
        string(name: 'MODULE_NAME', defaultValue: '', description: 'Buildnumber to use')
    }
    stages {
        stage('Deploy') {
            steps {
                ansiColor('xterm') {
                    sh "sbt ${MODULE_NAME}/'deploy pro ${MODULE_BUILD_NUMBER}'"
                }
            }
        }
    }
    post {
        failure {
            ansiColor('xterm') {
                sh "sbt ${MODULE_NAME}/\"rollback pro\""
            }
        }
    }
}
