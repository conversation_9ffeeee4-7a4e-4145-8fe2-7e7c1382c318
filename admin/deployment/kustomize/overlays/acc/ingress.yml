kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: "job-poster-api-admin"
  namespace: "default"
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "2m"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload";
      more_set_headers "x-frame-options: SAMEORIGIN";
spec:
  ingressClassName: nginx-internal
  rules:
    - host: "job-poster-api-admin-acc.persgroep.digital"
      http:
        paths:
          - path: "/"
            pathType: Prefix
            backend:
              service:
                name: "job-poster-api-admin"
                port:
                  number: 11363
