kind: ConfigMap
apiVersion: v1
metadata:
  name: job-poster-api-admin-config
  namespace: default
  labels:
    environment: acceptance
data:
  LOGGING_LEVEL: "INFO"
  LOGGING_APPENDER: "JSON"
  DB_CONNECTION_URL: "*********************************************************************************************************************************************"
  DB_THREAD_COUNT: "1"
  DB_MAX_POOL_SIZE: "1"
  OIDC_DISCOVERY_URL: "https://persgroep.onelogin.com/oidc/2/.well-known/openid-configuration"
  OIDC_ORIGIN: "https://job-poster-api-admin-acc.persgroep.digital"
  CREDIT_SERVICE_HOST: "http://credit-service:11400"
