apiVersion: apps/v1
kind: Deployment
metadata:
  name: job-poster-api-admin
  annotations:
    jenkins/build-number: "{{BUILD_NUMBER}}"
spec:
  selector:
    matchLabels:
      app: job-poster-api-admin
  replicas: 1
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 320
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: job-poster-api-admin
    spec:
      containers:
        - name: job-poster-api-admin
          image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/job-poster-api-admin:{{BUILD_NUMBER}}
          resources:
            limits:
              memory: 800Mi
            requests:
              cpu: 20m
              memory: 600Mi
          ports:
            - name: app-port
              containerPort: 11363
          readinessProbe:
            timeoutSeconds: 10
            initialDelaySeconds: 30
            periodSeconds: 15
            failureThreshold: 4
            httpGet:
              path: "/status"
              port: app-port
          livenessProbe:
            timeoutSeconds: 5
            initialDelaySeconds: 60
            periodSeconds: 15
            failureThreshold: 3
            httpGet:
              path: "/status"
              port: app-port
          envFrom:
            - configMapRef:
                name: job-poster-api-admin-config
          env:
            - name: "JAVA_OPTS"
              value: "-XX:+UseContainerSupport -XX:+UseG1GC -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:+UseStringDeduplication -XX:MaxRAMPercentage=75.0 -XX:InitialRAMPercentage=75.0"
            - name: DB_USER_NAME
              valueFrom:
                secretKeyRef:
                  name: job-poster-api-admin
                  key: user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: job-poster-api-admin
                  key: password
            - name: ENCRYPTION_SECRET
              valueFrom:
                secretKeyRef:
                  name: job-poster-api-admin
                  key: encryption-secret
            - name: OIDC_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: job-poster-api-admin
                  key: oidc-client-id
            - name: OIDC_SECRET
              valueFrom:
                secretKeyRef:
                  name: job-poster-api-admin
                  key: oidc-secret
            - name: API_KEY_SALT
              valueFrom:
                secretKeyRef:
                  name: job-poster-api
                  key: API_KEY_SALT
      restartPolicy: Always
      dnsPolicy: ClusterFirst
