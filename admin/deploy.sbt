import sbt.complete.DefaultParsers.spaceDelimited

import scala.sys.process.*

def getKubernetesClusterContext(clusterContext: String): Option[String] = {
  val hosts: String = s"host api.live.$clusterContext.ndp.kops.cloud" !!
  val pattern       = """is an alias for api\.(.*)\.""".r
  pattern.findAllIn(hosts).matchData.toSeq.headOption.map(_.group(1)).asInstanceOf[Option[String]]
}

lazy val deploy = inputKey[Unit]("Deploys to Kubernetes cluster")
deploy := {
  val args: Seq[String]   = spaceDelimited("<arg>").parsed
  lazy val clusterContext = args.head
  lazy val buildNumber    = args(1)

  val context: Option[String] = getKubernetesClusterContext(clusterContext)
  context match {
    case Some(kubernetesContext) =>
      streams.value.log.info(
        s"kubectl kustomize admin/deployment/kustomize/overlays/$clusterContext" #| s"sed s/{{BUILD_NUMBER}}/$buildNumber/g" #| s"kubectl apply --context=$kubernetesContext -f -" !!
      )
      streams.value.log.info(
        s"kubectl --context=$kubernetesContext rollout status deployment job-poster-api-admin" !!
      )
    case None => sys.exit(1)
  }
}

lazy val rollback = inputKey[Unit]("Rollback previous deployment")
rollback := {
  val args: Seq[String]   = spaceDelimited("<arg>").parsed
  lazy val clusterContext = args.head

  val context: Option[String] = getKubernetesClusterContext(clusterContext)
  context match {
    case Some(kubernetesContext) =>
      streams.value.log.info(
        s"kubectl --context=$kubernetesContext rollout undo deployment job-poster-api-admin" !!
      )
    case None => sys.exit(1)
  }
}