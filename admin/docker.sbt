import com.typesafe.sbt.packager.docker.{Cmd, ExecCmd}

enablePlugins(DockerPlugin, JavaAppPackaging, AshScriptPlugin)

dockerExposedPorts := Seq(11360)
dockerRepository := Some("************.dkr.ecr.eu-west-1.amazonaws.com")

dockerAliases ++= Set(
  dockerAlias.value.withTag(Some("latest")),
  dockerAlias.value.withTag(Some(scala.util.Properties.propOrNone("version").getOrElse("latest")))
).toSeq
Docker / daemonUserUid := Some("1001")
Docker / daemonUser := "daemon"
dockerBaseImage := "eclipse-temurin:21-jre"
Docker / maintainer := maintainer.value
Docker / packageName := name.value
Docker / version := version.value

dockerCommands := dockerCommands.value.filterNot {
  case Cmd("USER", args @ _*) if args contains "1001:0" => true
  case Cmd("USER", args @ _*) if args contains "daemon" => true
  case ExecCmd("ENTRYPOINT", _)                         => true
  case ExecCmd("CMD", args @ _*) if args.isEmpty        => true
  case _                                                => false
}

dockerCommands ++= Seq(
  Cmd("USER", (Docker / daemonUser).value),
  ExecCmd("ENTRYPOINT", "/opt/docker/bin/job-poster-api-admin"),
  ExecCmd("CMD")
)
