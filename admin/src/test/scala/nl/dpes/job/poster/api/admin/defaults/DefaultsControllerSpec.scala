package nl.dpes.job.poster.api.admin.defaults

import cats.effect._
import cats.implicits.{catsSyntaxApplicativeErrorId, catsSyntaxOptionId}
import nl.dpes.job.poster.api.admin.layout.FlashMessage
import nl.dpes.job.poster.api.admin.layout.FlashMessage.{DefaultsError, DefaultsSaved}
import nl.dpes.job.poster.api.admin.service.creditservice.Entitlement.{Credit, Subscription}
import nl.dpes.job.poster.api.admin.service.creditservice.Feature.Logo
import nl.dpes.job.poster.api.admin.service.creditservice.ProductConfiguration.JobPostingConfiguration
import nl.dpes.job.poster.api.admin.service.creditservice.Site._
import nl.dpes.job.poster.api.admin.service.creditservice._
import nl.dpes.job.poster.api.service.defaults.Defaults.ApplicationMethod.EasyApply
import nl.dpes.job.poster.api.service.defaults.{Defaults, DefaultsService}
import org.http4s._
import org.http4s.dsl.io._
import org.http4s.headers.`Content-Type`
import org.http4s.implicits._
import org.pac4j.core.profile.CommonProfile
import org.typelevel.ci.CIString
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.SimpleIOSuite

import scala.concurrent.duration.DurationInt

object DefaultsControllerSpec extends SimpleIOSuite {

  case class Error(message: String) extends Throwable(message)

  val loggerFactory: Slf4jFactory[IO] = Slf4jFactory.create[IO]

  val entitlements: List[Entitlement] = List(
    Credit(
      EntitlementId("123456789"),
      WebshopProduct(
        ProductName("Logo Vacature"),
        JobPostingConfiguration(Set(NVB, IOL, ITB), Availability(60 days), Set(Logo), Map())
      )
    ),
    Credit(
      EntitlementId("234567891"),
      WebshopProduct(
        ProductName("Stage Vacature"),
        JobPostingConfiguration(Set(NVB, IOL, ITB), Availability(60 days), Set(Logo), Map())
      )
    ),
    Credit(
      EntitlementId("345678912"),
      WebshopProduct(
        ProductName("Bijbaan Vacature"),
        JobPostingConfiguration(Set(NVB, IOL, ITB), Availability(60 days), Set(Logo), Map())
      )
    ),
    Subscription(
      EntitlementId("456789123"),
      WebshopProduct(
        ProductName("Standaard Vacature subscription"),
        JobPostingConfiguration(Set(NVB, IOL, ITB), Availability(60 days), Set(), Map())
      )
    )
  )

  val recruiterId: String           = "RECRUITER_ID"
  val profiles: List[CommonProfile] = List(new CommonProfile())

  val defaults: Defaults = Defaults(
    title = None,
    description = None,
    occupation = None,
    jobCategories = None,
    industryCategories = None,
    educationLevels = None,
    careerLevel = None,
    contractTypes = None,
    workplace = None,
    workingHours = None,
    salary = None,
    location = None,
    applicationMethod = Some(EasyApply),
    logo = None,
    video = None,
    company = None,
    configuration = Defaults
      .JobPosting(
        Set(Defaults.Site("Nationale Vacaturebank"), Defaults.Site("Intermediair"), Defaults.Site("Tweakers Carrière")),
        60 days,
        Set(Defaults.Feature("logo"))
      )
      .some
  )

  test("It should be able to return the entitlements with the preselected default product") {
    for {
      (mockCreditService, mockDefaultsService) <- getMock()
      controller                               <- IO(new DefaultsController[IO](mockCreditService, mockDefaultsService, loggerFactory))
      request                                  <- IO(Request[IO](Method.GET, uri"/recruiter/123/defaults"))
      authedRequest                            <- IO(AuthedRequest(profiles, request))
      response                                 <- controller.routes.orNotFound.run(authedRequest)
      responseBody                             <- response.as[String]
    } yield expect(
      response.status == Ok &&
      response.contentType.contains(`Content-Type`(MediaType.text.html)) &&
      responseBody.contains("<option value=\"Logo Vacature\" selected=\"true\">") &&
      responseBody.contains("<option value=\"Standaard Vacature subscription\">")
    )
  }

  test("It should not return the 'Stage' and 'Bijbaan' products") {
    for {
      (mockCreditService, mockDefaultsService) <- getMock()
      controller                               <- IO(new DefaultsController[IO](mockCreditService, mockDefaultsService, loggerFactory))
      request                                  <- IO(Request[IO](Method.GET, uri"/recruiter/123/defaults"))
      authedRequest                            <- IO(AuthedRequest(profiles, request))
      response                                 <- controller.routes.orNotFound.run(authedRequest)
      responseBody                             <- response.as[String]
    } yield expect(
      response.status == Ok &&
      response.contentType.contains(`Content-Type`(MediaType.text.html)) &&
      !responseBody.contains("Stage") &&
      !responseBody.contains("Bijbaan")
    )
  }

  test("It should be able to save the default job product configuration") {
    for {
      data                                     <- IO(List(("productName", "Logo Vacature")))
      (mockCreditService, mockDefaultsService) <- getMock()
      controller                               <- IO(new DefaultsController[IO](mockCreditService, mockDefaultsService, loggerFactory))
      request                                  <- IO(Request[IO](Method.POST, uri"/recruiter/123/defaults").withEntity(UrlForm(data: _*)))
      authedRequest                            <- IO(AuthedRequest(profiles, request))
      response                                 <- controller.routes.orNotFound.run(authedRequest)
    } yield expect(
      response.status == SeeOther &&
      response.headers.get(CIString("Location")).map(_.head).contains(Header.Raw(CIString("Location"), "/recruiter/123/defaults")) &&
      response.cookies.exists(cookie => FlashMessage.decode(cookie.content).map(_.render) == Right(DefaultsSaved("123").render))
    )
  }

  test("It should return an error when unable to save the default job product configuration") {
    for {
      data                                     <- IO(List(("productName", "Logo Vacature"), ("withEasyApply", "on")))
      (mockCreditService, mockDefaultsService) <- getMock(Some(Error("Unable to save defaults")))
      controller                               <- IO(new DefaultsController[IO](mockCreditService, mockDefaultsService, loggerFactory))
      request                                  <- IO(Request[IO](Method.POST, uri"/recruiter/123/defaults").withEntity(UrlForm(data: _*)))
      authedRequest                            <- IO(AuthedRequest(profiles, request))
      response                                 <- controller.routes.orNotFound.run(authedRequest)
    } yield expect(
      response.status == InternalServerError &&
      response.headers.get(CIString("Location")).map(_.head).contains(Header.Raw(CIString("Location"), "/recruiter/123/defaults")) &&
      response.cookies.exists(cookie => FlashMessage.decode(cookie.content).map(_.render) == Right(DefaultsError("123").render))
    )
  }

  test("It should return Not_Found when invoking an incorrect uri") {
    for {
      (mockCreditService, mockDefaultsService) <- getMock()
      controller                               <- IO(new DefaultsController[IO](mockCreditService, mockDefaultsService, loggerFactory))
      request                                  <- IO(Request[IO](Method.GET, uri"/recruiter/123/invalid"))
      authedRequest                            <- IO(AuthedRequest(profiles, request))
      response                                 <- controller.routes.orNotFound.run(authedRequest)
    } yield expect(response.status == NotFound)
  }

  def getMock(expectedError: Option[Error] = None): IO[(CreditService[IO], DefaultsService[IO])] = {
    val mockCreditService = new CreditService[IO] {
      override def getEntitlements(recruiterId: RecruiterId): IO[List[Entitlement]] = IO.pure(entitlements)
    }

    val mockDefaultsService = new DefaultsService[IO] {
      override def getDefaults(recruiterId: Defaults.RecruiterId): IO[Defaults] = IO(defaults)

      override def saveDefaults(recruiterId: Defaults.RecruiterId, defaults: Defaults): IO[Unit] = expectedError match {
        case Some(error) => error.raiseError[IO, Unit]
        case None        => IO.unit
      }
    }

    IO((mockCreditService, mockDefaultsService))
  }
}
