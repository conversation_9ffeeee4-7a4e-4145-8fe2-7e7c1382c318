package nl.dpes.job.poster.api.admin.service.creditservice

import cats.effect.IO
import cats.effect.kernel.Resource
import nl.dpes.job.poster.api.admin.service.creditservice.Entitlement.{Credit, Subscription}
import nl.dpes.job.poster.api.admin.service.creditservice.Feature.Booster.Booster
import nl.dpes.job.poster.api.admin.service.creditservice.Feature.Logo
import nl.dpes.job.poster.api.admin.service.creditservice.ProductConfiguration.{
  JobPostingConfiguration,
  JobUpgradeConfiguration,
  PbpConfiguration,
  ProfileViewConfiguration
}
import nl.dpes.job.poster.api.admin.service.creditservice.Requirement.ShouldContainFeature
import nl.dpes.job.poster.api.admin.service.creditservice.Site.NVB
import org.typelevel.log4cats.slf4j.Slf4jFactory
import org.typelevel.log4cats.{Logger, LoggerFactory}
import sttp.client3.SttpBackend
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend
import sttp.client3.testing.SttpBackendStub
import weaver.SimpleIOSuite

import scala.concurrent.duration.DurationInt

object CreditServiceClientSpec extends SimpleIOSuite {

  implicit val loggerFactory: LoggerFactory[IO] = Slf4jFactory.create[IO]
  implicit val loggerIO: Logger[IO]             = loggerFactory.getLogger

  val client: Resource[IO, SttpBackend[IO, Any]] => CreditServiceClient[IO] = CreditServiceClient[IO](_, "http://base-url")

  val recruiterId: RecruiterId = RecruiterId("recruiter-id")

  val entitlements: List[Entitlement] = List(
    Subscription(EntitlementId("a44S900000ZQA1GIAX"), WebshopProduct(ProductName("Cv Database Abonnement"), ProfileViewConfiguration)),
    Credit(
      EntitlementId("a44S9000009qq8GIAQ"),
      WebshopProduct(
        ProductName("Test Vacature Webshop €0,01"),
        JobPostingConfiguration(Set(NVB), Availability(60 days), Set(), Map())
      )
    ),
    Credit(
      EntitlementId("a44S9000009sJkGIAU"),
      WebshopProduct(
        ProductName("Upgrade Campagnebudget € 450,- Intermediair"),
        JobUpgradeConfiguration(Set(Booster), Set(), Requirements(Set(ShouldContainFeature(Logo))))
      )
    ),
    Credit(EntitlementId("a44S900001Nf56QIAR"), WebshopProduct(ProductName("Performance Based Pricing"), PbpConfiguration))
  )

  def createStubbedClientResource(f: SttpBackendStub[IO, Any] => SttpBackendStub[IO, Any]): Resource[IO, SttpBackend[IO, Any]] =
    Resource.make(IO(f(AsyncHttpClientFs2Backend.stub[IO])))(_.close())

  test("It should return entitlements for a recruiter") {
    for {
      result <- client(createStubbedClientResource(_.whenAnyRequest.thenRespond(Right(entitlements))))
        .getEntitlements(recruiterId)
        .attempt
    } yield expect(result == Right(entitlements))
  }
}
