package nl.dpes.job.poster.api.admin.service.creditservice

import io.circe.Json
import io.circe.syntax.EncoderOps
import nl.dpes.job.poster.api.admin.service.creditservice.Feature.Booster
import weaver.FunSuite

import scala.util.{Success, Try}

object FeatureSpec extends FunSuite {

  val featureString         = "logo"
  val invalidFeatureString  = "invalid"
  val feature: Try[Feature] = Feature[Try](featureString)

  test("It should be able to create 'Booster'") {
    expect(Feature[Try]("booster") == Booster[Try]("booster") && Booster[Try]("booster") == Success(Booster.Booster))
  }

  test("It should be able to create 'ValuedBooster'") {
    expect(Feature[Try]("booster_100") == Booster[Try]("booster_100") && Booster[Try]("booster_100") == Success(Booster.ValuedBooster(100)))
  }

  test("It should be able to create a 'Logo'") {
    expect(Feature[Try]("logo") == Success(Feature.Logo))
  }

  test("It should be able to create a 'Highlight'") {
    expect(Feature[Try]("highlight") == Success(Feature.Highlight))
  }

  test("It should be able to create a 'Spotlight'") {
    expect(Feature[Try]("spotlight") == Success(Feature.Spotlight))
  }

  test("It should be able to create a 'Spotlight'") {
    expect(Feature[Try]("spotlight") == Success(Feature.Spotlight))
  }

  test("It should not create an invalid feature") {
    expect(Feature[Try]("invalid").isFailure)
  }

  test("It should be able to encode a feature") {
    expect(feature.get.asJson.toString == s""""$featureString"""")
  }

  test("It should be able to decode a feature") {
    expect(Json.fromString(featureString).as[Feature] == feature.toEither)
  }

  test("It should not decode an invalid feature") {
    expect(
      Json.fromString(invalidFeatureString).as[Feature] match {
        case Left(thr) => thr.getMessage().contains(Feature.DeserializationError(invalidFeatureString).getMessage)
        case Right(_)  => false
      }
    )
  }
}
