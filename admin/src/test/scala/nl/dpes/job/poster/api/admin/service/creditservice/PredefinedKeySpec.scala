package nl.dpes.job.poster.api.admin.service.creditservice

import weaver.FunSuite

import scala.util.{Failure, Success, Try}

object PredefinedKeySpec extends FunSuite {

  val predefinedKey: Try[PredefinedKey] = PredefinedKey.valueOf[Try]("employmenttype")

  test("It should be able to create 'EmploymentType'") {
    expect(predefinedKey == Success(PredefinedKey.EmploymentType))
  }

  test("It should not create an invalid key") {
    expect(PredefinedKey.valueOf[Try]("unknown") == Failure(PredefinedKey.UnknownKey("unknown")))
  }
}
