package nl.dpes.job.poster.api.admin.service.creditservice

import cats.effect.IO
import nl.dpes.job.poster.api.admin.service.creditservice.Entitlement.Subscription
import nl.dpes.job.poster.api.admin.service.creditservice.ProductConfiguration.ProfileViewConfiguration
import org.mockito.ArgumentMatchersSugar.any
import org.mockito.MockitoSugar.{mock, when}
import weaver.SimpleIOSuite

object CreditServiceSpec extends SimpleIOSuite {

  case class Error(message: String) extends Throwable(message)

  val recruiterId: RecruiterId = RecruiterId("A recruiter id")

  val entitlements: List[Entitlement] = List(
    Subscription(EntitlementId("id"), WebshopProduct(ProductName("name"), ProfileViewConfiguration))
  )

  test("It should return the entitlements for a recruiter") {
    for {
      client  <- IO(mock[CreditServiceClient[IO]])
      _       <- IO(when(client.getEntitlements(RecruiterId(any[String]))).thenReturn(IO(entitlements)))
      service <- IO(CreditService(client))
      result  <- service.getEntitlements(recruiterId)
    } yield expect(result == entitlements)
  }

  test("It should return an error when the credit service client encounters an issue") {
    for {
      client  <- IO(mock[CreditServiceClient[IO]])
      _       <- IO(when(client.getEntitlements(RecruiterId(any[String]))).thenReturn(IO.raiseError(Error("An error occurred"))))
      service <- IO(CreditService(client))
      result  <- service.getEntitlements(recruiterId).attempt
    } yield expect(result == Left(Error("An error occurred")))
  }
}
