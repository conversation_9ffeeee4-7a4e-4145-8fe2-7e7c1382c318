package nl.dpes.job.poster.api.admin.service.creditservice

import io.circe.jawn.decode
import io.circe.syntax.EncoderOps
import nl.dpes.job.poster.api.admin.service.creditservice.PredefinedKey.EmploymentType
import nl.dpes.job.poster.api.admin.service.creditservice.ProductConfiguration.JobPostingConfiguration
import weaver.FunSuite

import scala.concurrent.duration.DurationInt
import scala.util.{Failure, Try}

object ProductConfigurationSpec extends FunSuite {

  val jobPostingConfigurationString: String =
    "{\"publishOn\":[\"intermediair\"],\"daysAvailable\":30,\"features\":[\"logo\"],\"predefined\":{\"employmenttype\":\"bijbaan\"}}"

  val jobPostingConfiguration: Try[JobPostingConfiguration] = for {
    site         <- Site.valueOf[Try]("intermediair")
    availability <- Availability[Try](30 days)
    feature      <- Feature[Try]("logo")
    key          <- PredefinedKey.valueOf[Try](EmploymentType.name)
    jobPosting   <- JobPostingConfiguration[Try](Set(site), availability, Set(feature), Map(key -> "bijbaan"))
  } yield jobPosting

  val invalidJobPostingConfiguration: Try[JobPostingConfiguration] = for {
    availability <- Availability[Try](30 days)
    feature      <- Feature[Try]("logo")
    key          <- PredefinedKey.valueOf[Try](EmploymentType.name)
    jobPosting   <- JobPostingConfiguration[Try](Set(), availability, Set(feature), Map(key -> "bijbaan"))
  } yield jobPosting

  test("It should be able to create a 'ProductConfiguration'") {
    expect(jobPostingConfiguration.isSuccess)
  }

  test("It should not create an invalid 'ProductConfiguration'") {
    expect(invalidJobPostingConfiguration == Failure(JobPostingConfiguration.NoSiteProvided))
  }

  test("It should be able to encode a 'ProductConfiguration'") {
    expect(jobPostingConfiguration.get.asJson.noSpaces == jobPostingConfigurationString)
  }

  test("It should be able to decode a 'ProductConfiguration'") {
    expect(decode[JobPostingConfiguration](jobPostingConfigurationString) == jobPostingConfiguration.toEither)
  }

  test("It should not decode an invalid 'ProductConfiguration'") {
    expect(decode[JobPostingConfiguration]("{}").isLeft)
  }
}
