package nl.dpes.job.poster.api.admin.service.creditservice

import io.circe.jawn.decode
import io.circe.syntax.EncoderOps
import nl.dpes.job.poster.api.admin.service.creditservice.Entitlement.{Credit, Subscription}
import nl.dpes.job.poster.api.admin.service.creditservice.PredefinedKey.EmploymentType
import nl.dpes.job.poster.api.admin.service.creditservice.ProductConfiguration.{JobPostingConfiguration, ProfileViewConfiguration}
import weaver.FunSuite

import scala.concurrent.duration.DurationInt
import scala.util.Try

object EntitlementSpec extends FunSuite {

  val creditString =
    "{\"id\":\"*********\",\"product\":{\"name\":\"product\",\"configuration\":{\"JobPostingConfiguration\":{\"publishOn\":[\"intermediair\"],\"daysAvailable\":30,\"features\":[\"logo\"],\"predefined\":{\"employmenttype\":\"bijbaan\"}}}}}"

  val subscriptionString =
    "{\"id\":\"*********\",\"product\":{\"name\":\"product\",\"configuration\":{\"ProfileViewConfiguration\":{}}}}"

  val jobPostingConfiguration: Try[JobPostingConfiguration] = for {
    site         <- Site.valueOf[Try]("intermediair")
    availability <- Availability[Try](30 days)
    feature      <- Feature[Try]("logo")
    key          <- PredefinedKey.valueOf[Try](EmploymentType.name)
    jobPosting   <- JobPostingConfiguration[Try](Set(site), availability, Set(feature), Map(key -> "bijbaan"))
  } yield jobPosting

  val credit: Credit = Credit(EntitlementId("*********"), WebshopProduct(ProductName("product"), jobPostingConfiguration.get))

  val subscription: Subscription =
    Subscription(EntitlementId("*********"), WebshopProduct(ProductName("product"), ProfileViewConfiguration))

  test("It should be able to encode a 'Credit'") {
    expect(credit.asJson.noSpaces == creditString)
  }

  test("It should be able to decode a 'Credit'") {
    expect(decode[Credit](creditString) == Right(credit))
  }

  test("It should be able to encode a 'Subscription'") {
    expect(subscription.asJson.noSpaces == subscriptionString)
  }

  test("It should be able to decode a 'Subscription'") {
    expect(decode[Subscription](subscriptionString) == Right(subscription))
  }
}
