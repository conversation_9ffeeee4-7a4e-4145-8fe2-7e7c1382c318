package nl.dpes.job.poster.api.admin.service.creditservice

import io.circe.Json
import io.circe.syntax.EncoderOps
import weaver.FunSuite

import scala.util.{Failure, Success, Try}

object RequirementSpec extends FunSuite {

  val requirementString: String        = "required_site_intermediair"
  val invalidRequirementString: String = "invalid"
  val requirement: Try[Requirement]    = Requirement[Try](requirementString)

  test("It should be able to create a requirement to include a site") {
    expect(Requirement[Try]("required_site_nationalevacaturebank") == Success(Requirement.ShouldPublishOn(Site.NVB)))
  }

  test("It should be able to create a requirement to include a feature") {
    expect(Requirement[Try]("required_feature_logo") == Success(Requirement.ShouldContainFeature(Feature.Logo)))
  }

  test("It should be able to create a requirement to exclude a feature") {
    expect(Requirement[Try]("required_feature_no_logo") == Success(Requirement.ShouldMissFeature(Feature.Logo)))
  }

  test("It should be able to create a requirement to ignore missing requirements") {
    expect(Requirement[Try]("ignore_empty_requirements") == Success(Requirement.IgnoreMissingRequirements))
  }

  test("It should not create an invalid requirement") {
    expect(Requirement[Try]("invalid") == Failure(Requirement.NotRequirement("invalid")))
  }

  test("It should be able to encode a requirement") {
    expect(requirement.get.asJson.noSpaces == s""""$requirementString"""")
  }

  test("It should be able to decode a requirement") {
    expect(Json.fromString(requirementString).as[Requirement] == requirement.toEither)
  }

  test("It should not decode an invalid requirement") {
    expect(
      Json.fromString(invalidRequirementString).as[Requirement] match {
        case Left(thr) => thr.getMessage().contains(Requirement.NotRequirement(invalidRequirementString).getMessage)
        case Right(_)  => false
      }
    )
  }
}
