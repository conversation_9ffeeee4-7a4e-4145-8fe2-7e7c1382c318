package nl.dpes.job.poster.api.admin.layout

import cats.effect.IO
import nl.dpes.job.poster.api.service.apikey.{Api<PERSON>ey, Integration}
import org.http4s.Request
import org.http4s.dsl.Http4sDsl
import weaver.SimpleIOSuite

object FlashMessageSpec extends SimpleIOSuite with Http4sDsl[IO] {
  import FlashMessage._

  test("a message can be encoded and then decoded") {
    for {
      encoded <- IO(FlashMessage.encode(FlashMessage.Empty))
      decoded <- IO.fromEither(decode(encoded))
    } yield expect(decoded == Empty)
  }

  test("a request without cookies has no flashMessage") {
    for {
      request <- IO(Request[IO]())
      message <- request.flashMessage
    } yield expect(message.isEmpty)
  }

  test("a request with the empty message has no flashMessage") {
    for {
      request <- IO(Request[IO]().addCookie(cookieName, encode(Empty)))
      message <- request.flashMessage
    } yield expect(message.isEmpty)
  }

  test("a request with a message can return that message") {
    for {
      expectedMessage <- IO(ApiKeyRevoked(Integration("For testing")))
      request         <- IO(Request[IO]().addCookie(cookieName, encode(expectedMessage)))
      message         <- request.flashMessage
    } yield expect(message.contains(expectedMessage))
  }

  test("a request with incorrect encoded message returns an error") { // maybe we should just log the issue and return a None
    for {
      request <- IO(Request[IO]().addCookie(cookieName, "not base64"))
      message <- request.flashMessage.attempt
    } yield message match {
      case Left(error)  => expect(error.getMessage == "Illegal base64 character 20")
      case Right(value) => failure(s"Value $value should be a Left")
    }
  }

  test("a request with incorrect encoded message returns an error") { // maybe we should just log the issue and return a None
    for {
      request <- IO(Request[IO]().addCookie(cookieName, "not base64"))
      message <- request.flashMessage.attempt
    } yield message match {
      case Left(error)  => expect(error.getMessage == "Illegal base64 character 20")
      case Right(value) => failure(s"Value $value should be a Left")
    }
  }

  test("when clearing the message an empty message is set — some browsers do not clean up the cookie completely") {
    for {
      response <- Ok().map(_.clearFlashMessage())
      cookie   <- IO.fromOption(response.cookies.find(_.name == cookieName))(new Throwable("FlashMessage cookie not available"))
      message  <- IO.fromEither(decode(cookie.content))
    } yield expect(message == Empty && cookie.path.contains("/"))
  }

  test("when setting a message the cookie is set on the root") {
    for {
      expectedMessage <- IO(ApiKeyCreated(ApiKey("the key"), Integration("The Integration")))
      response        <- Ok().map(_.withFlashMessage(expectedMessage))
      cookie          <- IO.fromOption(response.cookies.find(_.name == cookieName))(new Throwable("FlashMessage cookie not available"))
      actualMessage   <- IO.fromEither(decode(cookie.content))
    } yield expect(cookie.path.contains("/") && actualMessage == expectedMessage)
  }
}
