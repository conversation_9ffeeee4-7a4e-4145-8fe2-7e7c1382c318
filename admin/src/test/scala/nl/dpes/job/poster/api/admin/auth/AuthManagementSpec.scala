package nl.dpes.job.poster.api.admin.auth

import cats.effect.{IO, Resource}
import cats.effect.std.Dispatcher
import nl.dpes.job.poster.api.admin.config.OneLoginConfig
import nl.dpes.job.poster.api.admin.config.OneLoginConfig.{ClientId, DiscoveryUri, EncryptionSecret, Origin, Secret}
import org.http4s._
import org.http4s.dsl.io._
import org.http4s.implicits._
import org.mockito.MockitoSugar
import org.pac4j.core.config.{Config => Pac4jConfig}
import org.pac4j.core.context.session.SessionStore
import org.pac4j.http4s.Http4sWebContext
import weaver.SimpleIOSuite

object AuthManagementSpec extends SimpleIOSuite with MockitoSugar {

  val mockPac4jConfig: Pac4jConfig   = mock[Pac4jConfig]
  val mockSessionStore: SessionStore = mock[SessionStore]

  val testClientId: ClientId                 = ClientId("test-client-id")
  val testSecret: Secret                     = Secret("test-client-secret")
  val testDiscoveryUri: DiscoveryUri         = DiscoveryUri("http://localhost/.well-known/openid-configuration")
  val testOrigin: Origin                     = Origin("http://localhost:1234")
  val testEncryptionSecret: EncryptionSecret = EncryptionSecret("0123456789abcdef0123456789abcdef")

  val testOneLoginConfig: OneLoginConfig = OneLoginConfig(
    clientId = testClientId,
    secret = testSecret,
    discoveryUri = testDiscoveryUri,
    origin = testOrigin,
    encryptionSecret = testEncryptionSecret
  )

  def contextBuilder(dispatcher: Dispatcher[IO]): Request[IO] => Http4sWebContext[IO] =
    Http4sWebContext.withDispatcherInstance[IO](dispatcher)

  test("AuthManagement companion object apply should create an instance") {
    Dispatcher[IO].use { dispatcher =>
      val authManagementResource: Resource[IO, AuthManagement[IO]] =
        AuthManagement[IO](
          config = testOneLoginConfig,
          dispatcher = dispatcher,
          sessionStore = mockSessionStore
        )

      authManagementResource.use { authManagement =>
        IO(expect(authManagement != null))
      }
    }
  }
}
