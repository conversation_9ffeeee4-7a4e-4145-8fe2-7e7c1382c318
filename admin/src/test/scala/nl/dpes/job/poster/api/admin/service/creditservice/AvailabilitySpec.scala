package nl.dpes.job.poster.api.admin.service.creditservice

import io.circe.Json
import io.circe.syntax.EncoderOps
import nl.dpes.job.poster.api.admin.service.creditservice.Availability.AvailabilityTooLow
import weaver.FunSuite

import scala.concurrent.duration.DurationInt
import scala.util.Try

object AvailabilitySpec extends FunSuite {

  val availabilityLong: Int           = 10
  val invalidAvailabilityLong: Int    = -10
  val availability: Try[Availability] = Availability[Try](10 days)

  test("It should be able to create an availability duration") {
    expect(availability.isSuccess)
  }

  test("It should not create an invalid availability duration") {
    expect(Availability[Try](-10 days).isFailure)
  }

  test("It should be able to encode an availability duration") {
    expect(availability.get.asJson.noSpaces == s"""$availabilityLong""")
  }

  test("It should be able to decode an availability duration") {
    expect(Json.fromInt(availabilityLong).as[Availability] == availability.toEither)
  }

  test("It should not decode an invalid availability duration") {
    expect(
      Json.fromInt(invalidAvailabilityLong).as[Availability] match {
        case Left(thr) => thr.getMessage().contains(AvailabilityTooLow(invalidAvailabilityLong days).getMessage)
        case Right(_)  => false
      }
    )
  }
}
