package nl.dpes.job.poster.api.admin.session

import cats.effect._
import cats.effect.std.Dispatcher
import doobie.Transactor
import nl.dpes.job.poster.api.admin.config.OneLoginConfig.EncryptionSecret
import nl.dpes.job.poster.api.admin.testcontainers.DatabaseGenerator
import nl.dpes.job.poster.api.admin.session.Http4sSessionStore.NoSession
import org.http4s.Request
import org.pac4j.core.context.session.SessionStore
import org.pac4j.core.context.{<PERSON><PERSON>, WebContext}
import org.pac4j.core.util.Pac4jConstants
import org.pac4j.http4s.Http4sWebContext
import org.typelevel.log4cats.slf4j.Slf4jFactory
import org.typelevel.log4cats.{Logger, LoggerFactory}
import weaver.IOSuite

import java.util
import java.util.Optional

object Pac4jWrapperSpec extends IOSuite with DatabaseGenerator {
  override type Res = Transactor[IO]

  implicit val loggerFactory: LoggerFactory[IO] = Slf4jFactory.create[IO]
  implicit val loggerIO: Logger[IO]             = loggerFactory.getLogger

  def sessionDbF(xa: Transactor[IO]): IO[SessionDb[IO]] =
    IO(SessionDb.mySql(xa, EncryptionSecret("the secret"))).flatTap(_.initialize)

  def sessionStoreF(db: SessionDb[IO], dispatcher: Dispatcher[IO]): IO[Pac4jWrapper[IO]] =
    IO(Http4sSessionStore[IO](db, SessionCookie().builder)).map(Pac4jWrapper(_, dispatcher))

  val getExistingSessionIdFromAttributes: String => Optional[AnyRef] = str => Optional.of("the attribute session id")
  val getNoSessionIdFromAttributes: String => Optional[AnyRef]       = str => Optional.ofNullable(null)

  val setNoSessionIdInAttributes: Any => Unit = str => ()

  def getMutableSessionIdFromAttributes(ref: Ref[IO, Optional[AnyRef]], dispatcher: Dispatcher[IO]): String => Optional[AnyRef] = str =>
    dispatcher.unsafeRunSync(ref.get)

  def setMutableSessionIdFromAttributes(ref: Ref[IO, Optional[AnyRef]], dispatcher: Dispatcher[IO]): Any => Unit = value =>
    dispatcher.unsafeRunSync(ref.set(Optional.ofNullable(value.asInstanceOf[AnyRef])))

  val getExistingSessionIdFromCookies: util.Collection[Cookie] =
    java.util.Collections.singletonList(new Cookie(Pac4jConstants.SESSION_ID, "the cookie session id"))

  def createMutableContext(dispatcher: Dispatcher[IO]): MockedWebContext = dispatcher.unsafeRunSync(for {
    ref              <- Ref.of[IO, Optional[AnyRef]](Optional.ofNullable(null))
    getIdFromContext <- IO(getMutableSessionIdFromAttributes(ref, dispatcher))
    setIdInContext   <- IO(setMutableSessionIdFromAttributes(ref, dispatcher))
    context          <- IO(new MockedWebContext(getIdFromContext, setIdInContext))
  } yield context)

  class MockedWebContext(
    getSessionId: String => Optional[AnyRef] = getNoSessionIdFromAttributes,
    setSessionId: Any => Unit = setNoSessionIdInAttributes,
    cookies: util.Collection[Cookie] = java.util.Collections.emptyList()
  ) extends Http4sWebContext[IO](Request.apply[IO](), _ => "") {
    override def getRequestAttribute(name: String): Optional[AnyRef] = getSessionId(name)

    override def setRequestAttribute(name: String, value: Any): Unit = setSessionId(value)
    override def getRequestCookies: util.Collection[Cookie]          = cookies
  }

  override def sharedResource: Resource[IO, Res] = transactor

  // get session id
  test("session can be retrieved from context attributes") { xa =>
    val unusedCreateIdFlag: Boolean = false
    val context: WebContext         = new MockedWebContext(getExistingSessionIdFromAttributes)

    Dispatcher.parallel[IO].use { dispatcher =>
      for {
        db    <- sessionDbF(xa)
        store <- sessionStoreF(db, dispatcher)
      } yield expect(store.getSessionId(context, unusedCreateIdFlag).get() == "the attribute session id")
    }
  }

  test("session can be retrieved from context cookie when not found in the attributes") { xa =>
    val unusedCreateIdFlag: Boolean = false
    val context: WebContext         = new MockedWebContext(cookies = getExistingSessionIdFromCookies)

    Dispatcher.parallel[IO].use { dispatcher =>
      for {
        db    <- sessionDbF(xa)
        store <- sessionStoreF(db, dispatcher)
      } yield expect(store.getSessionId(context, unusedCreateIdFlag).get() == "the cookie session id")
    }
  }

  test("when no session in context then a new one is created") { xa =>
    val unusedCreateIdFlag: Boolean = false
    val context: WebContext         = new MockedWebContext() // no session id in the context
    val uuidLength                  = "c001eec3-803f-4dbf-9ee3-add294d98c0e".length

    Dispatcher.parallel[IO].use { dispatcher =>
      for {
        db    <- sessionDbF(xa)
        store <- sessionStoreF(db, dispatcher)
      } yield expect(store.getSessionId(context, unusedCreateIdFlag).get().length == uuidLength)
    }
  }

  // get session value
  test("Get nothing when no value in the session") { xa =>
    implicit val context: WebContext = new MockedWebContext() // no session id in the context, so we create one

    Dispatcher.parallel[IO].use { dispatcher =>
      for {
        db    <- sessionDbF(xa)
        store <- sessionStoreF(db, dispatcher)
      } yield expect(store.get(context, "not existing") == Optional.ofNullable(null))
    }
  }

  test("Get nothing when no session") { xa =>
    implicit val unusedCreateIdFlag: Boolean = false
    implicit val context: WebContext =
      new MockedWebContext(getExistingSessionIdFromAttributes) // session found will not create in database (for now)

    Dispatcher.parallel[IO].use { dispatcher =>
      for {
        db    <- sessionDbF(xa)
        store <- sessionStoreF(db, dispatcher)
      } yield expect(store.get(context, "not existing") == Optional.ofNullable(null))
    }
  }

  test("Get value when available in session") { xa =>
    implicit val unusedCreateIdFlag: Boolean = false

    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db    <- sessionDbF(xa)
        store <- sessionStoreF(db, dispatcher)
        _     <- IO(store.set(context, "existing", "a value"))
      } yield expect(store.get(context, "existing").get == "a value")
    }
  }

  // set session value
  test("Setting value in not existing session works") { xa => // this is actually last test
    implicit val unusedCreateIdFlag: Boolean = false

    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db    <- sessionDbF(xa)
        store <- sessionStoreF(db, dispatcher)
        _     <- IO(store.set(context, "existing", "a value"))
      } yield expect(store.get(context, "existing").get == "a value")
    }
  }

  test("Setting value in existing session works") { xa =>
    val unusedCreateIdFlag: Boolean = false

    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db    <- sessionDbF(xa)
        store <- sessionStoreF(db, dispatcher)
        _     <- IO(store.getSessionId(context, unusedCreateIdFlag)) // will create a session
        _     <- IO(store.set(context, "existing", "a value"))
      } yield expect(store.get(context, "existing").get == "a value")
    }
  }

  test("Setting value overrides existing value") { xa =>
    implicit val unusedCreateIdFlag: Boolean = false

    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db    <- sessionDbF(xa)
        store <- sessionStoreF(db, dispatcher)
        _     <- IO(store.set(context, "existing", "a value"))
        _     <- IO(store.set(context, "existing", "a second value"))
      } yield expect(store.get(context, "existing").get == "a second value")
    }
  }

  test("Setting 'null' removes value and key from session") { xa =>
    implicit val unusedCreateIdFlag: Boolean = false

    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db             <- sessionDbF(xa)
        store          <- sessionStoreF(db, dispatcher)
        id             <- IO(SessionId(store.getSessionId(context, unusedCreateIdFlag).get))
        _              <- IO(store.set(context, "existing", "a value"))
        initialSession <- db.getSession(id)
        _              <- IO(store.set(context, "existing", null))
        finalSession   <- db.getSession(id)
      } yield expect(initialSession.data.contains("existing") && !finalSession.data.contains("existing"))
    }
  }

  // destroy session
  test("Destroying session removes session from db, cookies and attributes") { xa =>
    implicit val unusedCreateIdFlag: Boolean = false

    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db             <- sessionDbF(xa)
        store          <- sessionStoreF(db, dispatcher)
        id             <- IO(SessionId(store.getSessionId(context, unusedCreateIdFlag).get))
        initialSession <- db.getSession(id)
        _              <- IO(store.destroySession(context))
        finalSession   <- db.getSession(id).attempt
      } yield expect(
        initialSession == Session() &&
        finalSession == Left(NoSession(id)) &&
        !context.getRequestAttribute("pac4jSessionId").isPresent
        // todo check cookie is removed
      )
    }
  }

  // gettrackable session
  test("Get trackable session gets a new session if it did not exist yet") { xa =>
    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db      <- sessionDbF(xa)
        store   <- sessionStoreF(db, dispatcher)
        session <- IO(store.getTrackableSession(context))
      } yield expect(session.get == Session())
    }
  }

  test("Get trackable session returns the current session") { xa =>
    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db      <- sessionDbF(xa)
        store   <- sessionStoreF(db, dispatcher)
        _       <- IO(store.set(context, "theKey", "the value"))
        session <- IO(store.getTrackableSession(context))
      } yield expect(
        session.get
          .asInstanceOf[Session]
          .data
          .get("theKey")
          .map(Session.deserialise)
          .contains("the value")
      )
    }
  }

  // buildfromsession
  test("Create a session from given value") { xa =>
    val unusedCreateIdFlag: Boolean = false
    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db      <- sessionDbF(xa)
        store   <- sessionStoreF(db, dispatcher)
        _       <- IO(store.buildFromTrackableSession(context, Session(Map("aKey" -> "aValue"))))
        id      <- IO(store.getSessionId(context, unusedCreateIdFlag))
        session <- db.getSession(SessionId(id.get))
      } yield expect(session == Session(Map("aKey" -> "aValue")))
    }
  }

  test("Destroy old session") { xa =>
    val unusedCreateIdFlag: Boolean = false
    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db             <- sessionDbF(xa)
        store          <- sessionStoreF(db, dispatcher)
        _              <- IO(store.buildFromTrackableSession(context, Session(Map("aKey" -> "aValue"))))
        initialId      <- IO(store.getSessionId(context, unusedCreateIdFlag))
        _              <- IO(store.buildFromTrackableSession(context, Session(Map("aKey" -> "aNewValue"))))
        finalId        <- IO(store.getSessionId(context, unusedCreateIdFlag))
        initialSession <- db.getSession(SessionId(initialId.get)).attempt // should be gone
        finalSession   <- db.getSession(SessionId(finalId.get))
      } yield expect(finalSession == Session(Map("aKey" -> "aNewValue")) && initialSession == Left(NoSession(SessionId(initialId.get))))
    }
  }

  // renew session
  test("New session id will be created and old data will be reuses") { xa =>
    implicit val unusedCreateIdFlag: Boolean = false
    Dispatcher.parallel[IO].use { dispatcher =>
      implicit val context: WebContext = createMutableContext(dispatcher)
      for {
        db             <- sessionDbF(xa)
        store          <- sessionStoreF(db, dispatcher)
        _              <- IO(store.buildFromTrackableSession(context, Session(Map("aKey" -> "aValue"))))
        initialId      <- IO(store.getSessionId(context, unusedCreateIdFlag))
        _              <- IO(store.renewSession(context))
        finalId        <- IO(store.getSessionId(context, unusedCreateIdFlag))
        initialSession <- db.getSession(SessionId(initialId.get)).attempt // should be gone
        finalSession   <- db.getSession(SessionId(finalId.get))
      } yield expect(finalSession == Session(Map("aKey" -> "aValue")) && initialSession == Left(NoSession(SessionId(initialId.get))))
    }
  }
}
