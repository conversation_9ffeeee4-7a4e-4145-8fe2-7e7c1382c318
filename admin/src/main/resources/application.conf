blaze-config {
    host = 0.0.0.0
    port = 11363
}

one-login-config {
    client-id = ${OIDC_CLIENT_ID}
    secret = ${OIDC_SECRET}
    discovery-uri = ${OIDC_DISCOVERY_URL}
    origin = ${OIDC_ORIGIN}
    encryption-secret = ${?ENCRYPTION_SECRET}
}

db-config {
    thread-count = 1
    thread-count = ${?DB_THREAD_COUNT}
    url = ${?DB_CONNECTION_URL}
    user-name = ${?DB_USER_NAME}
    user-password = ${?DB_PASSWORD}
}

api-key-config {
    salt = ${?API_KEY_SALT}
}

database {
    dataSourceClassName = com.mysql.cj.jdbc.MysqlDataSource
    dataSource {
        url = ${?DB_CONNECTION_URL}
        user = ${?DB_USER_NAME}
        password = ${?DB_PASSWORD}
        cachePrepStmts = true
        prepStmtCacheSize = 250
        prepStmtCacheSqlLimit = 2048
    }
    maximumPoolSize = 1
    maximumPoolSize = ${?DB_MAX_POOL_SIZE}
}

recruiter-service-config {
  host = ${?B2B_SALESFORCE_GATEWAY_SERVICE_HOST}
  port = 11310
}

credit-service-config {
  host = ${?CREDIT_SERVICE_HOST}
}
