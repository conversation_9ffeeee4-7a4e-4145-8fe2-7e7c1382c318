package nl.dpes.job.poster.api.admin.service.creditservice

import cats.ApplicativeThrow
import cats.implicits._
import io.circe.{KeyDecoder, KeyEncoder}

import scala.util.Try

sealed trait PredefinedKey {
  def name: String
}

object PredefinedKey {

  case object EmploymentType extends PredefinedKey {
    override def name: String = "employmenttype"
  }

  case class UnknownKey(keyName: String) extends Throwable(s"Unknown predefined key '$keyName'")

  def valueOf[F[_]: ApplicativeThrow](source: String): F[PredefinedKey] = source.toLowerCase match {
    case "employmenttype" => ApplicativeThrow[F].pure(EmploymentType)
    case _                => ApplicativeThrow[F].raiseError(Unknown<PERSON><PERSON>(source))
  }

  implicit lazy val jsonEncoder: KeyEncoder[PredefinedKey] = _.name
  implicit lazy val jsonDecoder: KeyDecoder[PredefinedKey] = (key: String) => valueOf[Try](key).toOption
}
