package nl.dpes.job.poster.api.admin.service.creditservice

import cats.effect.{Async, Resource}
import cats.implicits._
import org.typelevel.log4cats.Logger
import sttp.client3.{basicRequest, SttpBackend}
import sttp.client3._
import sttp.client3.circe._

trait CreditServiceClient[F[_]] {

  def getEntitlements(recruiterId: RecruiterId): F[List[Entitlement]]
}

object CreditServiceClient {

  def apply[F[_]: Async: Logger](sttpBackend: Resource[F, SttpBackend[F, Any]], uri: String): CreditServiceClient[F] =
    new CreditServiceClient[F] {

      override def getEntitlements(recruiterId: RecruiterId): F[List[Entitlement]] =
        for {
          _ <- Logger[F].info(s"Getting entitlements for recruiter '${recruiterId.value}'")
          result <- sttpBackend.use { backend =>
            basicRequest
              .get(uri"$uri/recruiter/${recruiterId.value}/entitlements")
              .response(asJson[Seq[Entitlement]])
              .send(backend)
              .map(_.body.map(_.toList))
              .rethrow
              .onError { case e => Logger[F].error(e.getMessage) }
          }
        } yield result
    }
}
