package nl.dpes.job.poster.api.admin.config

import nl.dpes.job.poster.api.admin.apikey.ApiKeyConfig
import nl.dpes.job.poster.api.database.DbConfig
import pureconfig._
import pureconfig.generic.semiauto._

case class AppConfig(
  blazeConfig: BlazeConfig,
  oneLoginConfig: OneLoginConfig,
  dbConfig: DbConfig,
  apiKeyConfig: ApiKeyConfig,
  recruiterServiceConfig: RecruiterServiceConfig,
  creditServiceConfig: CreditServiceConfig
)

object AppConfig {
  implicit val appConfigReader: ConfigReader[AppConfig] = deriveReader[AppConfig]
}
