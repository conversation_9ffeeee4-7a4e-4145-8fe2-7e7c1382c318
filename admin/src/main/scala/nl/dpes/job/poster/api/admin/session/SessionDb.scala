package nl.dpes.job.poster.api.admin.session

import cats.effect._
import cats.implicits._
import doobie.implicits._
import doobie.util.transactor.Transactor
import nl.dpes.job.poster.api.admin.config.OneLoginConfig._
import nl.dpes.job.poster.api.admin.session.Http4sSessionStore.NoSession

trait SessionDb[F[_]] {
  def initialize: F[Unit]

  def getSession(id: SessionId): F[Session]

  def storeSession(id: SessionId, session: Session): F[Unit]

  def removeSession(id: SessionId): F[Boolean]
}

object SessionDb {

  def mySql[F[_]: MonadCancelThrow](xa: Transactor[F], secret: EncryptionSecret): SessionDb[F] = new SessionDb[F] {

    override def initialize: F[Unit] =
      sql"""
        CREATE TABLE IF NOT EXISTS sessions (
          id  VARCHAR(36) PRIMARY KEY,
          session BLOB NOT NULL,
          lastAccess TIMESTAMP NOT NULL DEFAULT now()
        )
      """.update.run.transact(xa).void

    override def getSession(id: SessionId): F[Session] = for {
      maybeSession <- sql"""
        SELECT aes_decrypt(session, ${id.value + secret.value})
        FROM sessions
        WHERE id = $id
       """.query[Session].option.transact(xa)
      session      <- MonadCancelThrow[F].fromOption(maybeSession, NoSession(id))
    } yield session

    private def insertSession(id: SessionId, session: Session): F[Unit] =
      sql"""
        INSERT INTO sessions (id, session)
        VALUES ($id, aes_encrypt($session, ${id.value + secret.value}))
      """.update.run.transact(xa).void

    private def updateSession(id: SessionId, session: Session): F[Unit] =
      sql"""
        UPDATE sessions
        SET session = aes_encrypt($session, ${id.value + secret.value})
        WHERE id = $id
         """.update.run.transact(xa).void

    override def storeSession(id: SessionId, session: Session): F[Unit] =
      insertSession(id, session) orElse updateSession(id, session)

    override def removeSession(id: SessionId): F[Boolean] =
      sql"DELETE FROM sessions WHERE id = $id".update.run.transact(xa).map(_ > 0)
  }

  def mySqlResource[F[_]: MonadCancelThrow](xa: Transactor[F], encryptionSecret: EncryptionSecret): Resource[F, SessionDb[F]] =
    Resource.eval {
      for {
        db <- mySql(xa, encryptionSecret).pure[F]
        _  <- db.initialize
      } yield db
    }
}
