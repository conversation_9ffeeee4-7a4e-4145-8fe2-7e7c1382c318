package nl.dpes.job.poster.api.admin.apikey

import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.service.apikey.{ApiKeyId, ApiKeyInformation, Integration}
import scalatags.Text.all._

case class DeleteApiKeyPage(recruiterId: SalesForceId, keyInfo: ApiKeyInformation) {

  def render: Modifier =
    div(`class` := "warn")(
      h2(s"Revoke API key ‘${keyInfo.integration.value}’ for recruiter ${recruiterId.idWithChecksum}"),
      p(s"Are you sure you want to revoke this API key?"),
      p(s"All integrations using this key will stop working"),
      form(method := "post")(
        p(a(href := s"/recruiter/${recruiterId.idWithChecksum}/apiKeys")("Cancel"), " ", input(`type` := "submit", value := "Revoke key"))
      )
    )
}
