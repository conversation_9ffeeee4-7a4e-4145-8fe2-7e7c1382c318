package nl.dpes.job.poster.api.admin.service.creditservice

import io.circe.{Decoder, Encoder}
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}

case class WebshopProduct(name: ProductName, configuration: ProductConfiguration)

object WebshopProduct {
  implicit lazy val jsonEncoder: Encoder[WebshopProduct] = deriveEncoder
  implicit lazy val jsonDecoder: Decoder[WebshopProduct] = deriveDecoder
}
