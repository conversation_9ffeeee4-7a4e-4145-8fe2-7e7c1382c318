package nl.dpes.job.poster.api.admin.apikey

import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.admin.apikey.ApiKeysPage.ApiKeyEntry
import nl.dpes.job.poster.api.service.apikey.ApiKeyInformation
import scalatags.Text
import scalatags.Text.all._

import java.time.format.DateTimeFormatter
import java.time.{LocalDateTime, ZoneId}

case class ApiKeysPage(recruiterId: SalesForceId, keys: List[ApiKeyInformation]) {
  private val createApiKeyLink: Modifier = p(a(href := s"/recruiter/${recruiterId.idWithChecksum}/apiKeys/create")("Create a new API key"))

  def render: Modifier = Seq(
    h2(s"API keys for recruiter ${recruiterId.idWithChecksum}"),
    keys match {
      case Nil =>
        div(
          p(s"Recruiter ${recruiterId.idWithChecksum} has no API keys yet"),
          createApiKeyLink
        )
      case _ => div(table(th("API key name"), th("Created"), keys.map(ApiKeyEntry.apply).map(_.render(recruiterId))), createApiKeyLink)
    }
  )
}

object ApiKeysPage {

  case class ApiKeyEntry(info: ApiKeyInformation) {

    private def revokeApiKeyLink(recruiterId: SalesForceId): Modifier =
      a(href := s"/recruiter/${recruiterId.idWithChecksum}/apiKey/${info.id.value}/revoke")("Revoke")

    def render(recruiterId: SalesForceId): Modifier = tr(
      td(info.integration.value),
      td(
        DateTimeFormatter
          .ofPattern("YYYY-MM-dd HH:mm:ss")
          .format(LocalDateTime.ofInstant(info.createdAt.value.toInstant, ZoneId.of("CET")))
      ),
      td(revokeApiKeyLink(recruiterId))
    )
  }
}
