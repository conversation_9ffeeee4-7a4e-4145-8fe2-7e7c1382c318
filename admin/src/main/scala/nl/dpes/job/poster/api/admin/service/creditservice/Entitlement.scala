package nl.dpes.job.poster.api.admin.service.creditservice

import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.{Decoder, Encoder}

sealed trait Entitlement

object Entitlement {

  case class Subscription(id: EntitlementId, product: WebshopProduct) extends Entitlement

  object Subscription {
    implicit val subscriptionEncoder: Encoder[Subscription] = deriveEncoder
    implicit val subscriptionDecoder: Decoder[Subscription] = deriveDecoder
  }

  case class Credit(id: EntitlementId, product: WebshopProduct) extends Entitlement

  object Credit {
    implicit val creditEncoder: Encoder[Credit] = deriveEncoder
    implicit val creditDecoder: Decoder[Credit] = deriveDecoder
  }

  implicit val encoder: Encoder[Entitlement] = deriveEncoder
  implicit val decoder: Decoder[Entitlement] = deriveDecoder
}
