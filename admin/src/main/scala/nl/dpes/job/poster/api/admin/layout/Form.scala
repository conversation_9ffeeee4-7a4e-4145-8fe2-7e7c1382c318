package nl.dpes.job.poster.api.admin.layout

import scalatags.Text.TypedTag
import scalatags.Text.all._

object Form {

  def selectFromOptions(
    fieldName: String,
    displayName: String,
    preselectedValue: Option[String],
    options: List[String],
    onChangeEvent: Modifier = onchange := ""
  ): TypedTag[String] =
    div(`class` := "container-fluid")(
      div(`class` := "row g-1 mb-3")(
        label(`class` := "col-1 col-form-label", `for` := displayName, displayName),
        div(`class` := "col-sm-4")(
          select(`class` := "form-select", name := fieldName, id := fieldName, onChangeEvent)(
            ("Select from options" +: options.distinct).map(opt =>
              if (preselectedValue.contains(opt)) option(value := opt, selected := true)(opt) else option(value := opt)(opt)
            )
          )
        )
      )
    )

  def checkbox(fieldName: String, displayName: String, isChecked: Boolean): TypedTag[String] =
    div(`class` := "container-fluid")(
      div(`class` := "row g-1 mb-3")(
        label(`class` := "col-1 col-form-label", `for` := displayName, displayName),
        div(`class` := "col-sm-4")(
          div(
            input(
              `type` := "checkbox",
              id := fieldName,
              name := fieldName,
              `class` := "mt-2",
              if (isChecked) checked := true else ()
            )
          )
        )
      )
    )
}
