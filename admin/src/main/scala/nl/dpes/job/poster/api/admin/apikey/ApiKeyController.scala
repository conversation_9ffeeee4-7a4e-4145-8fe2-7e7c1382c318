package nl.dpes.job.poster.api.admin.apikey

import cats.Parallel
import cats.effect.{Async, Resource}
import cats.implicits._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.admin.apikey.ApiKeyController.redirectTo
import nl.dpes.job.poster.api.admin.apikey.CreateApiKeyPage.CreateApiKeyForm
import nl.dpes.job.poster.api.admin.auth._
import nl.dpes.job.poster.api.admin.layout.FlashMessage._
import nl.dpes.job.poster.api.admin.layout.MainPage
import nl.dpes.job.poster.api.service.apikey.ApiKeyRepository.ApiKeyNotFound
import nl.dpes.job.poster.api.service.apikey.{ApiKey, ApiKeyId, ApiKeyRepository}
import nl.dpes.job.poster.api.service.recruiter.RecruiterService
import org.http4s.FormDataDecoder.formEntityDecoder
import org.http4s.dsl.Http4sDsl
import org.http4s.headers.{`Content-Type`, Location}
import org.http4s.{AuthedRoutes, Response, Status, Uri}

class ApiKeyController[F[_]: Async: Parallel](repository: ApiKeyRepository[F], recruiterService: RecruiterService[F]) extends Http4sDsl[F] {

  private val handleIncorrectRecruiterId: PartialFunction[Throwable, F[Response[F]]] = { case e: SalesForceId.Error =>
    BadRequest(s"Incorrect SalesForce id: ${e.getMessage}")
      .map(_.withContentType(`Content-Type`(org.http4s.MediaType.text.html)))
  }

  private val handleApiKeyNotFound: PartialFunction[Throwable, F[Response[F]]] = { case e: ApiKeyNotFound =>
    NotFound(s"Incorrect SalesForce id: ${e.getMessage}")
      .map(_.withContentType(`Content-Type`(org.http4s.MediaType.text.html)))
  }

  private val apiKeyIndex: SecureRoute[F] = AuthedRoutes.of { case req @ GET -> Root / "recruiter" / recruiterId / "apiKeys" as user =>
    (for {
      message <- req.req.flashMessage
      id      <- Async[F].fromEither(SalesForceId(recruiterId))
      apiKeys <- repository.readRecruiterKeyInformation(id)
      resp    <- Ok(MainPage(ApiKeysPage(id, apiKeys).render, user.displayName, message).render)
    } yield resp
      .withContentType(`Content-Type`(org.http4s.MediaType.text.html))
      .clearFlashMessage())
      .recoverWith(handleIncorrectRecruiterId)
  }

  private val createApiKey: SecureRoute[F] = AuthedRoutes.of {
    case GET -> Root / "recruiter" / recruiterId / "apiKeys" / "create" as user =>
      (for {
        id       <- Async[F].fromEither(SalesForceId(recruiterId))
        response <- Ok(MainPage(CreateApiKeyPage(id, None).render, user.displayName).render)
      } yield response.withContentType(`Content-Type`(org.http4s.MediaType.text.html)))
        .recoverWith(handleIncorrectRecruiterId)

    case req @ POST -> Root / "recruiter" / recruiterId / "apiKeys" / "create" as _ =>
      import CreateApiKeyForm._
      (for {
        id       <- Async[F].fromEither(SalesForceId(recruiterId))
        data     <- req.req.as[CreateApiKeyForm]
        apiKey   <- ApiKey.generate
        _        <- repository.storeKey(id, apiKey, data.integrationName)
        response <- Async[F].delay(redirectTo(s"/recruiter/$recruiterId/apiKeys", SeeOther))
      } yield response.withFlashMessage(ApiKeyCreated(apiKey, data.integrationName)))
        .recoverWith(handleIncorrectRecruiterId)
  }

  private val revokeApiKey: SecureRoute[F] = AuthedRoutes.of {
    case GET -> Root / "recruiter" / recruiterId / "apiKey" / apiKeyId / "revoke" as user =>
      (for {
        id       <- Async[F].fromEither(SalesForceId(recruiterId))
        keyInfo  <- repository.readRecruiterKeyInformation(id, ApiKeyId(apiKeyId))
        response <- Ok(MainPage(DeleteApiKeyPage(id, keyInfo).render, user.displayName).render)
      } yield response.withContentType(`Content-Type`(org.http4s.MediaType.text.html)))
        .recoverWith(handleIncorrectRecruiterId)
        .recoverWith(handleApiKeyNotFound)

    case POST -> Root / "recruiter" / recruiterId / "apiKey" / apiKeyId / "revoke" as _ =>
      (for {
        id       <- Async[F].fromEither(SalesForceId(recruiterId))
        keyInfo  <- repository.readRecruiterKeyInformation(id, ApiKeyId(apiKeyId))
        _        <- repository.removeKey(id, ApiKeyId(apiKeyId))
        response <- Async[F].delay(redirectTo(s"/recruiter/$recruiterId/apiKeys", SeeOther))
      } yield response.withFlashMessage(ApiKeyRevoked(keyInfo.integration)))
        .recoverWith(handleIncorrectRecruiterId)
        .recoverWith(handleApiKeyNotFound)
  }

  def routes: SecureRoute[F] = apiKeyIndex <+> createApiKey <+> revokeApiKey
}

object ApiKeyController {

  def redirectTo[F[_]: Async](path: String, status: Status): Response[F] =
    Response[F]()
      .withStatus(status)
      .withHeaders(Location(Uri.unsafeFromString(path)))

  def resource[F[_]: Async: Parallel](
    apiKeyRepository: ApiKeyRepository[F],
    recruiterService: RecruiterService[F]
  ): Resource[F, ApiKeyController[F]] =
    Resource.pure(new ApiKeyController[F](apiKeyRepository, recruiterService))
}
