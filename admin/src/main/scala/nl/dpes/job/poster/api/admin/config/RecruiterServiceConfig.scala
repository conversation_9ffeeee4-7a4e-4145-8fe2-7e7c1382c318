package nl.dpes.job.poster.api.admin.config

import nl.dpes.job.poster.api.admin.config.RecruiterServiceConfig._
import pureconfig._
import pureconfig.generic.semiauto.deriveReader

case class RecruiterServiceConfig(host: Host, port: Port)

object RecruiterServiceConfig {
  case class Host(value: String) extends AnyVal
  implicit val hostConfigReader: ConfigReader[Host] = ConfigReader[String].map(Host.apply)

  case class Port(value: Int) extends AnyVal
  implicit val portConfigReader: ConfigReader[Port] = ConfigReader[Int].map(Port.apply)

  implicit val recruiterServiceConfigReader: ConfigReader[RecruiterServiceConfig] = deriveReader[RecruiterServiceConfig]
}
