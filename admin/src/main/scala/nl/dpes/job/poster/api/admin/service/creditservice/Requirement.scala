package nl.dpes.job.poster.api.admin.service.creditservice

import cats.ApplicativeThrow
import cats.implicits._
import io.circe.{Decoder, Encoder}

import scala.util.Try
import scala.util.matching.Regex

sealed trait Requirement extends Product with Serializable {
  val serialized: String
}

object Requirement {

  case class ShouldPublishOn(site: Site) extends Requirement {
    override val serialized: String = s"required_site_${site.name}"
  }

  case class ShouldContainFeature(feature: Feature) extends Requirement {
    override val serialized: String = s"required_feature_${feature.name}"
  }

  case class ShouldMissFeature(feature: Feature) extends Requirement {
    override val serialized: String = s"required_feature_no_${feature.name}"
  }

  case object IgnoreMissingRequirements extends Requirement {
    override val serialized: String = "ignore_empty_requirements"
  }

  case class DeserializationError(requirement: String) extends Throwable(s"Could not deserialize '$requirement' to a Requirement")
  case class NotRequirement(requirement: String)       extends Throwable(s"'$requirement' is not a Requirement")

  private val excludingFeaturePattern: Regex = "^required_feature_no_(.+)$".r
  private val includingFeaturePattern: Regex = "^required_feature_(.+)$".r
  private val includingSitePattern: Regex    = "^required_site_(.+)$".r

  def apply[F[_]: ApplicativeThrow](requirement: String): F[Requirement] = requirement match {
    case ignore if ignore == IgnoreMissingRequirements.serialized => ApplicativeThrow[F].pure(IgnoreMissingRequirements).widen
    case excludingFeaturePattern(feature)                         => Feature[F](feature).map(ShouldMissFeature.apply)
    case includingFeaturePattern(feature)                         => Feature[F](feature).map(ShouldContainFeature.apply)
    case includingSitePattern(site)                               => Site.valueOf[F](site).map(ShouldPublishOn.apply)
    case _                                                        => ApplicativeThrow[F].raiseError(NotRequirement(requirement))
  }

  implicit lazy val jsonEncoder: Encoder[Requirement] = Encoder.encodeString.contramap(_.serialized)
  implicit lazy val jsonDecoder: Decoder[Requirement] = Decoder.decodeString.emapTry(Requirement[Try])
}
