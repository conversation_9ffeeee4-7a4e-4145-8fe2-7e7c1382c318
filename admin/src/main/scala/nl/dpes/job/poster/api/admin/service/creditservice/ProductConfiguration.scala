package nl.dpes.job.poster.api.admin.service.creditservice

import cats.ApplicativeThrow
import io.circe.{Decoder, Encoder}
import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}

sealed trait ProductConfiguration

object ProductConfiguration {

  case class JobPostingConfiguration private (
    publishOn: Set[Site],
    daysAvailable: Availability,
    features: Set[Feature],
    predefined: Map[PredefinedKey, String]
  ) extends ProductConfiguration

  object JobPostingConfiguration {
    case object NoSiteProvided extends Throwable(s"No site provided for job posting product")

    def apply[F[_]: ApplicativeThrow](
      publishOn: Set[Site],
      availability: Availability,
      features: Set[Feature],
      predefined: Map[PredefinedKey, String]
    ): F[JobPostingConfiguration] =
      if (publishOn.isEmpty) ApplicativeThrow[F].raiseError(NoSiteProvided)
      else ApplicativeThrow[F].pure(new JobPostingConfiguration(publishOn, availability, features, predefined))

    implicit lazy val jsonEncoder: Encoder[JobPostingConfiguration] = deriveEncoder[JobPostingConfiguration]
    implicit lazy val jsonDecoder: Decoder[JobPostingConfiguration] = deriveDecoder[JobPostingConfiguration]
  }

  case class JobUpgradeConfiguration(features: Set[Feature], sites: Set[Site], requirements: Requirements) extends ProductConfiguration

  object JobUpgradeConfiguration {
    implicit lazy val jsonEncoder: Encoder[JobUpgradeConfiguration] = deriveEncoder[JobUpgradeConfiguration]
    implicit lazy val jsonDecoder: Decoder[JobUpgradeConfiguration] = deriveDecoder[JobUpgradeConfiguration]
  }

  type ProfileViewConfiguration = ProfileViewConfiguration.type

  case object ProfileViewConfiguration extends ProductConfiguration {
    implicit lazy val jsonEncoder: Encoder[ProfileViewConfiguration] = deriveEncoder[ProfileViewConfiguration]
    implicit lazy val jsonDecoder: Decoder[ProfileViewConfiguration] = deriveDecoder[ProfileViewConfiguration]
  }

  type PbpConfiguration = PbpConfiguration.type

  case object PbpConfiguration extends ProductConfiguration {
    implicit lazy val jsonEncoder: Encoder[PbpConfiguration] = deriveEncoder[PbpConfiguration]
    implicit lazy val jsonDecoder: Decoder[PbpConfiguration] = deriveDecoder[PbpConfiguration]
  }

  implicit lazy val jsonProdConfEncoder: Encoder[ProductConfiguration] = deriveEncoder[ProductConfiguration]
  implicit lazy val jsonProdConfDecoder: Decoder[ProductConfiguration] = deriveDecoder[ProductConfiguration]
}
