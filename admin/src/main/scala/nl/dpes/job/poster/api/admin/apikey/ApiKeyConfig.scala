package nl.dpes.job.poster.api.admin.apikey

import nl.dpes.job.poster.api.service.apikey.Salt
import pureconfig.ConfigReader
import pureconfig.generic.semiauto.deriveReader

case class ApiKeyConfig(salt: Salt)

object ApiKeyConfig {
  implicit val SaltConfigReader: ConfigReader[Salt] = ConfigReader[String].map(Salt.apply)

  implicit val appConfigReader: ConfigReader[ApiKeyConfig] = deriveReader[ApiKeyConfig]
}
