package nl.dpes.job.poster.api.admin.defaults

import cats.effect._
import cats.implicits._
import nl.dpes.job.poster.api.admin.auth.{CommonProfileOps, SecureRoute}
import nl.dpes.job.poster.api.admin.layout.FlashMessage.{DefaultsError, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d, MessageReader, MessageWriter}
import nl.dpes.job.poster.api.admin.layout.MainPage
import nl.dpes.job.poster.api.admin.service.creditservice.ProductConfiguration.{JobPostingConfiguration, JobUpgradeConfiguration}
import nl.dpes.job.poster.api.admin.service.creditservice._
import nl.dpes.job.poster.api.service.defaults.Defaults.ApplicationMethod.EasyApply
import nl.dpes.job.poster.api.service.defaults.Defaults.{ApplicationMethod, Configuration, JobPosting}
import nl.dpes.job.poster.api.service.defaults.{Defaults, DefaultsService}
import org.http4s.{AuthedRoutes, Response, Status, Uri}
import org.http4s.FormDataDecoder.formEntityDecoder
import org.http4s.dsl.Http4sDsl
import org.http4s.headers.{`Content-Type`, Location}
import org.pac4j.core.profile.CommonProfile
import org.typelevel.log4cats.slf4j.Slf4jFactory

class DefaultsController[F[_]: Async](creditService: CreditService[F], defaultsService: DefaultsService[F], loggerFactory: Slf4jFactory[F])
    extends Http4sDsl[F] {

  private val defaultsPage: AuthedRoutes[List[CommonProfile], F] = AuthedRoutes.of {
    case req @ GET -> Root / "recruiter" / recruiterId / "defaults" as user =>
      (for {
        message            <- req.req.flashMessage
        entitlements       <- creditService.getEntitlements(RecruiterId(recruiterId))
        jobPostingProducts <- collectJobPostingProducts(entitlements).pure[F]
        defaults           <- defaultsService.getDefaults(Defaults.RecruiterId(recruiterId))
        defaultProduct     <- getDefaultWebshopProduct(defaults.configuration, jobPostingProducts).pure[F]
        isEasyApply        <- isEasyApplySet(defaults).pure[F]
        response <- Ok(
          MainPage(
            pageContent = DefaultsPage.render(recruiterId, jobPostingProducts.map(_.name), isEasyApply, defaultProduct.map(_.name)),
            flashMessage = message,
            userName = user.displayName
          ).render
        )
      } yield response
        .withContentType(`Content-Type`(org.http4s.MediaType.text.html))
        .clearFlashMessage())
        .onError { case thr => loggerFactory.getLogger.error(s"Error occurred when trying to load the defaults: ${thr.getMessage}") }
  }

  private val saveDefaults: AuthedRoutes[List[CommonProfile], F] = AuthedRoutes.of {
    case req @ POST -> Root / "recruiter" / recruiterId / "defaults" as _ =>
      (for {
        form              <- req.req.as[DefaultsForm]
        entitlements      <- creditService.getEntitlements(RecruiterId(recruiterId))
        oldDefaults       <- defaultsService.getDefaults(Defaults.RecruiterId(recruiterId))
        webshopProduct    <- getWebshopProduct(form, entitlements)
        jobConfiguration  <- mapJobConfiguration(webshopProduct)
        applicationMethod <- setApplicationMethod(form, oldDefaults)
        defaults          <- updateDefaults(jobConfiguration, applicationMethod, oldDefaults)
        response <- defaultsService.saveDefaults(Defaults.RecruiterId(recruiterId), defaults).attempt.map {
          case Right(_) => redirectTo(s"/recruiter/$recruiterId/defaults", SeeOther).withFlashMessage(DefaultsSaved(recruiterId))
          case Left(_)  => redirectTo(s"/recruiter/$recruiterId/defaults", InternalServerError).withFlashMessage(DefaultsError(recruiterId))
        }
      } yield response)
        .onError { case thr => loggerFactory.getLogger.error(s"Error occurred when trying to save the defaults: ${thr.getMessage}") }
  }

  private def getWebshopProduct(defaultsForm: DefaultsForm, entitlements: List[Entitlement]): F[Option[WebshopProduct]] =
    entitlements
      .collectFirst {
        case Entitlement.Subscription(_, product) if product.name == defaultsForm.productName => product
        case Entitlement.Credit(_, product) if product.name == defaultsForm.productName       => product
      }
      .pure[F]

  private def mapJobConfiguration(webshopProduct: Option[WebshopProduct]): F[Option[JobPosting]] =
    (webshopProduct match {
      case Some(WebshopProduct(_, JobPostingConfiguration(publishOn, daysAvailable, features, _))) =>
        val sites       = publishOn.map(site => Defaults.Site(site.alternativeName))
        val duration    = daysAvailable.value
        val jobFeatures = features.map(feature => Defaults.Feature(feature.name))
        Some(Defaults.JobPosting(sites, duration, jobFeatures))
      case _ => None
    }).pure[F]

  private def updateDefaults(
    jobConfiguration: Option[JobPosting],
    applicationMethod: Option[ApplicationMethod],
    oldDefaults: Defaults
  ): F[Defaults] = {
    val defaultsWithJobConfiguration = jobConfiguration match {
      case Some(config) => oldDefaults.copy(configuration = Some(config))
      case None         => oldDefaults
    }
    applicationMethod match {
      case Some(easyApply) => defaultsWithJobConfiguration.copy(applicationMethod = Some(easyApply)).pure[F]
      case None            => defaultsWithJobConfiguration.pure[F]
    }
  }

  private def isEasyApplySet(defaults: Defaults): Boolean =
    defaults.applicationMethod.exists {
      case ApplicationMethod.ApplyViaJobBoard(_, _, _)  => false
      case ApplicationMethod.ApplyViaExternalWebsite(_) => false
      case ApplicationMethod.EasyApply                  => true
    }

  private def collectJobPostingProducts(entitlements: List[Entitlement]): List[WebshopProduct] =
    entitlements.collect {
      case Entitlement.Subscription(_, product @ WebshopProduct(productName, _: JobPostingConfiguration))
          if productName.isNotStage && productName.isNotBijbaan =>
        product
      case Entitlement.Credit(_, product @ WebshopProduct(productName, _: JobPostingConfiguration))
          if productName.isNotStage && productName.isNotBijbaan =>
        product
    }

  private def getDefaultWebshopProduct(
    defaultJobProductConfiguration: Option[Configuration],
    webshopProducts: List[WebshopProduct]
  ): Option[WebshopProduct] =
    webshopProducts
      .mapFilter(jobPostingConfiguration => getJobPostingConfiguration(defaultJobProductConfiguration, jobPostingConfiguration))
      .headOption

  private def getJobPostingConfiguration(
    defaultJobProductConfiguration: Option[Configuration],
    jobPostingConfiguration: WebshopProduct
  ): Option[WebshopProduct] =
    jobPostingConfiguration.configuration match {
      case JobPostingConfiguration(publishOn, daysAvailable, features, _) =>
        defaultJobProductConfiguration.collect {
          case config: JobPosting
              if publishOn.map(_.name) == config.publishOn.map(_.alternativeName) &&
                daysAvailable.value == config.publicationDuration &&
                features.map(_.name) == config.features.map(_.name) =>
            jobPostingConfiguration
        }
      case _: JobUpgradeConfiguration                    => None
      case ProductConfiguration.ProfileViewConfiguration => None
      case ProductConfiguration.PbpConfiguration         => None
    }

  private def setApplicationMethod(form: DefaultsForm, defaults: Defaults): F[Option[ApplicationMethod]] =
    (if (form.withEasyApply) Some(EasyApply)
     else defaults.applicationMethod).pure[F]

  def redirectTo(path: String, status: Status): Response[F] =
    Response[F]()
      .withStatus(status)
      .withHeaders(Location(Uri.unsafeFromString(path)))

  def routes: SecureRoute[F] = defaultsPage <+> saveDefaults
}

object DefaultsController {

  def resource[F[_]: Async](
    creditService: CreditService[F],
    defaultsService: DefaultsService[F],
    loggerFactory: Slf4jFactory[F]
  ): Resource[F, DefaultsController[F]] =
    Resource.pure(new DefaultsController[F](creditService, defaultsService, loggerFactory))
}
