package nl.dpes.job.poster.api.admin.service.creditservice

trait CreditService[F[_]] {

  def getEntitlements(recruiterId: RecruiterId): F[List[Entitlement]]
}

object CreditService {

  def apply[F[_]](creditServiceClient: CreditServiceClient[F]): CreditService[F] =
    new CreditService[F] {

      override def getEntitlements(recruiterId: RecruiterId): F[List[Entitlement]] =
        creditServiceClient.getEntitlements(recruiterId)
    }
}
