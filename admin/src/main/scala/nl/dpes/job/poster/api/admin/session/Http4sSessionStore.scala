package nl.dpes.job.poster.api.admin.session

import cats._
import cats.implicits._
import org.pac4j.core.context.Cookie
import org.pac4j.core.util.Pac4jConstants
import org.pac4j.http4s.Http4sWebContext

import java.util.{Optional, UUID}
import scala.jdk.CollectionConverters._
import scala.language.implicitConversions

case class Http4sSessionStore[F[_]: MonadThrow](storage: SessionDb[F], createCookie: String => Cookie) {
  import Http4sSessionStore._

  private def getSessionIdFromContext(context: Http4sWebContext[F]): F[SessionId] =
    for {
      id <- context.getSessionIdFromRequestAttributes orElse context.getSessionIdFromCookie
      _  <- storage.getSession(id).void orElse storage.storeSession(id, Session()) // make sure the session is initialized in the storage too
    } yield id

  def getSessionId(context: Http4sWebContext[F]): F[SessionId] =
    getSessionIdFromContext(context) orElse createSession(context)

  private def createSession(context: Http4sWebContext[F]): F[SessionId] =
    for {
      id <- SessionId(UUID.randomUUID().toString).pure[F]
      _  <- storage.storeSession(id, Session())
      _  <- context.storeSessionId(id, createCookie)
    } yield id

  def getSessionValue(key: String)(context: Http4sWebContext[F]): F[Option[Any]] = for {
    id      <- getSessionId(context)
    session <- storage.getSession(id) orElse Session().pure[F]
  } yield session.get(key)

  def setSessionValue(key: String, value: Any)(implicit context: Http4sWebContext[F]): F[Unit] = for {
    id      <- getSessionId(context)
    session <- storage.getSession(id) orElse Session().pure[F]
    _       <- storage.storeSession(id, session.set(key, value))
  } yield ()

  def destroySession(implicit context: Http4sWebContext[F]): F[Boolean] = (for {
    id <- getSessionId(context)
    _  <- context.clearSessionId
    _  <- storage.removeSession(id)
  } yield true)
    .recoverWith { case _ => false.pure[F] }

  def getTrackableSession(implicit context: Http4sWebContext[F]): F[Option[Session]] = for {
    id      <- getSessionId(context).attempt
    session <- id.traverse(storage.getSession)
  } yield session.toOption

  def buildFromTrackableSession(session: Session)(implicit context: Http4sWebContext[F]): F[Http4sSessionStore[F]] = for {
    _  <- destroySession
    id <- getSessionId(context) orElse createSession(context)
    _  <- storage.storeSession(id, session)
  } yield this

  def renewSession(implicit context: Http4sWebContext[F]): F[Boolean] = for {
    oldId        <- getSessionId(context)
    oldSession   <- storage.getSession(oldId)
    destroyed    <- destroySession
    newSessionId <- createSession(context)
    _            <- storage.storeSession(newSessionId, oldSession)
  } yield destroyed
}

object Http4sSessionStore {

  sealed abstract class SessionError(message: String) extends Throwable(message)
  case object NoSessionId                             extends SessionError("Session id not available")
  case class NoSession(sessionId: SessionId)          extends SessionError(s"No session found for session id '${sessionId.value}'")

  implicit class OptionOps[A](optional: Optional[A]) {
    def toOption: Option[A] = if (optional.isPresent) Some(optional.get) else None
  }

  implicit class SessionOps[F[_]: MonadThrow](context: Http4sWebContext[F]) {

    def storeSessionId(sessionId: SessionId, createCookie: String => Cookie): F[Unit] = {
      context.setRequestAttribute(Pac4jConstants.SESSION_ID, sessionId.value)
      context.addResponseCookie(createCookie(sessionId.value))
      MonadThrow[F].unit
    }

    def getSessionIdFromRequestAttributes: F[SessionId] =
      ApplicativeThrow[F]
        .fromOption(
          context
            .getRequestAttribute(Pac4jConstants.SESSION_ID)
            .toOption,
          NoSessionId
        )
        .map(id => SessionId(id.toString))

    def getSessionIdFromCookie: F[SessionId] =
      ApplicativeThrow[F]
        .fromOption(
          context.getRequestCookies.asScala
            .find(_.getName == Pac4jConstants.SESSION_ID),
          NoSessionId
        )
        .map(cookie => SessionId(cookie.getValue))

    def clearSessionId: F[Unit] = for {
      _ <- context.setRequestAttribute(Pac4jConstants.SESSION_ID, null).pure[F]
      _ <- context.removeResponseCookie(Pac4jConstants.SESSION_ID).pure[F]
    } yield ()
  }

}
