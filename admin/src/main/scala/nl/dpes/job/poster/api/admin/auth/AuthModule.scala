package nl.dpes.job.poster.api.admin.auth

import cats.effect._
import cats.effect.std.Dispatcher
import doobie.util.transactor.Transactor
import nl.dpes.job.poster.api.admin.config._
import nl.dpes.job.poster.api.admin.session._
import org.http4s.HttpRoutes
import org.typelevel.log4cats.Logger

case class AuthModule[F[_]](routes: HttpRoutes[F], secure: SecureRoute[F] => HttpRoutes[F])

object AuthModule {

  def apply[F[_] <: AnyRef: Async](
    config: OneLoginConfig
  )(implicit xa: Transactor[F], dispatcher: Dispatcher[F], logger: Logger[F]): Resource[F, AuthModule[F]] =
    for {
      sessionStore <- SessionDb
        .mySqlResource(xa, config.encryptionSecret)
        .map(Http4sSessionStore[F](_, SessionCookie().builder))
        .map(Pac4jWrapper[F](_, dispatcher))
      auth <- AuthManagement[F](config, dispatcher, sessionStore)
    } yield AuthModule(auth.routes, auth.apply)
}
