package nl.dpes.job.poster.api.admin.status

import cats.effect.{Async, Resource}
import cats.implicits._
import nl.dpes.job.poster.api.admin.layout.MainPage
import org.http4s.dsl.Http4sDsl
import org.http4s.headers.`Content-Type`
import org.http4s.{HttpRoutes, StaticFile}

class StatusController[F[_]: Async]() extends Http4sDsl[F] {

  private val statusPage: HttpRoutes[F] = HttpRoutes.of[F] { case req @ GET -> Root / "status" =>
    for {
      resp <- Ok(MainPage(StatusPage.render).render)
    } yield resp
      .withContentType(`Content-Type`(org.http4s.MediaType.text.html))
  }

  def routes: HttpRoutes[F] = statusPage
}

object StatusController {
  def resource[F[_]: Async](): Resource[F, StatusController[F]] = Resource.pure(new StatusController[F])
}
