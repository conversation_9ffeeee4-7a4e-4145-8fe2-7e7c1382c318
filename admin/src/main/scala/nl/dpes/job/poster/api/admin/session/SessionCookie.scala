package nl.dpes.job.poster.api.admin.session

import org.pac4j.core.context.Cookie
import org.pac4j.core.util.Pac4jConstants

case class SessionCookie(
  maxAge: Option[Int] = None,
  domain: Option[String] = None,
  path: Option[String] = Some("/"),
  secure: Boolean = false,
  httpOnly: Boolean = false
) {

  def builder: String => Cookie = value => {
    val cookie = new Cookie(Pac4jConstants.SESSION_ID, value)
    maxAge.foreach(cookie.setMaxAge)
    domain.foreach(cookie.setDomain)
    path.foreach(cookie.setPath)
    cookie.setSecure(secure)
    cookie.setHttpOnly(httpOnly)
    cookie
  }
}
