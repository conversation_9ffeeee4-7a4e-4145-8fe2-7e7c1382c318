package nl.dpes.job.poster.api.admin.service.creditservice

import cats.ApplicativeThrow
import cats.syntax.applicative._
import cats.syntax.applicativeError._
import cats.syntax.functor._
import io.circe.{Decoder, Encoder}

import scala.util.matching.Regex
import scala.util.Try

sealed trait Feature {
  def name: String
}

object Feature {
  case class DeserializationError(feature: String) extends Throwable(s"Could not deserialize '$feature' to a Feature'")

  sealed trait Booster extends Feature

  object Booster {

    case object Booster extends Booster {
      override def name: String = "booster"
    }

    case class ValuedBooster(value: Int) extends Booster {
      override def name: String = s"booster_$value"
    }

    private val valuedBoosterPattern: Regex = """booster_(\d+)""".r

    case class DeserializationError(label: String) extends Throwable(s"Could not deserialize '$label' to a Booster'")

    def apply[F[_]: ApplicativeThrow](feature: String): F[Booster] = feature match {
      case "booster"                   => Booster.pure.widen
      case valuedBoosterPattern(value) => ValuedBooster(value.toInt).pure.widen
      case _                           => DeserializationError(feature).raiseError
    }
  }

  case object Logo extends Feature {
    case class DeserializationError(feature: String) extends Throwable(s"Could not deserialize '$feature' to a Logo'")

    def apply[F[_]: ApplicativeThrow](feature: String): F[Logo.type] =
      if (feature == Logo.name) Logo.pure
      else DeserializationError(feature).raiseError

    override def name: String = "logo"
  }

  case object Highlight extends Feature {
    case class DeserializationError(feature: String) extends Throwable(s"Could not deserialize '$feature' to a Highlight'")

    def apply[F[_]: ApplicativeThrow](feature: String): F[Highlight.type] =
      if (feature == Highlight.name) Highlight.pure
      else DeserializationError(feature).raiseError

    override def name: String = "highlight"
  }

  case object Spotlight extends Feature {
    case class DeserializationError(feature: String) extends Throwable(s"Could not deserialize '$feature' to a Spotlight'")

    def apply[F[_]: ApplicativeThrow](feature: String): F[Spotlight.type] =
      if (feature == Spotlight.name) Spotlight.pure
      else DeserializationError(feature).raiseError

    override def name: String = "spotlight"
  }

  case object Scraped extends Feature {
    case class DeserializationError(feature: String) extends Throwable(s"Could not deserialize '$feature' to a Scraped'")

    def apply[F[_]: ApplicativeThrow](feature: String): F[Scraped.type] =
      if (feature == Scraped.name) Scraped.pure
      else DeserializationError(feature).raiseError

    override def name: String = "scraped"
  }

  def apply[F[_]: ApplicativeThrow](feature: String): F[Feature] =
    Booster(feature).widen[Feature] orElse
    Logo(feature).widen orElse
    Highlight(feature).widen orElse
    Spotlight(feature).widen orElse
    Scraped(feature).widen orElse
    DeserializationError(feature).raiseError

  implicit lazy val jsonEncoder: Encoder[Feature] = Encoder.encodeString.contramap(_.name)
  implicit lazy val jsonDecoder: Decoder[Feature] = Decoder.decodeString.emapTry(Feature[Try])
}
