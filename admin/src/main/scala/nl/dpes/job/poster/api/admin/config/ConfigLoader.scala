package nl.dpes.job.poster.api.admin.config

import pureconfig.{ConfigReader, ConfigSource}
import pureconfig.error.ConfigReaderException
import cats.MonadThrow
import cats.implicits._

object ConfigLoader {

  implicit class ConfigSourceOps(source: ConfigSource) {

    def loadConfig[F[_], A](implicit reader: ConfigReader[A], F: MonadThrow[F]): F[A] =
      F.pure(source.load[A])
        .flatMap {
          case Right(value) => F.pure(value)
          case Left(errors) => F.raiseError[A](ConfigReaderException(errors))
        }
  }
}
