package nl.dpes.job.poster.api.admin.service.creditservice

import cats.implicits.catsSyntaxValidatedId
import io.circe.{Decoder, Encoder}
import org.http4s.{QueryParamDecoder, QueryParameterValue}

case class ProductName(value: String) extends AnyVal

object ProductName {
  implicit lazy val jsonDecoder: Decoder[ProductName] = Decoder.decodeString.map(ProductName.apply)
  implicit lazy val jsonEncoder: Encoder[ProductName] = Encoder.encodeString.contramap(_.value)

  implicit val productNameQueryParamDecoder: QueryParamDecoder[ProductName] = (value: QueryParameterValue) =>
    ProductName(value.value).validNel

  implicit class ProductNameOps(productName: ProductName) {

    def isNotStage: Boolean   = !productName.value.toLowerCase.contains("stage")
    def isNotBijbaan: Boolean = !productName.value.toLowerCase.contains("bijbaan")
  }
}
