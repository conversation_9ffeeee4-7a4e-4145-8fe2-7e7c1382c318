package nl.dpes.job.poster.api.admin.layout

import scalatags.Text.all._

case class MainPage(pageContent: Modifier, userName: Option[String] = None, flashMessage: Option[FlashMessage] = None) {

  def render: String =
    "<!doctype html>" +
    html(
      head(
        link(rel := "stylesheet", href := "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"),
        meta(charset := "UTF-8"),
        meta(name := "viewport", content := "width=device-width, initial-scale=1"),
        link(rel := "shortcut icon", href := "data:image/x-icon;,", `type` := "image/x-icon")
      ),
      body(
        userName.map(name => div(`class` := "header")(span(s"You are now logged in as $name"), " ", a(href := "/logout")("log out"))),
        flashMessage.map(_.render),
        pageContent
      )
    ).toString()
}
