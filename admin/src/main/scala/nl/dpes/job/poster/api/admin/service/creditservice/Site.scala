package nl.dpes.job.poster.api.admin.service.creditservice

import cats._
import cats.implicits._
import io.circe.{Decoder, Encoder}

import scala.util.Try

sealed abstract class Site(val name: String)

object Site {
  case object NVB extends Site("nationalevacaturebank")
  case object IOL extends Site("intermediair")
  case object ITB extends Site("itbanen")

  case class UnknownSite(siteName: String) extends Throwable(s"Cannot decode '$siteName' to a Site")

  def valueOf[F[_]: ApplicativeThrow](value: String): F[Site] = value.toLowerCase match {
    case "nationalevacaturebank" => ApplicativeThrow[F].pure(NVB)
    case "intermediair"          => ApplicativeThrow[F].pure(IOL)
    case "itbanen"               => ApplicativeThrow[F].pure(ITB)
    case unknown                 => ApplicativeThrow[F].raiseError(UnknownSite(unknown))
  }

  implicit class SiteOps(site: Site) {

    def alternativeName: String = site match {
      case NVB => "Nationale Vacaturebank"
      case IOL => "Intermediair"
      case ITB => "Tweakers Carrière"
    }
  }

  implicit lazy val jsonEncoder: Encoder[Site] = Encoder.encodeString.contramap(_.name)
  implicit lazy val jsonDecoder: Decoder[Site] = Decoder.decodeString.emapTry(valueOf[Try])
}
