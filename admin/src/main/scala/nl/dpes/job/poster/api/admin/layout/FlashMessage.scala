package nl.dpes.job.poster.api.admin.layout

import cats.MonadThrow
import cats.implicits._
import io.circe._
import io.circe.generic.semiauto._
import io.circe.syntax._
import nl.dpes.job.poster.api.service.apikey.{<PERSON>pi<PERSON><PERSON>, Integration}
import org.http4s.{Request, Response, ResponseCookie}
import scalatags.Text
import scalatags.Text.all._

import java.nio.charset.StandardCharsets
import java.util.Base64

sealed trait FlashMessage {
  def render: Modifier
}

object FlashMessage {

  val cookieName: String = "flashMessage"

  case object Empty extends FlashMessage {
    override def render: Text.all.Modifier = ""
  }

  case class ApiKeyCreated(key: <PERSON><PERSON><PERSON><PERSON>, integration: Integration) extends FlashMessage {
    def render: Modifier = div(`class` := "flash-message success")("API key for ", strong(integration.value), " created: ", strong(key.key))
  }

  case class ApiKeyRevoked(integration: Integration) extends FlashMessage {
    override def render: Modifier = div(`class` := "flash-message success")("API key ‘", strong(integration.value), "’ revoked")
  }

  case class DefaultsSaved(recruiterId: String) extends FlashMessage {
    override def render: Modifier = div(`class` := "flash-message success")("Defaults saved for recruiter ", strong(recruiterId))
  }

  case class DefaultsError(recruiterId: String) extends FlashMessage {

    override def render: Modifier =
      div(`class` := "flash-message error")("Error has occurred when trying to save a default for recruiter ", strong(recruiterId))
  }

  private implicit lazy val apiKeyCodec: Codec[ApiKey]             = deriveCodec
  private implicit lazy val integrationodec: Codec[Integration]    = deriveCodec
  private implicit lazy val flashMessageCodec: Codec[FlashMessage] = deriveCodec

  def encode(message: FlashMessage): String =
    Base64.getEncoder.encodeToString(message.asJson.noSpaces.getBytes(StandardCharsets.UTF_8))

  def decode(value: String): Either[Error, FlashMessage] =
    parser.decode[FlashMessage](new String(Base64.getDecoder.decode(value), StandardCharsets.UTF_8))

  implicit class MessageReader[F[_]: MonadThrow](req: Request[F]) {

    def flashMessage: F[Option[FlashMessage]] = for {
      cookie  <- req.cookies.find(_.name == cookieName).pure[F]
      message <- MonadThrow[F].fromEither(cookie.traverse(c => decode(c.content)))
    } yield message.flatMap {
      case Empty   => None
      case message => Some(message)
    }
  }

  implicit class MessageWriter[F[_]: MonadThrow](resp: Response[F]) {

    def withFlashMessage(message: FlashMessage): Response[F] =
      resp.addCookie(ResponseCookie(name = cookieName, content = encode(message), path = "/".some))

    def clearFlashMessage(): Response[F] = withFlashMessage(Empty)
  }
}
