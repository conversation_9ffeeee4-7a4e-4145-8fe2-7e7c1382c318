package nl.dpes.job.poster.api.admin.config

import pureconfig._
import nl.dpes.job.poster.api.admin.config.OneLoginConfig._
import pureconfig.generic.semiauto.deriveReader

case class OneLoginConfig(
  clientId: ClientId,
  secret: Secret,
  discoveryUri: <PERSON><PERSON>ri,
  origin: Origin,
  encryptionSecret: EncryptionSecret
)

object OneLoginConfig {
  case class ClientId(value: String) extends AnyVal
  implicit val clientIdConfigReader: ConfigReader[ClientId] = ConfigReader[String].map(ClientId.apply)

  case class Secret(value: String) extends AnyVal
  implicit val secretConfigReader: ConfigReader[Secret] = ConfigReader[String].map(Secret.apply)

  case class DiscoveryUri(value: String) extends AnyVal
  implicit val discoveryUriConfigReader: ConfigReader[DiscoveryUri] = ConfigReader[String].map(DiscoveryUri.apply)

  case class Origin(value: String) extends AnyVal
  implicit val originConfigReader: ConfigReader[Origin] = ConfigReader[String].map(Origin.apply)

  final case class EncryptionSecret(value: String) extends AnyVal
  implicit val encryptionSecretConfigReader: ConfigReader[EncryptionSecret] = ConfigReader[String].map(EncryptionSecret.apply)

  implicit val oneloginConfigReader: ConfigReader[OneLoginConfig] = deriveReader[OneLoginConfig]
}
