package nl.dpes.job.poster.api.admin.apikey

import cats.implicits._
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.admin.apikey.CreateApiKeyPage.CreateApiKeyForm
import nl.dpes.job.poster.api.service.apikey.Integration
import org.http4s.{FormDataDecoder, QueryParamDecoder, QueryParameterValue}
import org.http4s.FormDataDecoder._
import scalatags.Text.all._

case class CreateApiKeyPage(recruiterId: SalesForceId, formData: Option[CreateApiKeyForm]) {

  def render: Modifier = Seq(
    h2(s"Create a new API key for recruiter ${recruiterId.idWithChecksum}"),
    form(method := "post")(
      label(`for` := "integrationName")("API key name"),
      " ",
      input(
        `type` := "text",
        name := "integrationName",
        id := "integrationName",
        value := formData.map(_.integrationName.value).getOrElse("")
      ),
      p(a(href := s"/recruiter/${recruiterId.idWithChecksum}/apiKeys")("Cancel"), " ", input(`type` := "submit", value := "Create key"))
    )
  )
}

object CreateApiKeyPage {
  case class CreateApiKeyForm(integrationName: Integration)

  object CreateApiKeyForm {
    implicit lazy val integrationDecoder: QueryParamDecoder[Integration] = (value: QueryParameterValue) => Integration(value.value).validNel

    implicit lazy val formDecoder: FormDataDecoder[CreateApiKeyForm] =
      field[Integration]("integrationName").map(CreateApiKeyForm.apply)
  }
}
