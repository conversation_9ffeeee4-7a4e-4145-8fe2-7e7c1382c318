package nl.dpes.job.poster.api.admin.auth

import cats.effect._
import cats.effect.std.Dispatcher
import nl.dpes.job.poster.api.admin.config.OneLoginConfig.Origin
import nl.dpes.job.poster.api.admin.config._
import org.http4s._
import org.http4s.dsl.Http4sDsl
import org.http4s.server.HttpMiddleware
import org.pac4j.core.config.Config
import org.pac4j.core.context.session.SessionStore
import org.pac4j.http4s._
import org.pac4j.http4s.CallbackService
import org.pac4j.http4s.LogoutService

import scala.concurrent.duration._

class AuthManagement[F[_] <: AnyRef: Sync](config: Config, origin: Origin)(contextBuilder: Request[F] => Http4sWebContext[F])
    extends Http4sDsl[F] {

  private val sessionConfig = SessionConfig(
    cookieName = "session",
    mkCookie = ResponseCookie(_, _, path = Some("/")),
    secret = List.fill(16)(0xff.toByte),
    maxAge = 5.minutes
  )

  private val sessionManager: HttpMiddleware[F] =
    Session.sessionManagement[F](sessionConfig)

  private val callbackService = new CallbackService[F](
    config,
    contextBuilder,
    defaultUrl = Some(s"${origin.value}/callback"),
    renewSession = true,
    defaultClient = None
  )

  private val centralLogoutService = new LogoutService[F](
    config,
    contextBuilder,
    defaultUrl = Some(s"${origin.value}/?defaulturlafterlogoutafteridp"),
    logoutUrlPattern = Some(s"${origin.value}/.*"),
    localLogout = true,
    destroySession = true,
    centralLogout = true
  )

  val routes: HttpRoutes[F] = sessionManager(HttpRoutes.of[F] {
    case req @ GET -> Root / "callback" =>
      callbackService.callback(req)
    case req @ POST -> Root / "callback" =>
      callbackService.callback(req)
    case req @ GET -> Root / "logout" =>
      centralLogoutService.logout(req)
  })

  def apply(pages: SecureRoute[F]): HttpRoutes[F] =
    sessionManager.compose(SecurityFilterMiddleware.securityFilter[F](config, contextBuilder))(pages)
}

object AuthManagement {

  def apply[F[_] <: AnyRef: Async](
    config: OneLoginConfig,
    dispatcher: Dispatcher[F],
    sessionStore: SessionStore
  ): Resource[F, AuthManagement[F]] =
    Resource.eval {
      Sync[F].delay {
        val builtConfig = new OneLoginConfigFactory[F](config, sessionStore).build()
        new AuthManagement[F](builtConfig, config.origin)(
          Http4sWebContext.withDispatcherInstance[F](dispatcher)
        )
      }
    }
}
