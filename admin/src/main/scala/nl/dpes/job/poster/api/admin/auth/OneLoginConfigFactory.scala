package nl.dpes.job.poster.api.admin.auth

import cats.effect.Async
import com.nimbusds.jose.JWSAlgorithm
import nl.dpes.job.poster.api.admin.auth.OneLoginConfigFactory.{DisablePac4jCsrf, SimpleSessionStoreFactory}
import nl.dpes.job.poster.api.admin.config.OneLoginConfig
import org.pac4j.core.authorization.authorizer.{<PERSON>srf<PERSON><PERSON><PERSON><PERSON>, DefaultAuthorizers}
import org.pac4j.core.authorization.generator.AuthorizationGenerator
import org.pac4j.core.client.Clients
import org.pac4j.core.config.{Config, ConfigFactory}
import org.pac4j.core.context.WebContext
import org.pac4j.core.context.session.{SessionStore, SessionStoreFactory}
import org.pac4j.core.profile.UserProfile
import org.pac4j.http4s.DefaultHttpActionAdapter
import org.pac4j.oidc.client.OidcClient
import org.pac4j.oidc.config.OidcConfiguration
import org.typelevel.log4cats.Logger
import org.typelevel.log4cats.slf4j.Slf4jFactory

import java.util
import java.util.Optional

class OneLoginConfigFactory[F[_] <: AnyRef: Async](oneLoginConfig: OneLoginConfig, sessionStore: SessionStore) extends ConfigFactory {

  implicit val loggerF: Logger[F] = Slf4jFactory.create[F].getLogger

  override def build(parameters: AnyRef*): Config = {
    val clients = new Clients(s"${oneLoginConfig.origin.value}/callback", oidcClient())

    val config = new Config(clients)
    config.setHttpActionAdapter(new DefaultHttpActionAdapter[F]) // <-- Render a nicer page
    config.setSessionStoreFactory(new SimpleSessionStoreFactory(sessionStore))
    config.addAuthorizer(DefaultAuthorizers.CSRF_CHECK, DisablePac4jCsrf)
    config
  }

  private def oidcClient(): OidcClient = {
    val oidcConfiguration = new OidcConfiguration()
    oidcConfiguration.setClientId(oneLoginConfig.clientId.value)
    oidcConfiguration.setSecret(oneLoginConfig.secret.value)
    oidcConfiguration.setDiscoveryURI(oneLoginConfig.discoveryUri.value)
    oidcConfiguration.setPreferredJwsAlgorithm(JWSAlgorithm.RS256)
    oidcConfiguration.setMaxClockSkew(60)
    oidcConfiguration.setUseNonce(true)
    val oidcClient = new OidcClient(oidcConfiguration)

    val authorizationGenerator = new AuthorizationGenerator {
      def generate(context: org.pac4j.core.context.CallContext, profile: UserProfile): Optional[UserProfile] =
        Optional.of(profile)
    }
    oidcClient.setAuthorizationGenerator(authorizationGenerator)
    oidcClient
  }
}

object OneLoginConfigFactory {

  case object DisablePac4jCsrf extends CsrfAuthorizer("pac4jCsrfToken", "") {
    override def isAuthorized(context: WebContext, sessionStore: SessionStore, profiles: util.List[UserProfile]): Boolean = true
  }

  class SimpleSessionStoreFactory(sessionStore: SessionStore) extends SessionStoreFactory {
    def newSessionStore(parameters: org.pac4j.core.context.FrameworkParameters): SessionStore = sessionStore
  }
}
