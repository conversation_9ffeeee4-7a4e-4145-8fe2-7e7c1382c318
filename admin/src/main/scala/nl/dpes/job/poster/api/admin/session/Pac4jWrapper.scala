package nl.dpes.job.poster.api.admin.session

import cats._
import cats.effect.std.Dispatcher
import cats.implicits._
import org.pac4j.core.context.WebContext
import org.pac4j.core.context.session.SessionStore
import org.pac4j.http4s.Http4sWebContext
import org.typelevel.log4cats.Logger
import java.util.Optional
import scala.language.implicitConversions

/** This class is created to handle all java like conversions, tha actual work is done in the Http4sSessionStore
  */
case class Pac4jWrapper[F[_]: MonadThrow: Logger](sessionStore: Http4sSessionStore[F], dispatcher: Dispatcher[F]) extends SessionStore {

  import Pac4jWrapper._

  /** The create session flag is not used as not always creating the session will cause issues. (The optional part is also misleading for
    * that matter)
    */
  override def getSessionId(context: WebContext, createSession: Boolean): Optional[String] =
    dispatcher
      .unsafeRunSync((for {
        _  <- Logger[F].debug(s"#### Get sessionId")
        id <- sessionStore.getSessionId(context.asHttp4sWebContext)
      } yield id.value).attempt)
      .toOption
      .toOptional

  override def get(context: WebContext, key: String): Optional[Any] =
    dispatcher
      .unsafeRunSync(for {
        _     <- Logger[F].debug(s"#### Getting session value for '$key'")
        value <- sessionStore.getSessionValue(key)(context.asHttp4sWebContext)
        _     <- Logger[F].debug(s"#### Got session value '$value' for '$key'")
      } yield value)
      .toOptional

  override def set(context: WebContext, key: String, value: Any): Unit = {
    implicit val http4sWebContext: Http4sWebContext[F] = context.asHttp4sWebContext

    dispatcher.unsafeRunSync(for {
      _ <- Logger[F].debug(s"#### Setting session value '$value' for '$key'")
      _ <- sessionStore.setSessionValue(key, value)
    } yield ())
  }

  override def destroySession(context: WebContext): Boolean = {
    implicit val http4sWebContext: Http4sWebContext[F] = context.asHttp4sWebContext

    dispatcher.unsafeRunSync(for {
      _         <- Logger[F].debug(s"#### Destroying session")
      destroyed <- sessionStore.destroySession
    } yield destroyed)
  }

  override def getTrackableSession(context: WebContext): Optional[Any] = {
    implicit val http4sWebContext: Http4sWebContext[F] = context.asHttp4sWebContext

    dispatcher
      .unsafeRunSync(for {
        _       <- Logger[F].debug(s"#### Get trackable session")
        session <- sessionStore.getTrackableSession
      } yield session)
      .toOptional
      .map(_.asInstanceOf[Any])
  }

  override def buildFromTrackableSession(context: WebContext, trackableSession: Any): Optional[SessionStore] = {
    implicit val http4sWebContext: Http4sWebContext[F] = context.asHttp4sWebContext

    dispatcher
      .unsafeRunSync(for {
        _ <- Logger[F].debug(s"#### Build from trackable session")
        _ <- sessionStore.buildFromTrackableSession(trackableSession.asInstanceOf[Session])
      } yield this.some)
      .toOptional
      .map(_.asInstanceOf[SessionStore])
  }

  override def renewSession(context: WebContext): Boolean = {
    implicit val http4sWebContext: Http4sWebContext[F] = context.asHttp4sWebContext

    dispatcher
      .unsafeRunSync(for {
        _       <- Logger[F].debug(s"#### Renewing session")
        renewed <- sessionStore.renewSession
      } yield renewed)
  }
}

object Pac4jWrapper {

  implicit class OptionOps[A](option: Option[A]) {

    def toOptional: Optional[A] = option match {
      case Some(value) => Optional.of(value)
      case None        => Optional.empty()
    }
  }

  implicit class WebContextWrapper[F[_]](context: WebContext) {

    def asHttp4sWebContext: Http4sWebContext[F] = context.asInstanceOf[Http4sWebContext[F]]
  }
}
