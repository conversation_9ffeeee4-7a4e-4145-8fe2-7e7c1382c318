package nl.dpes.job.poster.api.admin

import cats.data.OptionT
import cats.effect.std.Dispatcher
import cats.effect.{IO, IOApp, Resource}
import cats.implicits._
import doobie.util.transactor.Transactor
import nl.dpes.job.poster.api.admin.auth.AuthModule
import io.getquill.{SnakeCase, SqliteJdbcContext}
import nl.dpes.job.poster.api.admin.apikey.ApiKeyController
import nl.dpes.job.poster.api.admin.config.ConfigLoader.ConfigSourceOps
import nl.dpes.job.poster.api.admin.config._
import nl.dpes.job.poster.api.admin.defaults.DefaultsController
import nl.dpes.job.poster.api.admin.home.HomeController
import nl.dpes.job.poster.api.admin.service.creditservice.{CreditService, CreditServiceClient}
import nl.dpes.job.poster.api.admin.status.StatusController
import nl.dpes.job.poster.api.database.{Database, TableName}
import nl.dpes.job.poster.api.service.apikey.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ApiKeyRepository}
import nl.dpes.job.poster.api.service.defaults.{DefaultsRepository, DefaultsService}
import nl.dpes.job.poster.api.service.recruiter.RecruiterService
import org.http4s.HttpRoutes
import org.http4s.blaze.server.BlazeServerBuilder
import org.http4s.server.Server
import org.http4s.server.middleware.{ErrorAction, ErrorHandling}
import org.typelevel.log4cats.slf4j.Slf4jFactory
import org.typelevel.log4cats.Logger
import pureconfig.ConfigSource
import sttp.client3.SttpBackend
import sttp.client3.asynchttpclient.fs2.AsyncHttpClientFs2Backend

object Main extends IOApp.Simple {

  implicit val loggerFactory: Slf4jFactory[IO] = Slf4jFactory.create[IO]

  // todo: move to own class
  implicit val loggerIO: Logger[IO] = new Logger[IO] {
    override def error(t: Throwable)(message: => String): IO[Unit] = IO.println(s"error: $message")

    override def warn(t: Throwable)(message: => String): IO[Unit] = IO.println(s"warn: $message")

    override def info(t: Throwable)(message: => String): IO[Unit] = IO.println(s"info: $message")

    override def debug(t: Throwable)(message: => String): IO[Unit] = IO.println(s"debug: $message")

    override def trace(t: Throwable)(message: => String): IO[Unit] = IO.println(s"trace: $message")

    override def error(message: => String): IO[Unit] = IO.println(s"error: $message")

    override def warn(message: => String): IO[Unit] = IO.println(s"warn: $message")

    override def info(message: => String): IO[Unit] = IO.println(s"info: $message")

    override def debug(message: => String): IO[Unit] = IO.println(s"debug: $message")

    override def trace(message: => String): IO[Unit] = IO.println(s"trace: $message")
  }

  // todo: move to own class (with witherrorlogging)
  def errorHandler(t: Throwable, msg: => String)(implicit logger: Logger[IO]): OptionT[IO, Unit] =
    OptionT.liftF(
      logger.error(msg) >>
      logger.error(t.toString) >>
      logger.error(t.getStackTrace.mkString("/n"))
    )

  val withErrorLogging: HttpRoutes[IO] => HttpRoutes[IO] = routes =>
    ErrorHandling.Recover.total(
      ErrorAction.log(
        routes,
        messageFailureLogAction = errorHandler,
        serviceErrorLogAction = errorHandler
      )
    )

  lazy val backend: Resource[IO, SttpBackend[IO, Any]] = AsyncHttpClientFs2Backend.resource[IO]()

  override def run: IO[Unit] = {
    implicit lazy val context: SqliteJdbcContext[SnakeCase.type] = new SqliteJdbcContext(SnakeCase, "database")

    val appResource: Resource[IO, Server] = for {
      AppConfig(blazeConfig, oneLoginConfig, dbConfig, apiKeyConfig, recruitSrvConfig, creditServiceConfig) <- Resource.eval(
        ConfigSource.default.loadConfig[IO, AppConfig]
      )
      implicit0(transactor: Transactor[IO]) <- Database.asResource[IO](dbConfig)
      implicit0(dispatcher: Dispatcher[IO]) <- Dispatcher.parallel[IO]
      homeModule                            <- HomeController.resource[IO]()
      creditServiceClient                   <- Resource.pure(CreditServiceClient[IO](backend, creditServiceConfig.host.value))
      creditService                         <- Resource.pure(CreditService[IO](creditServiceClient))
      defaultsRepository                    <- DefaultsRepository[IO](TableName("defaults_entry"))
      defaultsService                       <- Resource.pure(DefaultsService[IO](defaultsRepository))
      defaultsModule                        <- DefaultsController.resource[IO](creditService, defaultsService, loggerFactory)
      auth                                  <- AuthModule[IO](oneLoginConfig)
      statusModule                          <- StatusController.resource[IO]()
      apiKeyModule <- ApiKeyController.resource[IO](
        ApiKeyRepository.impl(ApiKeyHasher(apiKeyConfig.salt)),
        RecruiterService.apply(RecruiterService.connection[IO](recruitSrvConfig.host.value, recruitSrvConfig.port.value))
      )
      _ <- Resource.pure(println(s"Starting server ${blazeConfig.host} : ${blazeConfig.port}"))
      server <- BlazeServerBuilder[IO]
        .bindHttp(blazeConfig.port.value, blazeConfig.host.value)
        .withoutBanner
        .withHttpApp(
          withErrorLogging(
            homeModule.routes <+> auth.routes <+> statusModule.routes <+> auth.secure(apiKeyModule.routes <+> defaultsModule.routes)
          ).orNotFound
        )
        .resource
      _ <- Resource.pure(IO.println("Server ready"))
    } yield server

    appResource.use(_ => IO.never.onCancel(IO.println("Shutting down...")))
  }
}
