package nl.dpes.job.poster.api.admin.defaults

import cats.implicits._
import io.circe._
import io.circe.generic.semiauto._
import nl.dpes.job.poster.api.admin.service.creditservice.ProductName
import org.http4s.{FormDataDecoder, ParseFailure}
import org.http4s.FormDataDecoder._

case class DefaultsForm(productName: ProductName, withEasyApply: Boolean)

object DefaultsForm {
  implicit val encoder: Encoder[DefaultsForm] = deriveEncoder[DefaultsForm]
  implicit val decoder: Decoder[DefaultsForm] = deriveDecoder[DefaultsForm]

  implicit val formDataDecoder: FormDataDecoder[DefaultsForm] = FormDataDecoder.apply[DefaultsForm] { formData =>
    val productNameF   = getFormStringField("productName", formData).map(ProductName.apply)
    val withEasyApplyF = getFormBooleanField("withEasyApply", formData).some
    (productNameF, withEasyApplyF)
      .mapN(DefaultsForm.apply)
      .map(_.validNel)
      .getOrElse(ParseFailure("error", "Error when trying to parse 'DefaultsForm'").invalidNel)
  }

  def getFormStringField(formField: String, formData: FormData): Option[String] =
    for {
      field <- formData.get(formField)
      value <- field.headOption
    } yield value

  def getFormBooleanField(formField: String, formData: FormData): Boolean =
    (for {
      field <- formData.get(formField)
      value <- field.headOption
    } yield value == "on")
      .getOrElse(false)
}
