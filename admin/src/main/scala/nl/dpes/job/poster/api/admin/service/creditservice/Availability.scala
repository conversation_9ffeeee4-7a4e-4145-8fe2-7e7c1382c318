package nl.dpes.job.poster.api.admin.service.creditservice

import cats._
import cats.implicits._
import io.circe.{Decoder, Encoder}

import scala.concurrent.duration._
import scala.util.Try

case class Availability private (value: Duration) extends AnyVal

object Availability {

  case class AvailabilityTooLow(duration: Duration)
      extends Throwable(s"Availability should be at least one day, ${duration.toDays} days provided")

  def apply[F[_]: ApplicativeThrow](value: Duration): F[Availability] =
    if (value < 1.days) ApplicativeThrow[F].raiseError(AvailabilityTooLow(value))
    else ApplicativeThrow[F].pure(new Availability(value))

  implicit lazy val jsonEncoder: Encoder[Availability] = Encoder.encodeLong.contramap(_.value.toDays)
  implicit lazy val jsonDecoder: Decoder[Availability] = Decoder.decodeLong.emapTry(duration => Availability[Try](duration.days))
}
