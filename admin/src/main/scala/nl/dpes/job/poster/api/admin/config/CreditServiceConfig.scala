package nl.dpes.job.poster.api.admin.config

import nl.dpes.job.poster.api.admin.config.CreditServiceConfig.Host
import pureconfig.ConfigReader
import pureconfig.generic.semiauto.deriveReader

case class CreditServiceConfig(host: Host)

object CreditServiceConfig {
  case class Host(value: String) extends AnyVal
  implicit val hostConfigReader: ConfigReader[Host] = ConfigReader[String].map(Host.apply)

  implicit val creditServiceConfigReader: ConfigReader[CreditServiceConfig] = deriveReader[CreditServiceConfig]
}
