package nl.dpes.job.poster.api.admin.defaults

import scalatags.Text.all._
import nl.dpes.job.poster.api.admin.service.creditservice.ProductName
import nl.dpes.job.poster.api.admin.layout.Form

case object DefaultsPage {

  def render(
    recruiterId: String,
    jobPostingProductNames: List[ProductName],
    isEasyApply: Boolean,
    defaultProductName: Option[ProductName]
  ): Modifier =
    form(action := s"/recruiter/$recruiterId/defaults", method := "POST")(
      h2("Welcome to the Job Poster API admin"),
      p(s"Manage defaults on this page"),
      Form.selectFromOptions("productName", "Job product name", defaultProductName.map(_.value), jobPostingProductNames.map(_.value)),
      Form.checkbox("withEasyApply", "With Easy Apply", isEasyApply),
      input(`class` := "btn btn-outline-primary", `type` := "submit", name := "Save")
    )
}
