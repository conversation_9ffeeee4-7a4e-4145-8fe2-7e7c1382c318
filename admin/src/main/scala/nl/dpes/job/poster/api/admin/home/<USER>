package nl.dpes.job.poster.api.admin.home

import cats.effect.{Async, Resource}
import cats.implicits._
import nl.dpes.job.poster.api.admin.layout.MainPage
import org.http4s.dsl.Http4sDsl
import org.http4s.headers.`Content-Type`
import org.http4s.{HttpRoutes, StaticFile}

class HomeController[F[_]: Async]() extends Http4sDsl[F] {

  private val homePage: HttpRoutes[F] = HttpRoutes.of[F] { case req @ GET -> Root =>
    for {
      resp <- Ok(MainPage(HomePage.render).render)
    } yield resp
      .withContentType(`Content-Type`(org.http4s.MediaType.text.html))
  }

  private val css = HttpRoutes.of[F] { case req @ GET -> Root / "css" / fileName =>
    StaticFile.fromResource(s"css/$fileName").getOrElseF(NotFound())
  }

  def routes: HttpRoutes[F] = homePage <+> css
}

object HomeController {
  def resource[F[_]: Async](): Resource[F, HomeController[F]] = Resource.pure(new HomeController[F])
}
