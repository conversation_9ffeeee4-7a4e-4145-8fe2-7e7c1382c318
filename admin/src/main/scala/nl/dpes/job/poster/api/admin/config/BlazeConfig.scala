package nl.dpes.job.poster.api.admin.config

import nl.dpes.job.poster.api.admin.config.BlazeConfig._
import pureconfig._
import pureconfig.generic.semiauto._

case class BlazeConfig(host: Host, port: Port)

object BlazeConfig {
  case class Host(value: String) extends AnyVal
  implicit val hostConfigReader: ConfigReader[Host] = ConfigReader[String].map(Host.apply)

  case class Port(value: Int) extends AnyVal
  implicit val portConfigReader: ConfigReader[Port] = ConfigReader[Int].map(Port.apply)

  implicit val blazeConfigReader: ConfigReader[BlazeConfig] = deriveReader[BlazeConfig]
}
