package nl.dpes.job.poster.api.admin.session

import doobie.Meta
import io.circe.parser._
import io.circe.syntax._

import java.io.{ByteArrayInputStream, ByteArrayOutputStream, ObjectInputStream, ObjectOutputStream}
import java.nio.charset.StandardCharsets.UTF_8
import java.util.Base64
import scala.language.implicitConversions

case class Session(data: Map[String, String] = Map.empty) {

  import Session._

  def get(key: String): Option[Any] = data.get(key).map(deserialise)

  def set(key: String, value: Any): Session =
    if (value == null) this.copy(data = data - key)
    else this.copy(data = data + (key -> serialise(value)))

}

object Session {

  def serialise(value: Any): String = {
    val stream: ByteArrayOutputStream = new ByteArrayOutputStream()
    val oos                           = new ObjectOutputStream(stream)
    oos.writeObject(value)
    oos.close()
    new String(
      Base64.getEncoder.encode(stream.toByteArray),
      UTF_8
    )
  }

  def deserialise(str: String): Any = {
    val bytes = Base64.getDecoder.decode(str.getBytes(UTF_8))
    val ois   = new ObjectInputStream(new ByteArrayInputStream(bytes))
    val value = ois.readObject
    ois.close()
    value
  }

  private def parseDbSession(session: String): Session = decode[Map[String, String]](session) match {
    case Left(error) =>
      println(s"COULD NOT PARSE SESSION!!!! $error, data: $session") // todo: use logger for this situation
      Session()
    case Right(parsedSessionData) => Session(parsedSessionData)
  }

  implicit val sessionMeta: Meta[Session] = Meta[String].imap(parseDbSession)(_.data.asJson.noSpaces)
}
