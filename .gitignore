# Scala/SBT specific
*.class
*.log
.cache/
.history/
.lib/
dist/*
target/
lib_managed/
src_managed/
project/boot/
project/plugins/project/
project/target/
project/project/target/
.scala_dependencies
.worksheet

# Metals/Bloop specific
.bloop/
.metals/
metals.sbt
.bsp/

# IntelliJ IDEA specific
.idea/
*.iml
*.iws
*.ipr
out/
.idea_modules/

# VS Code specific
.vscode/
.ionide/

# build tool
frontend-service/project/build.properties
.gradle/
salesforce/docker/prod/build/
salesforce/docker/prod/*.jar
salesforce/docker/prod/gateway/
salesforce/project/build.properties
lib/

# test artifacts
data/

# sonar
.scannerwork/

# os
.DS_Store

# Ignore environment configuration files
**/.secrets.env

# database contents
/docker/mysql/data/
