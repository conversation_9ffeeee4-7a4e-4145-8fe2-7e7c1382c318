version: '2.4'
networks:
  default:
    name: b2b_default
    external: true

services:
  job-poster-api:
    image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/job-poster-api-public-api:latest
    ports:
      - 11360:11360
    container_name: job-poster-api-service
    env_file:
      - public-api/.env
      - ./public-api/.secrets.env
    depends_on:
      database:
        condition: service_healthy

  job-poster-api-admin:
    image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/job-poster-api-admin:latest
    ports:
      - 11363:11363
    container_name: job-poster-api-admin
    env_file:
      - ./admin/.env
      - ./admin/.secrets.env
    depends_on:
      database:
        condition: service_healthy

  database:
    image: mysql:8.0.25
    container_name: job-poster-api-database
    environment:
      - MYSQL_DATABASE=job-poster-api
      - MYSQL_USER=job-poster-api
      - MYSQL_PASSWORD=password
      - MYSQL_RANDOM_ROOT_PASSWORD=yes
    healthcheck:
      test: [ "CMD", "mysqladmin" ,"ping", "-h", "localhost" ]
      interval: 5s
      retries: 120
    ports:
      - 11361:3306
    volumes:
      - ./docker/mysql:/docker-entrypoint-initdb.d
      - ./docker/mysql/data:/var/lib/mysql
    ulimits:
      nproc: 65535
      nofile:
        soft: 20000
        hard: 40000

  file-service:
    image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/file-service:980
    container_name: job-poster-api-file-service
    ports:
      - 11500:1500
    environment:
      - S3_ENDPOINT_URL=http://job-poster-api-localstack:4566
    ulimits:
      nproc: 65535
      nofile:
        soft: 20000
        hard: 40000

  localstack:
    image: localstack/localstack:1.4
    container_name: job-poster-api-localstack
    ports:
      - "127.0.0.1:5566:4566"            # LocalStack Gateway
    environment:
      - SERVICES=s3
      - DEFAULT_REGION=eu-west-1
    volumes:
      - "./docker/localstack-init.sh:/docker-entrypoint-initaws.d/setup-dev-env.sh"
