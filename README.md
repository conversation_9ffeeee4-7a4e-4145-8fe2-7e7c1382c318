# Job Poster API

This project contains 2 applications: job-poster-api-public-api — publicly exposed — and job-poster-api-admin — only
available via the internal network. Both applications can be run individually.

These projects have their own environment varialble configuration, which consists of 2 parts:

- not secret values, these are just set in the .env
- secret values, these have keys in the .env, with the value *see_secrets* and an entry in the .secrets.env — which will
  not be committed to the version control system, but needs to be added next to the .env file — containing the actual
  value

## Public API
The public api is a REST service and contains 2 main parts

- ATS, which contains job management, job performance and getting applicants, accessed with a Job Poster API API key
- Job configuration, setting the defaults via the dashboard (in b2b project), accessed with a SalsesForce access token

## Admin
The admin is a standalone app that can manage API keys for recruiters. This application is meant to be accessed via
Salesforce, where the relevant recruiter can be found.

### Prerequisites

This project requires:

* [JAVA 21](http://openjdk.java.net)
* [SBT 1.6.x](https://www.scala-sbt.org/)
* [Scala 2.13.x](https://docs.scala-lang.org/getting-started/index.html)
* [Docker](https://docs.docker.com/engine/install/)

### Run locally

First, before running **Job Poster API**, you need to start [**Job Manager** project](https://github.com/Vnumedia/b2b),
which is responsible for the lifecycle of all jobs.

Second, you need to set add the `.secrets.env` containing the key value pairs for all keys having *see_secrets* as value.

Finally, you run:

```
$ docker-compose up -d
```

The swagger documentation to **post**/**update**/**delete**/**suspend**/**resume** a job will
be [available here](http://0.0.0.0:11360/docs/v1/).
In order to use this API, you need an API Key. You can generate this key using
the [Persgroep dashboard](https://b2b-dev.persgroep.digital/dashboard/job-poster/api-key).
Once it is generated, you should copy it in the Job Poster API Swagger authentication pop-up window.

This project allows you, as well, to save default job details using
the [following swagger API](http://0.0.0.0:11360/docs/service/).
In order to use this API, you need a **b2b_token** (like the one used in our persgroep dashboard) that you should copy
inside the Job Poster API Swagger authentication pop-up window.

The Job Poster API ensures getting the job applications [via this endpoint](http://0.0.0.0:11360/docs/v1/).
You need to be authenticated in order to use this endpoint. The authentication is via an [**API Key
**](https://b2b-dev.persgroep.digital/dashboard/job-poster/api-key)
that you should copy inside the Job Poster API Swagger authentication pop-up window.

### Tests

```
$ sbt test
```

### Test Coverage

```
$ sbt coverageReport 
```

### Production environment

You can check the production environment through the following links:

- [Job Poster API](https://job-poster-api.persgroep.digital/docs/v1/)
- [API Key and Default API](https://job-poster-api.persgroep.digital/docs/service/)
- [Job Applications](https://job-poster-api.persgroep.digital/docs/v1/)

You can get your [API Key](https://www.persgroepemploymentsolutions.nl/dashboard/job-poster/api-key)
and set the [defaults](https://www.persgroepemploymentsolutions.nl/dashboard/job-poster/defaults) using the dashboard as
well.

## How to contribute

Before pushing your code, you may run the following command to format the code, compile, check tests and get the
coverage report:

```
$ sbt prepare-commit
```

## Deployment

After pushing, your code will be deployed
by [Jenkins](http://ndp-jenkins-master.persgroep.digital:8080/job/Job%20Poster%20API/)
 
