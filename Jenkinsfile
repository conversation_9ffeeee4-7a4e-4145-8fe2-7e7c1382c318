pipeline {
    agent {
        node {
            label 'alpakka_java21'
        }
    }
    environment {
        String SLACK_CHANNEL = '#dpgr_alpakka_notifications'
        String SLACK_MESSAGE = "@here Build #${env.BUILD_NUMBER} for ${env.JOB_NAME} (<${env.BUILD_URL}|open>) has failed"
        String SLACK_TEAM_DOMAIN = 'dpos'
    }
    options {
        ansiColor('xterm')
        buildDiscarder(logRotator(numToKeepStr: '25'))
        disableConcurrentBuilds()
        disableResume()
    }
    stages {
        stage('initialize') {
            steps {
                script {
                    def scmVars = checkout scm
                    env.MY_GIT_PREVIOUS_SUCCESSFUL_COMMIT = scmVars.GIT_PREVIOUS_SUCCESSFUL_COMMIT
                    sh "sbt -Drevision=${BUILD_NUMBER} update"
                }
            }
        }
        stage('shared library') {
            steps {
                script {
                    if (canSkipModule("^shared/") && false) {
                        echo '\u2B24 Do not build shared'
                    } else {
                        echo '\u2B24 Build shared'
                        build job: 'ACC-Job-Poster-API LIB Shared', parameters: [
                                [$class: 'StringParameterValue', name: 'MODULE_BUILD_NUMBER', value: "${BUILD_NUMBER}"],
                                [$class: 'StringParameterValue', name: 'MODULE_NAME', value: "shared"]
                        ]
                    }
                }
            }
        }
        stage('acceptance') {
            parallel {
                stage('public-api') {
                    steps {
                        script {
                            if (canSkipModule("^shared/|^public-api/") && false) {
                                echo '\u2B24 Do not build public-api'
                            } else {
                                echo '\u2B24 Build public api'
                                build job: 'ACC-Job-Poster-API APP Public API', parameters: [
                                        [$class: 'StringParameterValue', name: 'MODULE_BUILD_NUMBER', value: "${BUILD_NUMBER}"],
                                        [$class: 'StringParameterValue', name: 'MODULE_NAME', value: "public-api"]
                                ]
                            }
                        }
                    }
                }
                stage('admin') {
                    steps {
                        script {
                            if (canSkipModule("^shared/|^admin/")) {
                                echo '\u2B24 Do not build admin'
                            } else {
                                echo '\u2B24 Build admin'
                                build job: 'ACC-Job-Poster-API-APP-Admin', parameters: [
                                        [$class: 'StringParameterValue', name: 'MODULE_BUILD_NUMBER', value: "${BUILD_NUMBER}"],
                                        [$class: 'StringParameterValue', name: 'MODULE_NAME', value: "admin"]
                                ]
                            }
                        }
                    }
                }
            }
        }
        stage('production') {
            parallel {
                stage('public-api') {
                    steps {
                        script {
                            if (canSkipModule("^shared/|^public-api/") && false) {
                                echo '\u2B24 Do not deploy public api'
                            } else {
                                echo '\u2B24 Deploy public api'
                                build job: 'PRO-Job-Poster-API APP Public API', parameters: [
                                        [$class: 'StringParameterValue', name: 'MODULE_BUILD_NUMBER', value: "${BUILD_NUMBER}"],
                                        [$class: 'StringParameterValue', name: 'MODULE_NAME', value: "public-api"]
                                ]
                            }
                        }
                    }
                }
                stage('admin') {
                    steps {
                        script {
                            if (canSkipModule("^shared/|^admin/")) {
                                echo '\u2B24 Do not build admin'
                            } else {
                                echo '\u2B24 Build admin'
                                build job: 'PRO-Job-Poster-API APP Admin', parameters: [
                                        [$class: 'StringParameterValue', name: 'MODULE_BUILD_NUMBER', value: "${BUILD_NUMBER}"],
                                        [$class: 'StringParameterValue', name: 'MODULE_NAME', value: "admin"]
                                ]
                            }
                        }
                    }
                }
            }
        }
    }
}

def slack(String channel, String color, String message, String teamDomain, String token) {
    slackSend channel: channel, color: color, message: message, teamDomain: teamDomain, token: token
}

def canSkipModule(String moduleRegex) {
    return sh(returnStatus: true, script: "git diff --name-only $MY_GIT_PREVIOUS_SUCCESSFUL_COMMIT|egrep -q '${moduleRegex}'")
}
