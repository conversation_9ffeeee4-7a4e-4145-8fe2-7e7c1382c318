import scala.language.postfixOps
import scala.sys.process.*

ThisBuild / version := Option(System.getProperty("version")).getOrElse("1")

val scVersion                 = "2.13.16"
val LogbackVersion            = "1.5.18"
val tapirVersion              = "1.11.44"
val pureConfigVersion         = "0.17.9"
val http4sVersion             = "0.23.17"
val http4sDslVersion          = "0.23.30"
val weaverVersion             = "0.8.4"
val quillVersion              = "4.8.5"
val mysqlCxnVersion           = "8.0.33"
val sttpVersion               = "3.11.0"
val mockitoVersion            = "2.0.0"
val doobieVersion             = "1.0.0-RC10"
val testContainersVersion     = "1.21.3"
val log4catsVersion           = "2.7.1"
val scalaTagsVersion          = "0.13.1"
val http4sPac4jVersion        = "5.0.0"
val circeVersion              = "0.14.14"
val circeGenericExtrasVersion = "0.14.4"
val pac4jVersion              = "6.2.1"
val chimneyVersion            = "1.8.2"
val scalaTestVersion          = "3.2.19"
val serviceDefinitionsVersion = "10497"
val jsoupVersion              = "1.21.2"
val jacksonVersion            = "2.20.0"
val jacksonAnnotationsVersion = "2.20"

ThisBuild / scalaVersion := scVersion
ThisBuild / envFileName := ".env"

autoCompilerPlugins := true
addCompilerPlugin("com.olegpy" %% "better-monadic-for" % "0.3.1")
enablePlugins(Fs2Grpc)

lazy val commonSettings = Seq(
  Compile / compile / logLevel := Level.Error,
  commands ++= Commands.additionalCommands,
  dockerRepository := Some("674201978047.dkr.ecr.eu-west-1.amazonaws.com"),
  maintainer := "Alpakka <<EMAIL>>",
  scalaVersion := scVersion,
  packageDoc / publishArtifact := false,
  autoCompilerPlugins := true,
  scalacOptions ++= Seq(
    "-language:postfixOps",
    "-Ytasty-reader"
  ) ++ (CrossVersion.partialVersion(scalaVersion.value) match {
    case Some((2, n)) if n >= 13 =>
      Seq(
        "-Ymacro-annotations"
      )
    case _ => Seq.empty
  }),
  resolvers ++= Seq(
    "snapshots" at "https://archiva.persgroep.digital/repository/snapshots",
    "internal" at "https://archiva.persgroep.digital/repository/internal",
    "releases" at "https://archiva.persgroep.digital/repository/releases"
  ),
  scalacOptions --= Seq(
    "-Ywarn-unused:implicits",
    "-Werror",
    "-Xcheckinit",
    "-Xfatal-warnings",
    "-Xlint"
  ),
  addCompilerPlugin("com.olegpy" %% "better-monadic-for" % "0.3.1"),
  dependencyOverrides ++= Seq(
    "com.fasterxml.jackson.core"    % "jackson-core"         % jacksonVersion,
    "com.fasterxml.jackson.core"    % "jackson-annotations"  % jacksonAnnotationsVersion,
    "com.fasterxml.jackson.core"    % "jackson-databind"     % jacksonVersion,
    "com.fasterxml.jackson.module" %% "jackson-module-scala" % jacksonVersion
  )
)

val commonDependencies = Seq(
  "com.github.pureconfig" %% "pureconfig" % pureConfigVersion,
  "io.scalaland"          %% "chimney"    % chimneyVersion,
  "org.jsoup"              % "jsoup"      % jsoupVersion // this is _not_ common!
)

val loggingDependencies = Seq(
  "ch.qos.logback" % "logback-classic"  % LogbackVersion  % Runtime,
  "org.typelevel" %% "log4cats-slf4j"   % log4catsVersion,
  "org.typelevel" %% "log4cats-testing" % log4catsVersion % Test
)

val frontEndDependencies = Seq(
  "com.lihaoyi" %% "scalatags"      % scalaTagsVersion,
  "org.webjars"  % "bootstrap-sass" % "3.4.1"
)

val dbDependencies = Seq(
  "io.getquill" %% "quill-jdbc"           % quillVersion,
  "mysql"        % "mysql-connector-java" % mysqlCxnVersion
)

val tapirDependencies = Seq(
  "com.softwaremill.sttp.tapir" %% "tapir-core"              % tapirVersion,
  "com.softwaremill.sttp.tapir" %% "tapir-http4s-server"     % tapirVersion,
  "com.softwaremill.sttp.tapir" %% "tapir-json-circe"        % tapirVersion,
  "com.softwaremill.sttp.tapir" %% "tapir-openapi-docs"      % tapirVersion,
  "com.softwaremill.sttp.tapir" %% "tapir-swagger-ui-bundle" % tapirVersion
)

val jsonDependencies = Seq(
  "io.circe" %% "circe-core"           % circeVersion,
  "io.circe" %% "circe-generic"        % circeVersion,
  "io.circe" %% "circe-parser"         % circeVersion,
  "io.circe" %% "circe-generic-extras" % circeGenericExtrasVersion
)

val serverDependencies = Seq(
  "org.http4s" %% "http4s-dsl"          % http4sDslVersion,
  "org.http4s" %% "http4s-blaze-client" % http4sVersion,
  "org.http4s" %% "http4s-blaze-server" % http4sVersion
)

val restClientDependencies = Seq(
  "com.softwaremill.sttp.client3" %% "core"                          % sttpVersion,
  "com.softwaremill.sttp.client3" %% "async-http-client-backend-fs2" % sttpVersion,
  "com.softwaremill.sttp.client3" %% "circe"                         % sttpVersion
)

val jacksonDependencies = Seq(
  "com.fasterxml.jackson.core"    % "jackson-core"         % jacksonVersion,
  "com.fasterxml.jackson.core"    % "jackson-annotations"  % jacksonAnnotationsVersion,
  "com.fasterxml.jackson.core"    % "jackson-databind"     % jacksonVersion,
  "com.fasterxml.jackson.module" %% "jackson-module-scala" % jacksonVersion
)

val b2bDependencies = Seq(
  "io.grpc"      % "grpc-netty-shaded"   % scalapb.compiler.Version.grpcJavaVersion,
  "nl.dpes.b2b" %% "service-definitions" % serviceDefinitionsVersion excludeAll (
    ExclusionRule("com.fasterxml.jackson.core"),
    ExclusionRule("org.scala-lang.modules")
  )
) ++ jacksonDependencies

val testDependencies = Seq(
  "com.disneystreaming" %% "weaver-cats"             % weaverVersion    % Test,
  "com.disneystreaming" %% "weaver-scalacheck"       % weaverVersion    % Test,
  "org.mockito"         %% "mockito-scala-scalatest" % mockitoVersion   % Test,
  "org.scalatest"       %% "scalatest"               % scalaTestVersion % Test
)

val pac4jDependencies = Seq(
  "org.pac4j" %% "http4s-pac4j" % http4sPac4jVersion,
  "org.pac4j"  % "pac4j-oidc"   % pac4jVersion excludeAll (ExclusionRule("com.fasterxml.jackson.core"))
)

val doobieDependencies = Seq(
  "org.tpolecat" %% "doobie-core"           % doobieVersion,
  "org.tpolecat" %% "doobie-hikari"         % doobieVersion,
  "org.tpolecat" %% "doobie-postgres"       % doobieVersion,
  "org.tpolecat" %% "doobie-postgres-circe" % doobieVersion,
  "org.tpolecat" %% "doobie-scalatest"      % doobieVersion % Test
)

val testContainersDependencies = Seq(
  "org.testcontainers" % "testcontainers" % testContainersVersion % Test,
  "org.testcontainers" % "mysql"          % testContainersVersion % Test
)

testFrameworks += new TestFramework("weaver.framework.CatsEffect")
scalapbCodeGeneratorOptions += CodeGeneratorOption.Fs2Grpc
Compile / PB.includePaths += (baseDirectory.value / "target" / "protobuf_external_src")

lazy val scapegoatSettings = Seq(
  ThisBuild / scapegoatVersion := "3.1.8",
  scapegoatIgnoredFiles := Seq(
    ".*/nl/dpes/fileservice/.*",
    ".*/target/.*",
    ".*/src/test/resources/.*"
  ),
  scapegoatDisabledInspections := Seq(
    "ComparingUnrelatedTypes",
    "MethodReturningAny",   // Often unavoidable in API contexts
    "VarCouldBeVal",        // Can be noisy in certain contexts
    "AvoidOperatorOverload" // Common in Scala DSLs and libraries
  ),
  scalacOptions in Scapegoat += "-P:scapegoat:overrideLevels:OptionGet=Error:TryGet=Error:UnsafeTraversableMethods=Warning:MaxParameters=Warning:BooleanParameter=Warning",
  scapegoatConsoleOutput := true,
  scapegoatReports := Seq("html", "xml"),
  scapegoatMinimalWarnLevel := "Warning"
)

lazy val coverageSettings = Seq(
  Test / compile / coverageEnabled := true,
  Compile / compile / coverageEnabled := false,
  coverageFailOnMinimum := true,
  coverageHighlighting := true,
  coverageAggregate := true
)

lazy val sonarSettings = Seq(
  sonarProperties ++= Map(
    "sonar.host.url"                    -> "https://sonar.persgroep.digital",
    "sonar.projectVersion"              -> version.value,
    "sonar.projectKey"                  -> Seq(organization.value, name.value).mkString("."),
    "sonar.projectName"                 -> "Job Poster API",
    "sonar.projectBaseDir"              -> baseDirectory.value.getPath,
    "sonar.scala.coverage.reportPaths"  -> (crossTarget.value / "scoverage-report" / "scoverage.xml").getPath,
    "sonar.scala.scapegoat.reportPaths" -> (crossTarget.value / "scapegoat-report" / "scapegoat-scalastyle.xml").getPath,
    "sonar.sourceEncoding"              -> "UTF-8",
    "sonar.sources"                     -> "src/main/scala",
    "sonar.tests"                       -> "src/test/scala",
    "sonar.qualitygate.wait"            -> "true",
    "sonar.coverage.exclusions"         -> "**/*Controller*.*, **/*package.scala, **/*Config*.scala, **/Main.scala, **/*Page.scala, **/ApiKeyTapirService.scala, **/JobManagerFactory.scala"
  )
)

lazy val root = (project in file("."))
  .settings(
    name := "job-poster-api"
  )
  .aggregate(admin, `public-api`, shared)
  .settings(commonSettings *)
  .dependsOn(admin, `public-api`, shared)

lazy val admin = (project in file("admin"))
  .settings(
    name := "job-poster-api-admin",
    coverageMinimumBranchTotal := 0,
    libraryDependencies ++=
      serverDependencies ++
      pac4jDependencies ++
      frontEndDependencies ++
      dbDependencies ++
      doobieDependencies ++
      testContainersDependencies ++
      commonDependencies ++
      jsonDependencies ++
      restClientDependencies ++
      testDependencies,
    Compile / resourceGenerators += Def.task {
      val sassDir = (Compile / baseDirectory).value / "src/main/sass"
      val cssDir  = (Compile / resourceManaged).value / "css"

      val command = s"docker run -v $sassDir:/sass -v $cssDir:/css --entrypoint /opt/dart-sass/sass michalklempa/dart-sass /sass:/css"
      println(s"""Compiling SASS files
          |$command
          |""".stripMargin)
      command !

      cssDir.listFiles().toSeq
    }.taskValue,
    resolvers ++= Seq(
      DefaultMavenRepository,
      Resolver.typesafeRepo("releases"),
      Resolver.sonatypeRepo("public"),
      Resolver.sbtPluginRepo("releases"),
      "snapshots" at "https://archiva.persgroep.digital/repository/snapshots",
      "internal" at "https://archiva.persgroep.digital/repository/internal",
      "releases" at "https://archiva.persgroep.digital/repository/releases"
    )
  )
  .settings(commonSettings *)
  .settings(coverageSettings *)
  .settings(scapegoatSettings *)
  .settings(sonarSettings *)
  .dependsOn(shared)

lazy val `public-api` = (project in file("public-api"))
  .settings(
    name := "job-poster-api-public-api",
    libraryDependencies ++=
      commonDependencies ++
      serverDependencies ++
      tapirDependencies ++
      restClientDependencies ++
      b2bDependencies ++
      testDependencies ++
      testContainersDependencies ++
      doobieDependencies ++
      dbDependencies,
    scalapbCodeGeneratorOptions += CodeGeneratorOption.Fs2Grpc,
    coverageMinimumBranchTotal := 48,
    Compile / PB.includePaths += (baseDirectory.value / "target" / "protobuf_external_src")
  )
  .settings(commonSettings *)
  .settings(coverageSettings *)
  .settings(scapegoatSettings *)
  .settings(sonarSettings *)
  .enablePlugins(Fs2Grpc)
  .dependsOn(shared)

lazy val shared = (project in file("shared"))
  .settings(
    libraryDependencies ++=
      testDependencies ++
      dbDependencies ++
      b2bDependencies ++
      testContainersDependencies ++
      testDependencies ++
      doobieDependencies ++
      commonDependencies ++
      jsonDependencies ++
      loggingDependencies,
    coverageMinimumBranchTotal := 0
  )
  .settings(commonSettings *)
  .settings(coverageSettings *)
  .settings(scapegoatSettings *)
  .settings(sonarSettings *)
  .enablePlugins(Fs2Grpc)
