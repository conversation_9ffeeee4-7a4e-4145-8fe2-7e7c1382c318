package nl.dpes.job.poster.api.database

import DbConfig._
import pureconfig.ConfigReader
import pureconfig.generic.semiauto.deriveReader

case class DbConfig(url: Url, threadCount: ThreadCount, userName: UserName, userPassword: UserPassword)

object DbConfig {
  final case class Url(value: String) extends AnyVal
  implicit val urlReader: ConfigReader[Url] = ConfigReader[String].map(Url.apply)

  final case class ThreadCount(value: Int) extends AnyVal
  implicit val threadCountReader: ConfigReader[ThreadCount] = ConfigReader[Int].map(ThreadCount.apply)

  final case class UserName(value: String) extends AnyVal
  implicit val userNameReader: ConfigReader[UserName] = ConfigReader[String].map(UserName.apply)

  final case class UserPassword(value: String) extends AnyVal
  implicit val userPasswordReader: ConfigReader[UserPassword] = ConfigReader[String].map(UserPassword.apply)

  implicit val dbConfigReader: ConfigReader[DbConfig] = deriveReader[DbConfig]
}
