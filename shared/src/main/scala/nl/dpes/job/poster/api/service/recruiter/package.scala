package nl.dpes.job.poster.api.service

import io.getquill.MappedEncoding
import nl.dpes.b2b.salesforce.domain.SalesForceId

package object recruiter {
  implicit val encodeSalesforceId: MappedEncoding[SalesForceId, String] = MappedEncoding[SalesForceId, String](_.idWithChecksum)
  implicit val decodeSalesforceId: MappedEncoding[String, SalesForceId] = MappedEncoding[String, SalesForceId](SalesForceId.unsafeApply)
}
