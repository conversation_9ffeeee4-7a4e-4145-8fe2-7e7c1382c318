package nl.dpes.job.poster.api.service.defaults

import io.circe.generic.semiauto.{deriveDecoder, deriveEncoder}
import io.circe.syntax.EncoderOps
import io.circe.generic.extras.semiauto._
import io.circe._
import io.getquill.MappedEncoding
import Defaults._
import doobie.Meta

import scala.concurrent.duration._

case class Defaults(
  title: Option[Title],
  description: Option[Html],
  occupation: Option[Occupation],
  jobCategories: Option[JobCategories],
  industryCategories: Option[IndustryCategories],
  educationLevels: Option[EducationLevels],
  careerLevel: Option[CareerLevel],
  contractTypes: Option[ContractTypes],
  workplace: Option[Workplace],
  workingHours: Option[Range[Hour]],
  salary: Option[SalaryRange],
  location: Option[ZipCode],
  applicationMethod: Option[ApplicationMethod],
  logo: Option[Logo],
  video: Option[Video],
  company: Option[Company],
  configuration: Option[Configuration]
)

object Defaults {

  val version: String = "0.4"
  val empty: Defaults = Defaults(None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None)

  case class Title(name: String) extends AnyVal

  object Title {
    implicit lazy val decoder: Decoder[Title] = deriveUnwrappedDecoder[Title]
    implicit lazy val encoder: Encoder[Title] = deriveUnwrappedEncoder[Title]
  }

  case class Html(document: String) extends AnyVal

  object Html {
    implicit lazy val decoder: Decoder[Html] = deriveUnwrappedDecoder[Html]
    implicit lazy val encoder: Encoder[Html] = deriveUnwrappedEncoder[Html]
  }

  case class Occupation(name: String) extends AnyVal

  object Occupation {
    implicit lazy val decoder: Decoder[Occupation] = deriveUnwrappedDecoder[Occupation]
    implicit lazy val encoder: Encoder[Occupation] = deriveUnwrappedEncoder[Occupation]
  }

  case class JobCategory(name: String) extends AnyVal

  object JobCategory {
    implicit lazy val decoder: Decoder[JobCategory] = deriveUnwrappedDecoder[JobCategory]
    implicit lazy val encoder: Encoder[JobCategory] = deriveUnwrappedEncoder[JobCategory]
  }

  case class JobCategories(categories: Set[JobCategory])

  object JobCategories {
    implicit lazy val decoder: Decoder[JobCategories] = Decoder.decodeSet[JobCategory].map(JobCategories(_))
    implicit lazy val encoder: Encoder[JobCategories] = Encoder.encodeSet[JobCategory].contramap(_.categories)
  }

  case class IndustryCategory(name: String) extends AnyVal

  object IndustryCategory {
    implicit lazy val decoder: Decoder[IndustryCategory] = deriveUnwrappedDecoder[IndustryCategory]
    implicit lazy val encoder: Encoder[IndustryCategory] = deriveUnwrappedEncoder[IndustryCategory]
  }

  case class IndustryCategories(categories: Set[IndustryCategory])

  object IndustryCategories {
    implicit lazy val decoder: Decoder[IndustryCategories] = Decoder.decodeSet[IndustryCategory].map(IndustryCategories(_))
    implicit lazy val encoder: Encoder[IndustryCategories] = Encoder.encodeSet[IndustryCategory].contramap(_.categories)
  }

  case class EducationLevel(name: String) extends AnyVal

  object EducationLevel {
    implicit lazy val decoder: Decoder[EducationLevel] = deriveUnwrappedDecoder[EducationLevel]
    implicit lazy val encoder: Encoder[EducationLevel] = deriveUnwrappedEncoder[EducationLevel]
  }

  case class EducationLevels(levels: Set[EducationLevel])

  object EducationLevels {
    implicit lazy val decoder: Decoder[EducationLevels] = Decoder.decodeSet[EducationLevel].map(EducationLevels(_))
    implicit lazy val encoder: Encoder[EducationLevels] = Encoder.encodeSet[EducationLevel].contramap(_.levels)
  }

  case class CareerLevel(name: String) extends AnyVal

  object CareerLevel {
    implicit lazy val decoder: Decoder[CareerLevel] = deriveUnwrappedDecoder[CareerLevel]
    implicit lazy val encoder: Encoder[CareerLevel] = deriveUnwrappedEncoder[CareerLevel]
  }

  case class ContractType(name: String) extends AnyVal

  object ContractType {
    implicit lazy val decoder: Decoder[ContractType] = deriveUnwrappedDecoder[ContractType]
    implicit lazy val encoder: Encoder[ContractType] = deriveUnwrappedEncoder[ContractType]
  }

  case class ContractTypes(types: Set[ContractType])

  object ContractTypes {
    implicit lazy val decoder: Decoder[ContractTypes] = Decoder.decodeSet[ContractType].map(ContractTypes(_))
    implicit lazy val encoder: Encoder[ContractTypes] = Encoder.encodeSet[ContractType].contramap(_.types)
  }

  case class Workplace(name: String) extends AnyVal

  object Workplace {
    implicit lazy val decoder: Decoder[Workplace] = deriveUnwrappedDecoder[Workplace]
    implicit lazy val encoder: Encoder[Workplace] = deriveUnwrappedEncoder[Workplace]
  }

  case class Hour(amount: Int) extends AnyVal

  object Hour {
    implicit lazy val decoder: Decoder[Hour] = deriveUnwrappedDecoder[Hour]
    implicit lazy val encoder: Encoder[Hour] = deriveUnwrappedEncoder[Hour]
  }

  case class Range[T](lower: T, upper: T)

  object Range {
    implicit def rangeDecoder[T: Decoder]: Decoder[Range[T]] = deriveDecoder[Range[T]]
    implicit def rangeEncoder[T: Encoder]: Encoder[Range[T]] = deriveEncoder[Range[T]]
  }

  case class Period(value: String) extends AnyVal

  object Period {
    implicit lazy val decoder: Decoder[Period] = deriveUnwrappedDecoder[Period]
    implicit lazy val encoder: Encoder[Period] = deriveUnwrappedEncoder[Period]
  }

  case class SalaryRange(lower: Int, upper: Int, period: Option[Period])

  object SalaryRange {
    implicit lazy val decoder: Decoder[SalaryRange] = deriveDecoder[SalaryRange]
    implicit lazy val encoder: Encoder[SalaryRange] = deriveEncoder[SalaryRange]
  }

  case class ZipCode(zipcode: String) extends AnyVal

  object ZipCode {
    implicit lazy val decoder: Decoder[ZipCode] = deriveUnwrappedDecoder[ZipCode]
    implicit lazy val encoder: Encoder[ZipCode] = deriveUnwrappedEncoder[ZipCode]
  }

  sealed trait ApplicationMethod

  object ApplicationMethod {
    case class FirstName(name: String) extends AnyVal

    object FirstName {
      implicit lazy val decoder: Decoder[FirstName] = deriveUnwrappedDecoder[FirstName]
      implicit lazy val encoder: Encoder[FirstName] = deriveUnwrappedEncoder[FirstName]
    }
    case class LastName(name: String) extends AnyVal

    object LastName {
      implicit lazy val decoder: Decoder[LastName] = deriveUnwrappedDecoder[LastName]
      implicit lazy val encoder: Encoder[LastName] = deriveUnwrappedEncoder[LastName]
    }

    case class EmailAddress(address: String) extends AnyVal

    object EmailAddress {
      implicit lazy val decoder: Decoder[EmailAddress] = deriveUnwrappedDecoder[EmailAddress]
      implicit lazy val encoder: Encoder[EmailAddress] = deriveUnwrappedEncoder[EmailAddress]
    }

    case class ApplyViaJobBoard(firstName: FirstName, lastName: LastName, emailAddress: EmailAddress) extends ApplicationMethod

    object ApplyViaJobBoard {
      implicit lazy val decoder: Decoder[ApplyViaJobBoard] = deriveDecoder[ApplyViaJobBoard]
      implicit lazy val encoder: Encoder[ApplyViaJobBoard] = deriveEncoder[ApplyViaJobBoard]
    }

    case class ApplyViaExternalWebsite(url: Website) extends ApplicationMethod

    object ApplyViaExternalWebsite {
      implicit lazy val decoder: Decoder[ApplyViaExternalWebsite] = deriveDecoder[ApplyViaExternalWebsite]
      implicit lazy val encoder: Encoder[ApplyViaExternalWebsite] = deriveEncoder[ApplyViaExternalWebsite]
    }

    type EasyApply = EasyApply.type

    case object EasyApply extends ApplicationMethod {
      implicit lazy val decoder: Decoder[EasyApply] = deriveDecoder[EasyApply]
      implicit lazy val encoder: Encoder[EasyApply] = deriveEncoder[EasyApply]
    }

    implicit lazy val decoder: Decoder[ApplicationMethod] = deriveDecoder[ApplicationMethod]
    implicit lazy val encoder: Encoder[ApplicationMethod] = deriveEncoder[ApplicationMethod]
  }

  case class Logo(url: String) extends AnyVal

  object Logo {
    implicit lazy val decoder: Decoder[Logo] = deriveUnwrappedDecoder[Logo]
    implicit lazy val encoder: Encoder[Logo] = deriveUnwrappedEncoder[Logo]
  }

  case class Video(url: String) extends AnyVal

  object Video {
    implicit lazy val decoder: Decoder[Video] = deriveUnwrappedDecoder[Video]
    implicit lazy val encoder: Encoder[Video] = deriveUnwrappedEncoder[Video]
  }

  case class CompanyName(name: String) extends AnyVal

  object CompanyName {
    implicit lazy val decoder: Decoder[CompanyName] = deriveUnwrappedDecoder[CompanyName]
    implicit lazy val encoder: Encoder[CompanyName] = deriveUnwrappedEncoder[CompanyName]
  }

  sealed trait Address

  object Address {
    case class Street(name: String) extends AnyVal

    object Street {
      implicit lazy val decoder: Decoder[Street] = deriveUnwrappedDecoder[Street]
      implicit lazy val encoder: Encoder[Street] = deriveUnwrappedEncoder[Street]
    }
    case class HouseNumber(value: String) extends AnyVal

    object HouseNumber {
      implicit lazy val decoder: Decoder[HouseNumber] = deriveUnwrappedDecoder[HouseNumber]
      implicit lazy val encoder: Encoder[HouseNumber] = deriveUnwrappedEncoder[HouseNumber]
    }
    case class HouseNumberAddition(value: String) extends AnyVal

    object HouseNumberAddition {
      implicit lazy val decoder: Decoder[HouseNumberAddition] = deriveUnwrappedDecoder[HouseNumberAddition]
      implicit lazy val encoder: Encoder[HouseNumberAddition] = deriveUnwrappedEncoder[HouseNumberAddition]
    }
    case class RawAddress(value: String) extends AnyVal

    object RawAddress {
      implicit lazy val decoder: Decoder[RawAddress] = deriveUnwrappedDecoder[RawAddress]
      implicit lazy val encoder: Encoder[RawAddress] = deriveUnwrappedEncoder[RawAddress]
    }
    case class Country(code: String) extends AnyVal

    object Country {
      implicit lazy val decoder: Decoder[Country] = deriveUnwrappedDecoder[Country]
      implicit lazy val encoder: Encoder[Country] = deriveUnwrappedEncoder[Country]
    }
    case class City(name: String) extends AnyVal

    object City {
      implicit lazy val decoder: Decoder[City] = deriveUnwrappedDecoder[City]
      implicit lazy val encoder: Encoder[City] = deriveUnwrappedEncoder[City]
    }
    case class StreetNameAndHouseNumber(value: String) extends AnyVal

    object StreetNameAndHouseNumber {
      implicit lazy val decoder: Decoder[StreetNameAndHouseNumber] = deriveUnwrappedDecoder[StreetNameAndHouseNumber]
      implicit lazy val encoder: Encoder[StreetNameAndHouseNumber] = deriveUnwrappedEncoder[StreetNameAndHouseNumber]
    }

    case class PostalAddress(streetNameAndHouseNumber: StreetNameAndHouseNumber, city: City, zipCode: ZipCode) extends Address

    object PostalAddress {
      implicit lazy val decoder: Decoder[PostalAddress] = deriveDecoder[PostalAddress]
      implicit lazy val encoder: Encoder[PostalAddress] = deriveEncoder[PostalAddress]
    }

    case class DomesticAddress(
      street: Street,
      houseNumber: HouseNumber,
      houseNumberAddition: HouseNumberAddition,
      zipCode: ZipCode,
      city: City
    ) extends Address

    object DomesticAddress {
      implicit lazy val decoder: Decoder[DomesticAddress] = deriveDecoder[DomesticAddress]
      implicit lazy val encoder: Encoder[DomesticAddress] = deriveEncoder[DomesticAddress]
    }

    case class ForeignAddress(address: RawAddress, country: Country) extends Address

    object ForeignAddress {
      implicit lazy val decoder: Decoder[ForeignAddress] = deriveDecoder[ForeignAddress]
      implicit lazy val encoder: Encoder[ForeignAddress] = deriveEncoder[ForeignAddress]
    }

    implicit lazy val decoder: Decoder[Address] = deriveDecoder[Address]
    implicit lazy val encoder: Encoder[Address] = deriveEncoder[Address]
  }

  case class CompanyType(name: String) extends AnyVal

  object CompanyType {
    implicit lazy val decoder: Decoder[CompanyType] = deriveUnwrappedDecoder[CompanyType]
    implicit lazy val encoder: Encoder[CompanyType] = deriveUnwrappedEncoder[CompanyType]
  }

  case class Website(url: String) extends AnyVal

  object Website {
    implicit lazy val decoder: Decoder[Website] = deriveUnwrappedDecoder[Website]
    implicit lazy val encoder: Encoder[Website] = deriveUnwrappedEncoder[Website]
  }

  case class Company(name: CompanyName, address: Address, companyType: CompanyType, website: Option[Website])

  object Company {
    implicit lazy val decoder: Decoder[Company] = deriveDecoder[Company]
    implicit lazy val encoder: Encoder[Company] = deriveEncoder[Company]
  }

  case class Site(name: String) extends AnyVal

  object Site {
    implicit lazy val decoder: Decoder[Site] = deriveUnwrappedDecoder[Site]
    implicit lazy val encoder: Encoder[Site] = deriveUnwrappedEncoder[Site]

    implicit class SiteOps(site: Site) {

      def alternativeName: String = site match {
        case Site("Nationale Vacaturebank") => "nationalevacaturebank"
        case Site("Intermediair")           => "intermediair"
        case Site("Tweakers Carrière")      => "itbanen"
      }
    }
  }

  case class Feature(name: String) extends AnyVal

  object Feature {
    implicit lazy val decoder: Decoder[Feature] = deriveUnwrappedDecoder[Feature]
    implicit lazy val encoder: Encoder[Feature] = deriveUnwrappedEncoder[Feature]
  }

  case class Budget(amount: Int) extends AnyVal

  object Budget {
    implicit lazy val decoder: Decoder[Budget] = deriveUnwrappedDecoder[Budget]
    implicit lazy val encoder: Encoder[Budget] = deriveUnwrappedEncoder[Budget]
  }

  sealed trait Configuration

  object Configuration {
    implicit lazy val encoder: Encoder[Configuration] = deriveEncoder
    implicit lazy val decoder: Decoder[Configuration] = deriveDecoder
  }

  case class JobPosting(
    publishOn: Set[Site],
    publicationDuration: Duration,
    features: Set[Feature] = Set()
  ) extends Configuration

  object JobPosting {
    implicit lazy val durationEncoder: Encoder[Duration] = Encoder.encodeLong.contramap(_.toDays)
    implicit lazy val durationDecoder: Decoder[Duration] = Decoder.decodeLong.map(_.days)

    implicit lazy val jobPostingEncoder: Encoder[JobPosting] = deriveEncoder[JobPosting]
    implicit lazy val jobPostingDecoder: Decoder[JobPosting] = deriveDecoder[JobPosting]
  }

  case class PerformanceBased(budget: Budget) extends Configuration

  object PerformanceBased {
    implicit lazy val performanceBasedEncoder: Encoder[PerformanceBased] = deriveEncoder[PerformanceBased]
    implicit lazy val performanceBasedDecoder: Decoder[PerformanceBased] = deriveDecoder[PerformanceBased]
  }

  case class RecruiterId(id: String) extends AnyVal

  object RecruiterId {
    implicit lazy val meta: Meta[RecruiterId] = Meta[String].imap(RecruiterId.apply)(_.id)
  }

  implicit lazy val encoder: Encoder[Defaults] = deriveEncoder[Defaults]
  implicit lazy val decoder: Decoder[Defaults] = deriveDecoder[Defaults]

  val Empty: Defaults = defaultsDecoder("{}")

  def defaultsDecoder(input: String): Defaults = parser.decode[Defaults](input) match {
    // During decoding we must add logic to upcast older versions
    // to json: String -> Json; upcast: Json -> Json; to defaults: Json -> Defaults
    case Left(error)     => throw error
    case Right(defaults) => defaults
  }

  implicit lazy val encodeDefaults: MappedEncoding[Defaults, String] =
    MappedEncoding[Defaults, String](_.asJson.deepDropNullValues.noSpaces)
  implicit lazy val decodeDefaults: MappedEncoding[String, Defaults] = MappedEncoding[String, Defaults](defaultsDecoder)

  // During decoding we must add logic to upcast older versions
  // to json: String -> Json; upcast: Json -> Json; to defaults: Json -> Defaults
  implicit lazy val meta: Meta[Defaults] = Meta[String].imap(defaultsDecoder)(_.asJson.noSpaces)
}
