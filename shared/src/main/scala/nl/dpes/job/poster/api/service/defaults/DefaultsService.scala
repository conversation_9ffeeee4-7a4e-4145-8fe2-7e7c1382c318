package nl.dpes.job.poster.api.service.defaults

import cats.MonadThrow
import nl.dpes.job.poster.api.service.defaults.Defaults.RecruiterId

trait DefaultsService[F[_]] {
  def getDefaults(recruiterId: RecruiterId): F[Defaults]
  def saveDefaults(recruiterId: RecruiterId, defaults: Defaults): F[Unit]
}

object DefaultsService {

  def apply[F[_]: MonadThrow](repository: DefaultsRepository[F]): DefaultsService[F] = new DefaultsService[F] {

    override def getDefaults(recruiterId: RecruiterId): F[Defaults] =
      repository.readDefaults(recruiterId)

    override def saveDefaults(recruiterId: RecruiterId, defaults: Defaults): F[Unit] =
      repository.storeDefaults(recruiterId, defaults)
  }
}
