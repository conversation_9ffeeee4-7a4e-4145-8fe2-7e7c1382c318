package nl.dpes.job.poster.api.service.defaults

import cats.effect.{Async, Resource}
import cats.syntax.functor._
import doobie.implicits._
import doobie.util.transactor.Transactor
import nl.dpes.job.poster.api.database.TableName
import nl.dpes.job.poster.api.service.defaults.Defaults.RecruiterId

/** NOTE THAT CHANGES IN THE MODEL FOR THIS REPOSITORY SHOULD ALSO BE IMPLEMENEDED IN THE API DEFAULTS
  */
trait DefaultsRepository[F[_]] {
  def storeDefaults(recruiterId: RecruiterId, defaults: Defaults): F[Unit]
  def readDefaults(recruiterId: RecruiterId): F[Defaults]
}

object DefaultsRepository {

  def apply[F[_]: Async: Transactor](name: TableName): Resource[F, DefaultsRepository[F]] = {
    val xa = implicitly[Transactor[F]]

    def initialize: F[Unit] =
      sql"""
         create table if not exists ${name.frag}
         (
             recruiter_id varchar(18)  not null
                 primary key,
             version      varchar(255) not null,
             defaults     json         not null
         )
             engine = InnoDB
             charset = utf8mb3;
         """.update.run.void.transact(xa)

    def repository: DefaultsRepository[F] = new DefaultsRepository[F] {
      override def storeDefaults(recruiterId: RecruiterId, defaults: Defaults): F[Unit] =
        sql"""
             insert into ${name.frag} (recruiter_id, version, defaults)
             values ($recruiterId, ${Defaults.version}, $defaults)
             on duplicate key update version = ${Defaults.version}, defaults = $defaults
           """.update.run.void.transact(xa)

      override def readDefaults(recruiterId: RecruiterId): F[Defaults] =
        sql"""
             select defaults
             from ${name.frag}
             where recruiter_id = $recruiterId
           """.query[Defaults].option.map(_ getOrElse Defaults.empty).transact(xa)
    }

    Resource.eval(initialize as repository)
  }
}
