package nl.dpes.job.poster.api.tracing

import cats._
import cats.effect._
import cats.implicits._
import io.circe._
import io.circe.generic.semiauto._
import io.circe.syntax._
import nl.dpes.job.poster.api.tracing.RequestTimer._
import org.typelevel.log4cats._

case class RequestTimer[F[_]: LoggerFactory: Temporal: MonadThrow](clientName: ClientName) {

  def log[A](fa: => F[A]): F[A] =
    for {
      start           <- Temporal[F].monotonic
      attemptedResult <- fa.attempt
      end             <- Temporal[F].monotonic
      jsonMessage     <- MonadThrow[F].pure(LogMessage(clientName, (end - start).toMillis).asJson.noSpaces)
      _               <- LoggerFactory[F].getLogger.info(jsonMessage)
      result          <- MonadThrow[F].fromEither(attemptedResult)
    } yield result
}

object RequestTimer {
  case class ClientName private (value: String) extends AnyVal

  object ClientName {
    val addressService   = new ClientName("address service")
    val jobManager       = new ClientName("job manager")
    val logoService      = new ClientName("logo service")
    val recruiterService = new ClientName("recruiter service")
    val vacancyEnricher  = new ClientName("vacancy enricher")

    implicit val encoder: Encoder[ClientName] = Encoder.encodeString.contramap(_.value)
    implicit val decoder: Decoder[ClientName] = Decoder.decodeString.map(ClientName(_))
  }

  case class LogMessage(clientName: ClientName, durationInMillis: Long)

  object LogMessage {
    implicit val encoder: Encoder[LogMessage] = deriveEncoder
    implicit val decoder: Decoder[LogMessage] = deriveDecoder
  }
}
