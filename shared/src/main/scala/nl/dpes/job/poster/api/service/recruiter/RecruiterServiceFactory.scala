package nl.dpes.job.poster.api.service.recruiter

import cats.effect.Resource
import cats.effect.kernel.Async
import nl.dpes.b2b.salesforce.service.GrpcRecruiterService
import org.typelevel.log4cats.LoggerFactory

trait RecruiterServiceFactory[F[_]] {
  def create(loggerFactory: LoggerFactory[F]): RecruiterService[F]
}

object RecruiterServiceFactory {

  def impl[F[_]: Async](connection: Resource[F, GrpcRecruiterService]): RecruiterServiceFactory[F] = new RecruiterServiceFactory[F] {

    override def create(loggerFactory: LoggerFactory[F]): RecruiterService[F] = {
      implicit lazy val implicitLoggerFactory: LoggerFactory[F] = loggerFactory
      RecruiterService[F](connection)
    }
  }

}
