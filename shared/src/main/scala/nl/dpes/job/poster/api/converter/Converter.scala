package nl.dpes.job.poster.api.converter

trait Converter[From, To] {
  def convert(from: From): To
  def invert(from: To): From
}

object Converter {

  implicit class ConvertorOps[From](value: From) {
    def toDomain[To](implicit converter: Converter[From, To]): To = converter.convert(value)
  }

  implicit class InvertorOps[From](value: From) {
    def toApi[To](implicit converter: Converter[To, From]): To = converter.invert(value)
  }

}
