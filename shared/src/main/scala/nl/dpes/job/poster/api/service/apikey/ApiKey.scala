package nl.dpes.job.poster.api.service.apikey

import cats.Applicative
import cats.effect.Sync
import cats.syntax.applicative._
import io.getquill.mirrorContextWithQueryProbing.MappedEncoding
import nl.dpes.job.poster.api.service.BearerToken

import java.util.UUID

case class Api<PERSON><PERSON>(key: String) extends AnyVal

object ApiKey {
  def apply[F[_]: Applicative](bearerToken: BearerToken): F[ApiKey] = new ApiKey(bearerToken.token).pure
  def generate[F[_]: Sync]: F[ApiKey]                               = Sync[F].delay(ApiKey(UUID.randomUUID().toString))

  implicit val dbEncodeApiKey: MappedEncoding[ApiKey, String] = MappedEncoding[ApiKey, String](_.key)
  implicit val dbDecodeApiKey: MappedEncoding[String, ApiKey] = MappedEncoding[String, ApiKey](<PERSON><PERSON><PERSON><PERSON>(_))
}
