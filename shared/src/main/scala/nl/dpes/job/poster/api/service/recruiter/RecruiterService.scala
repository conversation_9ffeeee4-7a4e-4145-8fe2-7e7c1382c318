package nl.dpes.job.poster.api.service.recruiter

import akka.actor.ActorSystem
import akka.grpc.GrpcClientSettings
import cats.data.EitherT
import cats.effect.{Async, Resource, Sync}
import cats.syntax.applicative._
import cats.syntax.applicativeError._
import cats.syntax.flatMap._
import cats.syntax.functor._
import nl.dpes.b2b.domain.GenericError
import nl.dpes.b2b.salesforce.domain.{Re<PERSON><PERSON>er, RecruiterToken, SalesForceId}
import nl.dpes.b2b.salesforce.service.GrpcRecruiterService
import nl.dpes.b2b.salesforce.v1.RecruiterServiceClient
import nl.dpes.job.poster.api.tracing.RequestTimer
import nl.dpes.job.poster.api.tracing.RequestTimer.ClientName._
import org.slf4j
import org.slf4j.Logger
import org.typelevel.log4cats.LoggerFactory

import scala.concurrent.duration.{DurationInt, SECONDS}
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

trait RecruiterService[F[_]] {
  def getRecruiter(bearerToken: AccessToken): F[Recruiter]
  def getRecruiter(recruiterId: SalesForceId): F[Recruiter]
}

object RecruiterService {
  case class RecruiterUnauthorized(message: String) extends Throwable(message)
  case class RecruiterForbidden(message: String)    extends Throwable(message)
  case class RecruiterServiceError(message: String) extends Throwable(message)

  implicit lazy val actorSystem: ActorSystem     = ActorSystem.create()
  implicit lazy val ec: ExecutionContextExecutor = ExecutionContext.global

  def apply[F[_]: Async: LoggerFactory](connection: Resource[F, GrpcRecruiterService]): RecruiterService[F] =
    new RecruiterService[F] {

      override def getRecruiter(recruiterId: SalesForceId): F[Recruiter] = for {
        logger <- LoggerFactory[F].fromClass(getClass)
        _      <- logger.info("Reading recruiter information with recruiter id")
        recruiter <- RequestTimer(recruiterService)
          .log(connection.use { service =>
            EitherT(Async[F].fromFuture(service.getRecruiterById(recruiterId).pure))
              .leftMap {
                case GenericError(401, message, cause)  => RecruiterUnauthorized(message)
                case GenericError(404, message, cause)  => RecruiterForbidden(message)
                case GenericError(code, message, cause) => RecruiterServiceError(s"something went wrong: '$message' ($code)")
                case _                                  => RecruiterServiceError("something went wrong")
              }
              .rethrowT
              .onError { case e => logger.error(s"Failed reading recruiter by id ${recruiterId.idWithChecksum}: ${e.getMessage}") }
          })
      } yield recruiter

      override def getRecruiter(accessToken: AccessToken): F[Recruiter] = for {
        logger <- LoggerFactory[F].fromClass(getClass)
        _      <- logger.info("Reading recruiter information with access token")
        recruiter <- RequestTimer(recruiterService)
          .log(connection.use { service =>
            EitherT(Async[F].fromFuture(service.getRecruiter(RecruiterToken(accessToken.token)).pure))
              .leftMap {
                case GenericError(401, message, cause)  => RecruiterUnauthorized(message)
                case GenericError(404, message, cause)  => RecruiterForbidden(message)
                case GenericError(code, message, cause) => RecruiterServiceError(s"something went wrong: '$message' ($code)")
                case _                                  => RecruiterServiceError("something went wrong")
              }
              .rethrowT
              .onError { case e => logger.error(s"Failed reading recruiter by access token: ${e.getMessage}") }
          })
      } yield recruiter
    }

  def connection[F[_]: Async](host: String, port: Int): Resource[F, GrpcRecruiterService] = for {
    client  <- client(host, port)
    service <- backend(client)
  } yield service

  def backend[F[_]: Sync](client: RecruiterServiceClient): Resource[F, GrpcRecruiterService] = {
    implicit val logger: Logger = slf4j.LoggerFactory.getLogger("GrpcRecruiterService")
    Resource.pure(new GrpcRecruiterService(client))
  }

  private def client[F[_]: Async](host: String, port: Int): Resource[F, RecruiterServiceClient] =
    Resource.make(
      Async[F].delay(
        RecruiterServiceClient(
          GrpcClientSettings
            .connectToServiceAt(host, port)
            .withTls(false)
            .withConnectionAttempts(3)
            .withResolveTimeout(5.seconds)
            .withOverrideAuthority(s"$host:$port")
        )
      )
    )(client => Async[F].delay(client.close()))
}
