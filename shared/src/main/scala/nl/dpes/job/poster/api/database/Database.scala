package nl.dpes.job.poster.api.database

import cats.effect._
import doobie.hikari.HikariTransactor
import doobie.ExecutionContexts

object Database {

  def asResource[F[_]: Async](config: DbConfig): Resource[F, HikariTransactor[F]] =
    for {
      ec <- ExecutionContexts.fixedThreadPool(config.threadCount.value)
      xa <- HikariTransactor.newHikariTransactor[F](
        "com.mysql.cj.jdbc.Driver",
        config.url.value,
        config.userName.value,
        config.userPassword.value,
        ec
      )
    } yield xa
}
