package nl.dpes.job.poster.api.service.apikey

import cats.effect.Sync
import cats.syntax.applicative._
import cats.syntax.applicativeError._
import cats.syntax.flatMap._
import cats.syntax.functor._
import io.getquill.{SnakeCase, SqliteJdbcContext}
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.service.apikey.ApiKeyRepository.RecruiterId
import nl.dpes.job.poster.api.service.recruiter._ // if marked as unused — do not remove!! — (it is used)

import java.sql.Timestamp
import java.time.Instant
import java.util.UUID

trait ApiKeyRepository[F[_]] {
  def storeKey(recruiterId: SalesForceId, key: ApiKey, integration: Integration): F[Unit]
  def removeKey(recruiterId: RecruiterId, keyId: ApiKeyId): F[Unit]
  def readRecruiterId(key: ApiKey): F[SalesForceId]
  def readRecruiterKeyInformation(recruiterId: RecruiterId): F[List[ApiKeyInformation]]
  def readRecruiterKeyInformation(recruiterId: RecruiterId, id: ApiKeyId): F[ApiKeyInformation]
}

object ApiKeyRepository {
  type RecruiterId = SalesForceId

  case object ApiKeyEntryNotFound extends Throwable(s"Entry not found by key")

  case class ApiKeyAlreadySet(recruiterId: SalesForceId)
      extends Throwable(s"Recruiter ${recruiterId.idWithChecksum} already has an API key")

  case class ApiKeyEntryNotFound(recruiterId: SalesForceId)
      extends Throwable(s"No API key found by recruiter ${recruiterId.idWithChecksum}")

  case class ApiKeyNotFound(recruiterId: SalesForceId, apiKeyId: ApiKeyId)
      extends Throwable(s"API key ${apiKeyId.value} for recruiter ${recruiterId.idWithChecksum} not found")

  case class ApiKeyEntry(
    id: ApiKeyId = ApiKeyId.generate,
    recruiterId: SalesForceId,
    apiKey: HashedApiKey,
    integration: Integration,
    createdAt: CreatedAt
  )

  object ApiKeyEntry {
    case class ID(value: String = UUID.randomUUID().toString) extends AnyVal
  }

  def impl[F[_]: Sync](keyHasher: ApiKeyHasher)(implicit context: SqliteJdbcContext[SnakeCase.type]): ApiKeyRepository[F] =
    new ApiKeyRepository[F] {
      import context._

      override def storeKey(recruiterId: SalesForceId, key: ApiKey, integration: Integration): F[Unit] = for {
        hashedKey <- Sync[F].delay(keyHasher.hash(key))
        _         <- insert(ApiKeyId.generate, recruiterId, hashedKey, integration)
      } yield ()

      override def readRecruiterId(key: ApiKey): F[SalesForceId] = for {
        hashedKey <- Sync[F].delay(keyHasher.hash(key))
        maybeId   <- selectRecruiter(hashedKey)
        key       <- maybeId.map(_.pure).getOrElse(ApiKeyEntryNotFound.raiseError)
      } yield key

      private def selectRecruiter(key: HashedApiKey): F[Option[SalesForceId]] = for {
//      _ <- println(s"Selecting recruiter by key.").pure
        result <- Sync[F].blocking(
          context
            .run(
              quote(
                query[ApiKeyEntry].filter(_.apiKey == lift(key))
              )
            )
            .headOption
            .map(_.recruiterId)
        )
      } yield result

      private def insert(apiKeyId: ApiKeyId, recruiterId: SalesForceId, key: HashedApiKey, integration: Integration): F[Unit] = for {
//      _ <- println(s"Inserting key for recruiter: '${recruiterId.idWithChecksum}'.").pure
        createdAt <- CreatedAt(Timestamp.from(Instant.now)).pure
        _ <- Sync[F].blocking(
          context.run(
            quote(
              query[ApiKeyEntry]
                .insertValue(ApiKeyEntry(lift(apiKeyId), lift(recruiterId), lift(key), lift(integration), lift(createdAt)))
            )
          )
        )
      } yield ()

      override def readRecruiterKeyInformation(recruiterId: RecruiterId): F[List[ApiKeyInformation]] = for {
        //      _ <- println(s"Updating key for recruiter: '${recruiterId.idWithChecksum}'.").pure
        records <- Sync[F].blocking(
          context.run(
            quote(
              query[ApiKeyEntry]
                .filter(_.recruiterId == lift(recruiterId))
                .sortBy(_.createdAt)
            )
          )
        )
      } yield records.map(r => ApiKeyInformation(r.id, r.integration, r.createdAt))

      override def readRecruiterKeyInformation(recruiterId: RecruiterId, id: ApiKeyId): F[ApiKeyInformation] =
        (for {
          //      _ <- println(s"Updating key for recruiter: '${recruiterId.idWithChecksum}'.").pure
          records <- Sync[F].blocking(
            context.run(
              quote(
                query[ApiKeyEntry]
                  .filter(e => e.recruiterId == lift(recruiterId) && e.id == lift(id))
                  .take(1)
              )
            )
          )
        } yield records.map(r => ApiKeyInformation(r.id, r.integration, r.createdAt)).headOption).flatMap {
          case Some(value) => value.pure
          case None        => ApiKeyNotFound(recruiterId, id).raiseError
        }

      override def removeKey(recruiterId: RecruiterId, keyId: ApiKeyId): F[Unit] = Sync[F]
        .blocking(
          context.run(
            quote(
              query[ApiKeyEntry].filter(entry => entry.recruiterId == lift(recruiterId) && entry.id == lift(keyId)).delete
            )
          )
        )
        .void
    }
}
