package nl.dpes.job.poster.api.service.apikey

import javax.crypto.SecretKeyFactory
import javax.crypto.spec.PBEKeySpec

case class ApiKeyHasher(salt: Salt) {
  private val strength: Int = 65536

  def hash(key: <PERSON>pi<PERSON>ey): HashedApiKey = {
    val spec    = new PBEKeySpec(key.key.toString.toCharArray, salt.value.toCharArray.map(_.toByte), strength, 128)
    val factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1")

    HashedApiKey(factory.generateSecret(spec).getEncoded.mkString)
  }
}
