package nl.dpes.job.poster.api.service.apikey

import cats.effect.IO
import weaver.SimpleIOSuite

object Api<PERSON>eyHasherTest extends SimpleIOSuite {
  test("Hashing a key always gives the same result") {
    for {
      apiKey <- IO(ApiKey("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
      first  <- IO(ApiKeyHasher(Salt("the salt")).hash(apiKey))
      second <- IO(ApiKeyHasher(Salt("the salt")).hash(apiKey))
    } yield expect(first == second) and
    expect(first.key == "2431159067-47715158-90-87-11940108120-77")
  }
}
