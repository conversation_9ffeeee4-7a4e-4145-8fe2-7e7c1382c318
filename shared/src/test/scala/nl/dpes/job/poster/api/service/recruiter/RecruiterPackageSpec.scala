package nl.dpes.job.poster.api.service.recruiter

import cats.effect.IO
import nl.dpes.b2b.salesforce.domain.SalesForceId
import weaver.SimpleIOSuite

object RecruiterPackageSpec extends SimpleIOSuite {
  test("salesforceid codec shoudl do round trip") {
    for {
      _  <- IO.unit
      id <- IO.fromEither(SalesForceId("123456789123445"))
    } yield expect(decodeSalesforceId.f(encodeSalesforceId.f(id)) == id)
  }
}
