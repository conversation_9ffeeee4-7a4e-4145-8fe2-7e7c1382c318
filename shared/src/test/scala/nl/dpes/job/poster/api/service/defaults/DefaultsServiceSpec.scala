package nl.dpes.job.poster.api.service.defaults

import cats.effect.IO
import cats.syntax.either._
import nl.dpes.job.poster.api.service.defaults.Defaults.RecruiterId
import org.mockito.MockitoSugar._
import weaver.SimpleIOSuite

object DefaultsServiceSpec extends SimpleIOSuite {

  case class Error(message: String) extends Throwable(message)

  val defaults: Defaults       = Defaults.defaultsDecoder("{}")
  val unit: Unit               = ()
  val recruiterId: RecruiterId = RecruiterId("RECRUITER_ID")

  test("It should be able to get the defaults") {
    for {
      defaultsRepository <- getMock
      _                  <- IO(when(defaultsRepository.readDefaults(recruiterId)).thenReturn(IO(defaults)))
      defaultsService    <- IO(DefaultsService(defaultsRepository))
      result             <- defaultsService.getDefaults(recruiterId)
    } yield expect(result == defaults)
  }

  test("It should return an error when the repository fails to get the defaults") {
    for {
      defaultsRepository <- getMock
      _                  <- IO(when(defaultsRepository.readDefaults(recruiterId)).thenReturn(IO.raiseError(Error("Error occurred"))))
      defaultsService    <- IO(DefaultsService(defaultsRepository))
      result             <- defaultsService.getDefaults(recruiterId).attempt
    } yield expect(result == Error("Error occurred").asLeft)
  }

  test("It should be able to save the defaults") {
    for {
      defaultsRepository <- getMock
      _                  <- IO(when(defaultsRepository.storeDefaults(recruiterId, defaults)).thenReturn(IO(unit)))
      defaultsService    <- IO(DefaultsService(defaultsRepository))
      result             <- defaultsService.saveDefaults(recruiterId, defaults)
    } yield expect(result == unit)
  }

  test("It should return an error when the repository fails to save the defaults") {
    for {
      defaultsRepository <- getMock
      _                  <- IO(when(defaultsRepository.storeDefaults(recruiterId, defaults)).thenReturn(IO.raiseError(Error("Error occurred"))))
      defaultsService    <- IO(DefaultsService(defaultsRepository))
      result             <- defaultsService.saveDefaults(recruiterId, defaults).attempt
    } yield expect(result == Error("Error occurred").asLeft)
  }

  def getMock: IO[DefaultsRepository[IO]] = IO(mock[DefaultsRepository[IO]])
}
