package nl.dpes.job.poster.api.service.apikey

import cats.effect.{IO, Resource}
import com.typesafe.config.{Config, ConfigFactory}
import io.getquill.{SnakeCase, SqliteJdbcContext}
import nl.dpes.b2b.salesforce.domain.SalesForceId
import nl.dpes.job.poster.api.service.apikey.ApiKeyRepository.{ApiKeyEntryNotFound, ApiKeyNotFound}
import org.testcontainers.containers.MySQLContainer
import weaver._

import scala.util.Random

object ApiKeyRepositorySpec extends IOSuite {
  override type Res = ApiKeyRepository[IO]

  val randomRecruiterId: IO[SalesForceId] = IO.delay(SalesForceId("0038" + Random.nextString(11))).rethrow
  val apiKey: IO[ApiKey]                  = ApiKey.generate[IO]
  val integration: IO[Integration]        = IO(Integration("Feed Manager"))

  val dbContainerResource: Resource[IO, MySQLContainer[Nothing]] = Resource.make {
    IO {
      val container: MySQLContainer[Nothing] = new MySQLContainer("mysql:8.0.35-debian").withInitScript("init.sql")
      container.start()
      container
    }
  }(container => IO(container.stop()))

  def configResource(container: MySQLContainer[Nothing]): Resource[IO, Config] = Resource.pure {
    ConfigFactory.parseString(s""" {
         |    dataSourceClassName = com.mysql.cj.jdbc.MysqlDataSource
         |    dataSource {
         |      url = "${container.getJdbcUrl}"
         |      user = "${container.getUsername}"
         |      password = "${container.getPassword}"
         |      cachePrepStmts = true
         |      prepStmtCacheSize = 250
         |      prepStmtCacheSqlLimit = 2048
         |    }
         |    maximumPoolSize = 10
         |  }""".stripMargin)
  }

  override def sharedResource: Resource[IO, this.Res] =
    for {
      container <- dbContainerResource
      config    <- configResource(container)
    } yield {
      implicit lazy val context: SqliteJdbcContext[SnakeCase.type] = new SqliteJdbcContext(SnakeCase, config)
      ApiKeyRepository.impl[IO](ApiKeyHasher(Salt("the salt")))
    }

  test("When an API key is created the associated recruiter id can be retrieved") { repo =>
    for {
      recruiterId          <- randomRecruiterId
      integration          <- integration
      apiKey               <- apiKey
      _                    <- repo.storeKey(recruiterId, apiKey, integration)
      retrievedRecruiterId <- repo.readRecruiterId(apiKey)
    } yield expect(recruiterId == retrievedRecruiterId)
  }

  test("When an API key has been deleted the recruiters id cannot be retrieved anymore") { repo =>
    for {
      recruiterId        <- randomRecruiterId
      integration        <- integration
      apiKey             <- apiKey
      _                  <- repo.storeKey(recruiterId, apiKey, integration)
      info               <- repo.readRecruiterKeyInformation(recruiterId)
      _                  <- repo.removeKey(recruiterId, info.head.id)
      infoAfterRemoval   <- repo.readRecruiterKeyInformation(recruiterId)
      deletedRecruiterId <- repo.readRecruiterId(apiKey).attempt
    } yield expect(infoAfterRemoval.isEmpty) and (deletedRecruiterId match {
      case Left(value) => expect(value == ApiKeyEntryNotFound)
      case Right(_)    => failure("Retrieving a recruiter id with a deleted API key should fail")
    })
  }

  test("When storing multiple API keys their info can be retrieved") { repo =>
    for {
      recruiterId <- randomRecruiterId
      integration <- integration
      apiKey1     <- apiKey
      _           <- repo.storeKey(recruiterId, apiKey1, integration)
      apiKey2     <- apiKey
      _           <- repo.storeKey(recruiterId, apiKey2, integration)
      apiKey3     <- apiKey
      _           <- repo.storeKey(recruiterId, apiKey3, integration)
      info        <- repo.readRecruiterKeyInformation(recruiterId)
    } yield expect(info.size == 3)
  }

  test("A single key can be retrieved, in the context of the recruiter that owns it") { repo =>
    for {
      recruiterId <- randomRecruiterId
      integration <- integration
      apiKey      <- apiKey
      _           <- repo.storeKey(recruiterId, apiKey, integration)
      allInfo     <- repo.readRecruiterKeyInformation(recruiterId)
      info        <- repo.readRecruiterKeyInformation(recruiterId, allInfo.head.id)
    } yield expect(info == ApiKeyInformation(allInfo.head.id, integration, allInfo.head.createdAt))
  }

  test("When retrieving key information in the context of another recruiter a failure is returned") { repo =>
    for {
      ownerRecruiterId <- randomRecruiterId
      otherRecruiterId <- randomRecruiterId
      integration      <- integration
      apiKey           <- apiKey
      _                <- repo.storeKey(ownerRecruiterId, apiKey, integration)
      allInfo          <- repo.readRecruiterKeyInformation(ownerRecruiterId)
      info             <- repo.readRecruiterKeyInformation(otherRecruiterId, allInfo.head.id).attempt
    } yield info match {
      case Left(error) => expect(error == ApiKeyNotFound(otherRecruiterId, allInfo.head.id))
      case Right(_)    => failure("Retrieving a recruiter id with a deleted API key should fail")
    }
  }
}
