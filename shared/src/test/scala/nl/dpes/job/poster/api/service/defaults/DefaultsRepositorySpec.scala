package nl.dpes.job.poster.api.service.defaults

import cats.effect._
import cats.implicits._
import doobie.implicits._
import nl.dpes.job.poster.api.database.TableName
import nl.dpes.job.poster.api.service.defaults.Defaults.{RecruiterId, Title}
import nl.dpes.job.poster.api.testcontainers.DatabaseGenerator
import weaver._

import java.sql.SQLSyntaxErrorException

object DefaultsRepositorySpec extends IOSuite with DatabaseGenerator {
  override type Res = doobie.util.transactor.Transactor[IO]

  override def sharedResource: Resource[IO, Res] = transactor

  def randomTableName: IO[TableName]     = IO.randomUUID.map(uuid => TableName(s"defaults_$uuid"))
  def randomRecruiterId: IO[RecruiterId] = IO.randomUUID.map(uuid => RecruiterId(s"recruiter-$uuid".take(18)))

  test("When the repo is not instantiated there should be no table") { xa =>
    for {
      tableName <- randomTableName
      result <- sql"""select "success" from ${tableName.frag};"""
        .query[String]
        .option
        .transact[IO](xa)
        .attempt
    } yield result match {
      case Left(value) => expect(value.getClass == classOf[SQLSyntaxErrorException])
      case Right(_)    => failure("This test should fail successfully")
    }
  }

  test("When the repo is instantiated there should be a table") { implicit xa =>
    for {
      tableName <- randomTableName
      result <- DefaultsRepository[IO](tableName).use(_ =>
        sql"""select "success" from ${tableName.frag};"""
          .query[String]
          .option
          .transact[IO](xa)
      )
    } yield expect(result.isEmpty)
  }

  test("instantiating the repo twice should not fail") { implicit xa =>
    for {
      tableName   <- randomTableName
      recruiterId <- randomRecruiterId
      _           <- DefaultsRepository[IO](tableName).use(_.storeDefaults(recruiterId, Defaults.empty))
      results     <- DefaultsRepository[IO](tableName).use(_.readDefaults(recruiterId))
    } yield expect(results == Defaults.empty)
  }

  test("storing defaults twice overwrites the old version") { implicit xa =>
    val firstDefaults  = Defaults.empty.copy(title = Title("1st version").some)
    val secondDefaults = Defaults.empty.copy(title = Title("2nd version").some)

    for {
      tableName      <- randomTableName
      recruiterId    <- randomRecruiterId
      _              <- DefaultsRepository[IO](tableName).use(_.storeDefaults(recruiterId, firstDefaults))
      originalResult <- DefaultsRepository[IO](tableName).use(_.readDefaults(recruiterId))
      _              <- DefaultsRepository[IO](tableName).use(_.storeDefaults(recruiterId, secondDefaults))
      finalResult    <- DefaultsRepository[IO](tableName).use(_.readDefaults(recruiterId))
    } yield expect(originalResult == firstDefaults) and expect(finalResult == secondDefaults)
  }

  test("reading defaults for a non-existing recruiter should return an empty defaults object") { implicit xa =>
    for {
      tableName   <- randomTableName
      recruiterId <- randomRecruiterId
      result      <- DefaultsRepository[IO](tableName).use(_.readDefaults(recruiterId))
    } yield expect(result == Defaults.empty)
  }
}
