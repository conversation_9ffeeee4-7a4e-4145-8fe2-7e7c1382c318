package nl.dpes.job.poster.api.service.apikey

import cats.effect.IO
import weaver.SimpleIOSuite

object Api<PERSON>eySpec extends SimpleIOSuite {
  test("Decoding an encoded apikey should be the apikey") {
    for {
      apiKey <- IO(ApiKey("7c5b5b44-db23-4b2e-9da5-7ce8867dc4be"))
    } yield {
      val roundtrip = ApiKey.dbEncodeApiKey.f andThen ApiKey.dbDecodeApiKey.f
      expect(roundtrip(apiKey) == apiKey)
    }
  }
}
