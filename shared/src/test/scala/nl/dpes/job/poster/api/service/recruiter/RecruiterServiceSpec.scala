package nl.dpes.job.poster.api.service.recruiter

import cats.effect.{IO, Resource}
import cats.syntax.either._
import nl.dpes.b2b.domain.GenericError
import nl.dpes.b2b.salesforce.domain.{Recruiter, RecruiterToken}
import nl.dpes.b2b.salesforce.service.GrpcRecruiterService
import nl.dpes.job.poster.api.service
import org.mockito.MockitoSugar._
import org.typelevel.log4cats.slf4j.loggerFactoryforSync
import weaver._

import scala.concurrent.Future

object RecruiterServiceSpec extends SimpleIOSuite {
  val accessToken: AccessToken       = AccessToken("sample_access_token")
  val recruiterToken: RecruiterToken = RecruiterToken(accessToken.token)
  val recruiter: Recruiter           = mock[Recruiter]

  test("getRecruiter should return a Recruiter") {
    for {
      client <- IO(mock[GrpcRecruiterService])
      _ <- IO(
        when(client.getRecruiter(recruiterToken))
          .thenAnswer(Future.successful(Right(recruiter)))
      )

      connection <- IO(Resource.pure[IO, GrpcRecruiterService](client))
      service    <- IO(service.recruiter.RecruiterService[IO](connection))
      result     <- service.getRecruiter(accessToken).attempt
    } yield expect(result == Right(recruiter))
  }

  test("getRecruiter should raise a RecruiterUnauthorized error on 401") {
    for {
      client <- IO(mock[GrpcRecruiterService])
      _ <- IO(
        when(client.getRecruiter(recruiterToken))
          .thenAnswer(Future.successful(GenericError(401, "unauthorized").asLeft))
      )

      connection <- IO(Resource.pure[IO, GrpcRecruiterService](client))
      service    <- IO(service.recruiter.RecruiterService[IO](connection))
      result     <- service.getRecruiter(accessToken).attempt
    } yield expect(result == RecruiterService.RecruiterUnauthorized("unauthorized").asLeft)
  }

  test("getRecruiter should raise a RecruiterForbidden error on 404") {
    for {
      client <- IO(mock[GrpcRecruiterService])
      _ <- IO(
        when(client.getRecruiter(recruiterToken))
          .thenAnswer(Future.successful(GenericError(404, "not found").asLeft))
      )

      connection <- IO(Resource.pure[IO, GrpcRecruiterService](client))
      service    <- IO(service.recruiter.RecruiterService[IO](connection))
      result     <- service.getRecruiter(accessToken).attempt
    } yield expect(result == RecruiterService.RecruiterForbidden("not found").asLeft)
  }

  test("getRecruiter should raise a RecruiterServiceError error on 500") {
    for {
      client <- IO(mock[GrpcRecruiterService])
      _ <- IO(
        when(client.getRecruiter(recruiterToken))
          .thenAnswer(Future.successful(GenericError(500, "internal error").asLeft))
      )

      connection <- IO(Resource.pure[IO, GrpcRecruiterService](client))
      service    <- IO(service.recruiter.RecruiterService[IO](connection))
      result     <- service.getRecruiter(accessToken).attempt
    } yield expect(result == RecruiterService.RecruiterServiceError("something went wrong: 'internal error' (500)").asLeft)
  }

  test("getRecruiter should raise a RecruiterServiceError error when the response is unexpected") {
    for {
      client <- IO(mock[GrpcRecruiterService])
      _ <- IO(
        when(client.getRecruiter(recruiterToken))
          .thenReturn(Future.successful(GenericError(418, "I'm a teapot").asLeft))
      )

      connection <- IO(Resource.pure[IO, GrpcRecruiterService](client))
      service    <- IO(service.recruiter.RecruiterService[IO](connection))
      result     <- service.getRecruiter(accessToken).attempt
    } yield expect(result == RecruiterService.RecruiterServiceError("something went wrong: 'I'm a teapot' (418)").asLeft)
  }

  test("RecruiterService should create grpc client and service") {
    val host = "localhost"
    val port = 12345

    for {
      service <- RecruiterService.connection[IO](host, port).use(IO.pure)
    } yield expect(service.isInstanceOf[GrpcRecruiterService])
  }
}
