package nl.dpes.job.poster.api.tracing

import cats.effect.IO
import io.circe.parser._
import io.circe.syntax._
import nl.dpes.job.poster.api.tracing.RequestTimer.{ClientName, LogMessage}
import org.mockito.ArgumentMatchersSugar._
import org.mockito.MockitoSugar.{mock, when}
import org.mockito.{ArgumentCaptor, Mockito}
import org.typelevel.log4cats._
import org.typelevel.log4cats.slf4j.Slf4jFactory

object RequestTimerSpec extends weaver.SimpleIOSuite {
  implicit lazy val loggerFactory: LoggerFactory[IO] = Slf4jFactory.create[IO]

  test("when the thunk returns a result, the timer will return that result") {

    val timer          = new RequestTimer[IO](ClientName.recruiterService)
    val thunk: IO[Int] = IO.pure(42)

    timer.log(thunk).attempt.map {
      case Left(_)      => failure("Expected the result to be 42")
      case Right(value) => expect(value == 42)
    }
  }

  test("when the thunk fails, the timer will return that failure") {

    val timer          = new RequestTimer[IO](ClientName.jobManager)
    val thunk: IO[Int] = IO.raiseError(new Exception("Boom"))

    timer.log(thunk).attempt.map {
      case Left(error) => expect(error.getMessage == "Boom")
      case Right(_)    => failure("Expected the thunk to fail")
    }
  }

  class MockedLoggerFactory extends LoggerFactory[IO] {
    val mockedLogger: SelfAwareStructuredLogger[IO] = mock[SelfAwareStructuredLogger[IO]]
    when(mockedLogger.info(any[String])).thenReturn(IO.unit)

    override def getLoggerFromName(name: String): LoggerType = mockedLogger
    override def fromName(name: String): IO[LoggerType]      = IO(getLoggerFromName(name))

    def extractLogMessage: IO[LogMessage] = {
      val infoMessageCaptor: ArgumentCaptor[String] = ArgumentCaptor.forClass(classOf[String])
      Mockito.verify(mockedLogger).info(infoMessageCaptor.capture())
      IO.fromEither(parse(infoMessageCaptor.getValue).flatMap(_.asJson.as[LogMessage]))
    }
  }

  test("When the call fails the message is logged") {
    val thunk: IO[Int] = IO.raiseError(new Exception("Boom"))

    implicit val loggerFactory: MockedLoggerFactory = new MockedLoggerFactory

    val timer = new RequestTimer[IO](ClientName.logoService)

    for {
      result     <- timer.log(thunk).attempt
      logMessage <- loggerFactory.extractLogMessage
    } yield result match {
      case Left(_)  => expect(logMessage.clientName == ClientName.logoService)
      case Right(_) => failure(s"The call should fail")
    }
  }

  test("When the call succeeds the message is logged") {
    val thunk: IO[Int] = IO.pure(42)

    implicit val loggerFactory: MockedLoggerFactory = new MockedLoggerFactory

    val timer = new RequestTimer[IO](ClientName.logoService)

    for {
      result     <- timer.log(thunk).attempt
      logMessage <- loggerFactory.extractLogMessage
    } yield result match {
      case Left(value) => failure(s"The call should not fail, but failed with: '${value.getMessage}'")
      case Right(_)    => expect(logMessage.clientName == ClientName.logoService)
    }
  }
}
