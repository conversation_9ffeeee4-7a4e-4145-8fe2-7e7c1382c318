package nl.dpes.job.poster.api.service.defaults

import cats.effect._
import weaver._

object DefaultsSpec extends SimpleIOSuite {

  test("Old data can pe parsed") { xa =>
    import scala.io.Source

    val source = Resource.make(IO(Source.fromURL(getClass.getResource("/oldData.json"))))(source => IO(source.close))
    for {
      json <- source.use(source => IO(source.mkString))
    } yield {
      val defaults = io.circe.parser.decode[List[Defaults]](json)
      expect(defaults.isRight)
    }
  }
}
