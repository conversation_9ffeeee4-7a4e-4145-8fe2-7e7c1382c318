[{"company": {"name": "DPG Recruitment Test Account", "address": {"PostalAddress": {"city": "Amsterdam", "zipCode": "1033SN", "streetNameAndHouseNumber": "Streetstraat, 1"}}, "website": "https://www.google.com", "companyType": "<PERSON><PERSON><PERSON>"}, "configuration": {"JobPosting": {"features": ["highlight", "logo", "spotlight"], "publishOn": ["Nationale Vacaturebank"], "publicationDuration": 60}}}, {"company": {"name": "DPG Media NDP", "address": {"PostalAddress": {"city": "Amsterdam", "zipCode": "1018 LL", "streetNameAndHouseNumber": "mt. Lincolnweg 40"}}, "website": "https://www.dpgmedia.nl", "companyType": "<PERSON><PERSON><PERSON>"}, "location": "1018LL", "workplace": "<PERSON>e", "careerLevel": "<PERSON><PERSON><PERSON>", "workingHours": {"lower": 32, "upper": 40}, "configuration": {"JobPosting": {"features": ["logo"], "publishOn": ["Intermediair"], "publicationDuration": 60}}, "contractTypes": ["Interim", "Tijdelijk", "Vast"], "jobCategories": ["Automatisering/Internet"], "educationLevels": ["HBO", "WO"], "applicationMethod": {"ApplyViaExternalWebsite": {"url": "https://vacatures.dpgmedia.nl"}}, "industryCategories": ["ICT", "Internet"]}, {"logo": null, "title": null, "video": null, "salary": null, "company": null, "location": null, "occupation": "qweqwe", "careerLevel": null, "description": null, "contractType": null, "workingHours": null, "configuration": null, "jobCategories": null, "educationLevel": null, "applicationMethod": null, "industryCategories": null}, {"careerLevel": "<PERSON><PERSON><PERSON>", "contractTypes": ["Vast", "Tijdelijk"], "jobCategories": ["Commercieel/Verkoop"], "educationLevels": ["VMBO/MAVO"], "applicationMethod": {"ApplyViaExternalWebsite": {"url": "https://test.nl"}}, "industryCategories": ["Detailhandel", "Handel/G<PERSON>el"]}, {"salary": {"lower": 10000, "upper": 30000, "period": "Month"}, "company": {"name": "ACME corp", "address": {"PostalAddress": {"city": "Amsterdam", "zipCode": "1033SN", "streetNameAndHouseNumber": "mt Lincolnweg 40"}}, "website": "https://www.nationalevacaturebank.nl/", "companyType": "Direct employer"}, "location": "1018LL", "workplace": "Remote", "occupation": "Developer", "careerLevel": "Starter", "description": "<p>The description</p>\n<p>To describe the duties of the defaults.</p>", "workingHours": {"lower": 10, "upper": 30}, "configuration": {"JobPosting": {"features": ["logo"], "publishOn": ["Nationale Vacaturebank", "Intermediair"], "publicationDuration": 60}}, "contractTypes": ["Interim", "Stage", "Tijdelijk"], "jobCategories": ["Administratief/Secretarieel", "Automatisering/Internet"], "educationLevels": ["HBO", "WO", "HAVO"], "applicationMethod": {"ApplyViaJobBoard": {"lastName": "<PERSON><PERSON>", "firstName": "<PERSON>", "emailAddress": "<EMAIL>"}}, "industryCategories": ["Telecom", "Techniek"]}, {"occupation": "President"}, {"occupation": "test345", "careerLevel": "<PERSON>ie", "workingHours": {"lower": 1, "upper": 1}, "configuration": {"JobPosting": {"features": ["logo"], "publishOn": ["Intermediair"], "publicationDuration": 60}}, "educationLevels": ["Lagere school", "Postdoctoraal"]}, {"location": "1000AA", "occupation": "Default Test 3-15", "careerLevel": "<PERSON>ie", "workingHours": {"lower": 1000, "upper": 2000}, "contractTypes": ["Interim", "Thuiswerk"], "jobCategories": ["Administratief/Secretarieel"], "educationLevels": ["LBO", "VMBO/MAVO"], "industryCategories": ["Accountancy"]}, {"company": {"name": "DPG Media 2002", "address": {"PostalAddress": {"city": "Amsterdam", "zipCode": "1114 AM", "streetNameAndHouseNumber": "<PERSON> 40"}}, "website": "https://vacatures.dpgmedia.nl", "companyType": "<PERSON><PERSON><PERSON>"}, "location": "1018LL", "workplace": "<PERSON>e", "careerLevel": "<PERSON><PERSON><PERSON>", "workingHours": {"lower": 38, "upper": 40}, "configuration": {"JobPosting": {"features": ["logo"], "publishOn": ["Nationale Vacaturebank"], "publicationDuration": 60}}, "contractTypes": ["Vast", "Tijdelijk"], "jobCategories": ["Automatisering/Internet"], "educationLevels": ["HBO"], "applicationMethod": {"ApplyViaExternalWebsite": {"url": "https://vacatures.dpgmedia.nl"}}, "industryCategories": ["ICT", "Media/Uitgeverijen/TV"]}, {"company": {"name": "dPES QA - Test Account", "address": {"PostalAddress": {"city": "Amsterdam", "zipCode": "1018 LL", "streetNameAndHouseNumber": "<PERSON> 19 Etage 5"}}, "website": "https://vacatures.dprmedia.nl", "companyType": "Direct employer"}, "location": "1018LL", "workplace": "<PERSON>e", "careerLevel": "<PERSON><PERSON><PERSON>", "workingHours": {"lower": 38, "upper": 40}, "configuration": {"JobPosting": {"features": ["highlight", "logo", "spotlight"], "publishOn": ["Nationale Vacaturebank"], "publicationDuration": 60}}, "contractTypes": ["Vast", "Tijdelijk"], "jobCategories": ["Automatisering/Internet"], "educationLevels": ["HBO"], "applicationMethod": {"ApplyViaJobBoard": {"lastName": "HRM", "firstName": "Afd.", "emailAddress": "<EMAIL>"}}, "industryCategories": ["ICT", "Internet"]}, {"logo": null, "title": "The default title of all the jobs", "video": null, "salary": null, "company": null, "location": null, "occupation": null, "careerLevel": null, "description": null, "contractType": null, "workingHours": null, "configuration": null, "jobCategories": null, "educationLevel": null, "applicationMethod": null, "industryCategories": null}, {"location": "1111LL", "occupation": "123", "careerLevel": "<PERSON>ie", "contractType": "Leer-we<PERSON> overeen<PERSON>", "workingHours": {"lower": 1, "upper": 1}, "jobCategories": ["Beleid/Bestuur/Staf"], "educationLevels": ["Postdoctoraal"], "industryCategories": ["Afval en milieu"]}]