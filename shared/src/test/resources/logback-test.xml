<configuration>
    <!-- Prevent logback from logging its own status -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <root level="OFF">
        <appender-ref ref="STDOUT" />
    </root>
    
    <!-- Silence SLF4J replay warnings -->
    <logger name="org.slf4j" level="OFF"/>
    
    <!-- Silence Testcontainers logs -->
    <logger name="org.testcontainers" level="OFF"/>
    <logger name="tc" level="OFF"/>
    <logger name="com.github.dockerjava" level="OFF"/>
    <logger name="org.testcontainers.ext" level="OFF"/>
    
    <!-- Silence database connection logs -->
    <logger name="com.zaxxer.hikari" level="OFF"/>
    
    <!-- Silence B2B domain logs -->
    <logger name="nl.dpes" level="OFF"/>
    <logger name="B2B-DOMAIN" level="OFF"/>
</configuration>
